import unittest
import requests
import os
import sys
import django
from dotenv import load_dotenv


# Now we can import Django models
from users.models import Student
from rest_framework.authtoken.models import Token

# Load environment variables
load_dotenv()

# Get FastAPI URL from environment variables or use default
FASTAPI_URL = os.getenv("FASTAPI_URL", "http://localhost:8001")
# Get Django URL from environment variables or use default
DJANGO_URL = os.getenv("DJANGO_URL", "http://localhost:8000")

class TestDjangoFastAPIAuth(unittest.TestCase):
    """Test authentication between Django and FastAPI servers"""

    def setUp(self):
        """Set up test data"""
        # Test user data
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testauthuser',
            'password': 'testpass123',
        }

        # Create a test user if it doesn't exist
        try:
            self.user = Student.objects.get(email=self.user_data['email'])
        except Student.DoesNotExist:
            self.user = Student.objects.create_user(
                email=self.user_data['email'],
                username=self.user_data['username'],
                password=self.user_data['password'],
                is_active=True,
                is_email_verified=True
            )

        # Get or create token for the user
        self.token, _ = Token.objects.get_or_create(user=self.user)
        self.token_key = self.token.key

        # Django auth/test endpoint
        self.django_auth_test_url = f"{DJANGO_URL}/api/users/auth/test/"
        # FastAPI test-auth endpoint
        self.fastapi_test_auth_url = f"{FASTAPI_URL}/test-auth"

    def test_django_auth_with_token(self):
        """Test authentication with Token format to Django auth/test endpoint"""
        # Make request to Django auth/test endpoint with Token format
        response = requests.get(
            self.django_auth_test_url,
            headers={"Authorization": f"Token {self.token_key}"},
        )

        # Assert response is successful
        self.assertEqual(response.status_code, 200)

        # Print response data for debugging
        print(f"Response from Django auth/test endpoint: {response.status_code}")
        print(f"Response content: {response.text[:200]}...")  # Print first 200 chars

        # Assert response contains expected data
        data = response.json()
        self.assertTrue('auth_info' in data)
        self.assertTrue('is_authenticated' in data['auth_info'])
        self.assertTrue(data['auth_info']['is_authenticated'])

        print(f"✅ Authentication with Token to Django auth/test endpoint successful")

    def test_django_auth_with_bearer_token(self):
        """Test authentication with Bearer token format to Django auth/test endpoint"""
        # Make request to Django auth/test endpoint with Bearer token
        response = requests.get(
            self.django_auth_test_url,
            headers={"Authorization": f"Bearer {self.token_key}"},
        )

        # Assert response is successful
        self.assertEqual(response.status_code, 200)

        # Print response data for debugging
        print(f"Response from Django auth/test endpoint with Bearer token: {response.status_code}")
        print(f"Response content: {response.text[:200]}...")  # Print first 200 chars

        # Assert response contains expected data
        data = response.json()
        self.assertTrue('auth_info' in data)
        self.assertTrue('is_authenticated' in data['auth_info'])
        self.assertTrue(data['auth_info']['is_authenticated'])

        print(f"✅ Authentication with Bearer token to Django auth/test endpoint successful")

#     def test_fastapi_auth_with_token(self):
#         """Test authentication with Token format to FastAPI test-auth endpoint"""
#         # Make request to FastAPI test-auth endpoint with Token format
#         response = requests.get(
#             self.fastapi_test_auth_url,
#             headers={"Authorization": f"Token {self.token_key}"},
#             timeout=5.0
#         )

#         # Assert response is successful
#         self.assertEqual(response.status_code, 200)

#         # Print response data for debugging
#         print(f"Response from FastAPI test-auth endpoint: {response.status_code}")
#         print(f"Response content: {response.text[:200]}...")  # Print first 200 chars

#         # Assert response contains expected data
#         data = response.json()
#         self.assertEqual(data['status'], 'success')
#         self.assertEqual(data['message'], 'Authentication successful')
#         self.assertEqual(data['user_info']['id'], self.user.id)
#         self.assertEqual(data['user_info']['username'], self.user.username)
#         self.assertEqual(data['user_info']['email'], self.user.email)

#         print(f"✅ Authentication with Token to FastAPI test-auth endpoint successful")

    def test_fastapi_auth_with_bearer_token(self):
        """Test authentication with Bearer token format to FastAPI test-auth endpoint"""
        # Make request to FastAPI test-auth endpoint with Bearer token
        response = requests.get(
            self.fastapi_test_auth_url,
            headers={"Authorization": f"Bearer {self.token_key}"},
            timeout=5.0
        )

        # Print response data for debugging
        print(f"Response from FastAPI test-auth endpoint: {response.status_code}")
        print(f"Response content: {response.text[:200]}...")  # Print first 200 chars

        # Assert response is successful
        self.assertEqual(response.status_code, 200)

        # Assert response contains expected data
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['message'], 'Authentication successful')
        self.assertEqual(data['user_info']['username'], self.user.username)

        print(f"✅ Authentication with Bearer token to FastAPI test-auth endpoint successful")

if __name__ == '__main__':
    unittest.main()
