"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@iconify";
exports.ids = ["vendor-chunks/@iconify"];
exports.modules = {

/***/ "(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/customisations/defaults.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultIconCustomisations: () => (/* binding */ defaultIconCustomisations),\n/* harmony export */   defaultIconSizeCustomisations: () => (/* binding */ defaultIconSizeCustomisations)\n/* harmony export */ });\n/* harmony import */ var _icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n\n\nconst defaultIconSizeCustomisations = Object.freeze({\n  width: null,\n  height: null\n});\nconst defaultIconCustomisations = Object.freeze({\n  // Dimensions\n  ...defaultIconSizeCustomisations,\n  // Transformations\n  ..._icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultIconTransformations\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2N1c3RvbWlzYXRpb25zL2RlZmF1bHRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0U7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUssMEVBQTBCO0FBQy9CLENBQUM7O0FBRW1FIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBpY29uaWZ5XFx1dGlsc1xcbGliXFxjdXN0b21pc2F0aW9uc1xcZGVmYXVsdHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zIH0gZnJvbSAnLi4vaWNvbi9kZWZhdWx0cy5tanMnO1xuXG5jb25zdCBkZWZhdWx0SWNvblNpemVDdXN0b21pc2F0aW9ucyA9IE9iamVjdC5mcmVlemUoe1xuICB3aWR0aDogbnVsbCxcbiAgaGVpZ2h0OiBudWxsXG59KTtcbmNvbnN0IGRlZmF1bHRJY29uQ3VzdG9taXNhdGlvbnMgPSBPYmplY3QuZnJlZXplKHtcbiAgLy8gRGltZW5zaW9uc1xuICAuLi5kZWZhdWx0SWNvblNpemVDdXN0b21pc2F0aW9ucyxcbiAgLy8gVHJhbnNmb3JtYXRpb25zXG4gIC4uLmRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zXG59KTtcblxuZXhwb3J0IHsgZGVmYXVsdEljb25DdXN0b21pc2F0aW9ucywgZGVmYXVsdEljb25TaXplQ3VzdG9taXNhdGlvbnMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconData: () => (/* binding */ getIconData),\n/* harmony export */   internalGetIconData: () => (/* binding */ internalGetIconData)\n/* harmony export */ });\n/* harmony import */ var _icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/merge.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs\");\n/* harmony import */ var _tree_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tree.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs\");\n\n\n\n\n\nfunction internalGetIconData(data, name, tree) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  let currentProps = {};\n  function parse(name2) {\n    currentProps = (0,_icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconData)(\n      icons[name2] || aliases[name2],\n      currentProps\n    );\n  }\n  parse(name);\n  tree.forEach(parse);\n  return (0,_icon_merge_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconData)(data, currentProps);\n}\nfunction getIconData(data, name) {\n  if (data.icons[name]) {\n    return internalGetIconData(data, name, []);\n  }\n  const tree = (0,_tree_mjs__WEBPACK_IMPORTED_MODULE_1__.getIconsTree)(data, [name])[name];\n  return tree ? internalGetIconData(data, name, tree) : null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24tc2V0L2dldC1pY29uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWtEO0FBQ1I7QUFDWjtBQUNPOztBQUVyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhEQUFhO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOERBQWE7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsdURBQVk7QUFDM0I7QUFDQTs7QUFFNEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGljb25pZnlcXHV0aWxzXFxsaWJcXGljb24tc2V0XFxnZXQtaWNvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWVyZ2VJY29uRGF0YSB9IGZyb20gJy4uL2ljb24vbWVyZ2UubWpzJztcbmltcG9ydCB7IGdldEljb25zVHJlZSB9IGZyb20gJy4vdHJlZS5tanMnO1xuaW1wb3J0ICcuLi9pY29uL2RlZmF1bHRzLm1qcyc7XG5pbXBvcnQgJy4uL2ljb24vdHJhbnNmb3JtYXRpb25zLm1qcyc7XG5cbmZ1bmN0aW9uIGludGVybmFsR2V0SWNvbkRhdGEoZGF0YSwgbmFtZSwgdHJlZSkge1xuICBjb25zdCBpY29ucyA9IGRhdGEuaWNvbnM7XG4gIGNvbnN0IGFsaWFzZXMgPSBkYXRhLmFsaWFzZXMgfHwgLyogQF9fUFVSRV9fICovIE9iamVjdC5jcmVhdGUobnVsbCk7XG4gIGxldCBjdXJyZW50UHJvcHMgPSB7fTtcbiAgZnVuY3Rpb24gcGFyc2UobmFtZTIpIHtcbiAgICBjdXJyZW50UHJvcHMgPSBtZXJnZUljb25EYXRhKFxuICAgICAgaWNvbnNbbmFtZTJdIHx8IGFsaWFzZXNbbmFtZTJdLFxuICAgICAgY3VycmVudFByb3BzXG4gICAgKTtcbiAgfVxuICBwYXJzZShuYW1lKTtcbiAgdHJlZS5mb3JFYWNoKHBhcnNlKTtcbiAgcmV0dXJuIG1lcmdlSWNvbkRhdGEoZGF0YSwgY3VycmVudFByb3BzKTtcbn1cbmZ1bmN0aW9uIGdldEljb25EYXRhKGRhdGEsIG5hbWUpIHtcbiAgaWYgKGRhdGEuaWNvbnNbbmFtZV0pIHtcbiAgICByZXR1cm4gaW50ZXJuYWxHZXRJY29uRGF0YShkYXRhLCBuYW1lLCBbXSk7XG4gIH1cbiAgY29uc3QgdHJlZSA9IGdldEljb25zVHJlZShkYXRhLCBbbmFtZV0pW25hbWVdO1xuICByZXR1cm4gdHJlZSA/IGludGVybmFsR2V0SWNvbkRhdGEoZGF0YSwgbmFtZSwgdHJlZSkgOiBudWxsO1xufVxuXG5leHBvcnQgeyBnZXRJY29uRGF0YSwgaW50ZXJuYWxHZXRJY29uRGF0YSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon-set/get-icon.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon-set/tree.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getIconsTree: () => (/* binding */ getIconsTree)\n/* harmony export */ });\nfunction getIconsTree(data, names) {\n  const icons = data.icons;\n  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);\n  const resolved = /* @__PURE__ */ Object.create(null);\n  function resolve(name) {\n    if (icons[name]) {\n      return resolved[name] = [];\n    }\n    if (!(name in resolved)) {\n      resolved[name] = null;\n      const parent = aliases[name] && aliases[name].parent;\n      const value = parent && resolve(parent);\n      if (value) {\n        resolved[name] = [parent].concat(value);\n      }\n    }\n    return resolved[name];\n  }\n  (names || Object.keys(icons).concat(Object.keys(aliases))).forEach(resolve);\n  return resolved;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24tc2V0L3RyZWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBpY29uaWZ5XFx1dGlsc1xcbGliXFxpY29uLXNldFxcdHJlZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2V0SWNvbnNUcmVlKGRhdGEsIG5hbWVzKSB7XG4gIGNvbnN0IGljb25zID0gZGF0YS5pY29ucztcbiAgY29uc3QgYWxpYXNlcyA9IGRhdGEuYWxpYXNlcyB8fCAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgY29uc3QgcmVzb2x2ZWQgPSAvKiBAX19QVVJFX18gKi8gT2JqZWN0LmNyZWF0ZShudWxsKTtcbiAgZnVuY3Rpb24gcmVzb2x2ZShuYW1lKSB7XG4gICAgaWYgKGljb25zW25hbWVdKSB7XG4gICAgICByZXR1cm4gcmVzb2x2ZWRbbmFtZV0gPSBbXTtcbiAgICB9XG4gICAgaWYgKCEobmFtZSBpbiByZXNvbHZlZCkpIHtcbiAgICAgIHJlc29sdmVkW25hbWVdID0gbnVsbDtcbiAgICAgIGNvbnN0IHBhcmVudCA9IGFsaWFzZXNbbmFtZV0gJiYgYWxpYXNlc1tuYW1lXS5wYXJlbnQ7XG4gICAgICBjb25zdCB2YWx1ZSA9IHBhcmVudCAmJiByZXNvbHZlKHBhcmVudCk7XG4gICAgICBpZiAodmFsdWUpIHtcbiAgICAgICAgcmVzb2x2ZWRbbmFtZV0gPSBbcGFyZW50XS5jb25jYXQodmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzb2x2ZWRbbmFtZV07XG4gIH1cbiAgKG5hbWVzIHx8IE9iamVjdC5rZXlzKGljb25zKS5jb25jYXQoT2JqZWN0LmtleXMoYWxpYXNlcykpKS5mb3JFYWNoKHJlc29sdmUpO1xuICByZXR1cm4gcmVzb2x2ZWQ7XG59XG5cbmV4cG9ydCB7IGdldEljb25zVHJlZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon-set/tree.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/defaults.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultExtendedIconProps: () => (/* binding */ defaultExtendedIconProps),\n/* harmony export */   defaultIconDimensions: () => (/* binding */ defaultIconDimensions),\n/* harmony export */   defaultIconProps: () => (/* binding */ defaultIconProps),\n/* harmony export */   defaultIconTransformations: () => (/* binding */ defaultIconTransformations)\n/* harmony export */ });\nconst defaultIconDimensions = Object.freeze(\n  {\n    left: 0,\n    top: 0,\n    width: 16,\n    height: 16\n  }\n);\nconst defaultIconTransformations = Object.freeze({\n  rotate: 0,\n  vFlip: false,\n  hFlip: false\n});\nconst defaultIconProps = Object.freeze({\n  ...defaultIconDimensions,\n  ...defaultIconTransformations\n});\nconst defaultExtendedIconProps = Object.freeze({\n  ...defaultIconProps,\n  body: \"\",\n  hidden: false\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vZGVmYXVsdHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFd0ciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGljb25pZnlcXHV0aWxzXFxsaWJcXGljb25cXGRlZmF1bHRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBkZWZhdWx0SWNvbkRpbWVuc2lvbnMgPSBPYmplY3QuZnJlZXplKFxuICB7XG4gICAgbGVmdDogMCxcbiAgICB0b3A6IDAsXG4gICAgd2lkdGg6IDE2LFxuICAgIGhlaWdodDogMTZcbiAgfVxuKTtcbmNvbnN0IGRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zID0gT2JqZWN0LmZyZWV6ZSh7XG4gIHJvdGF0ZTogMCxcbiAgdkZsaXA6IGZhbHNlLFxuICBoRmxpcDogZmFsc2Vcbn0pO1xuY29uc3QgZGVmYXVsdEljb25Qcm9wcyA9IE9iamVjdC5mcmVlemUoe1xuICAuLi5kZWZhdWx0SWNvbkRpbWVuc2lvbnMsXG4gIC4uLmRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zXG59KTtcbmNvbnN0IGRlZmF1bHRFeHRlbmRlZEljb25Qcm9wcyA9IE9iamVjdC5mcmVlemUoe1xuICAuLi5kZWZhdWx0SWNvblByb3BzLFxuICBib2R5OiBcIlwiLFxuICBoaWRkZW46IGZhbHNlXG59KTtcblxuZXhwb3J0IHsgZGVmYXVsdEV4dGVuZGVkSWNvblByb3BzLCBkZWZhdWx0SWNvbkRpbWVuc2lvbnMsIGRlZmF1bHRJY29uUHJvcHMsIGRlZmF1bHRJY29uVHJhbnNmb3JtYXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/merge.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeIconData: () => (/* binding */ mergeIconData)\n/* harmony export */ });\n/* harmony import */ var _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n/* harmony import */ var _transformations_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./transformations.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs\");\n\n\n\nfunction mergeIconData(parent, child) {\n  const result = (0,_transformations_mjs__WEBPACK_IMPORTED_MODULE_0__.mergeIconTransformations)(parent, child);\n  for (const key in _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultExtendedIconProps) {\n    if (key in _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconTransformations) {\n      if (key in parent && !(key in result)) {\n        result[key] = _defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconTransformations[key];\n      }\n    } else if (key in child) {\n      result[key] = child[key];\n    } else if (key in parent) {\n      result[key] = parent[key];\n    }\n  }\n  return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vbWVyZ2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzRjtBQUNyQjs7QUFFakU7QUFDQSxpQkFBaUIsOEVBQXdCO0FBQ3pDLG9CQUFvQixtRUFBd0I7QUFDNUMsZUFBZSxxRUFBMEI7QUFDekM7QUFDQSxzQkFBc0IscUVBQTBCO0FBQ2hEO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBpY29uaWZ5XFx1dGlsc1xcbGliXFxpY29uXFxtZXJnZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVmYXVsdEV4dGVuZGVkSWNvblByb3BzLCBkZWZhdWx0SWNvblRyYW5zZm9ybWF0aW9ucyB9IGZyb20gJy4vZGVmYXVsdHMubWpzJztcbmltcG9ydCB7IG1lcmdlSWNvblRyYW5zZm9ybWF0aW9ucyB9IGZyb20gJy4vdHJhbnNmb3JtYXRpb25zLm1qcyc7XG5cbmZ1bmN0aW9uIG1lcmdlSWNvbkRhdGEocGFyZW50LCBjaGlsZCkge1xuICBjb25zdCByZXN1bHQgPSBtZXJnZUljb25UcmFuc2Zvcm1hdGlvbnMocGFyZW50LCBjaGlsZCk7XG4gIGZvciAoY29uc3Qga2V5IGluIGRlZmF1bHRFeHRlbmRlZEljb25Qcm9wcykge1xuICAgIGlmIChrZXkgaW4gZGVmYXVsdEljb25UcmFuc2Zvcm1hdGlvbnMpIHtcbiAgICAgIGlmIChrZXkgaW4gcGFyZW50ICYmICEoa2V5IGluIHJlc3VsdCkpIHtcbiAgICAgICAgcmVzdWx0W2tleV0gPSBkZWZhdWx0SWNvblRyYW5zZm9ybWF0aW9uc1trZXldO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoa2V5IGluIGNoaWxkKSB7XG4gICAgICByZXN1bHRba2V5XSA9IGNoaWxkW2tleV07XG4gICAgfSBlbHNlIGlmIChrZXkgaW4gcGFyZW50KSB7XG4gICAgICByZXN1bHRba2V5XSA9IHBhcmVudFtrZXldO1xuICAgIH1cbiAgfVxuICByZXR1cm4gcmVzdWx0O1xufVxuXG5leHBvcnQgeyBtZXJnZUljb25EYXRhIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/merge.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/name.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/name.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matchIconName: () => (/* binding */ matchIconName),\n/* harmony export */   stringToIcon: () => (/* binding */ stringToIcon),\n/* harmony export */   validateIconName: () => (/* binding */ validateIconName)\n/* harmony export */ });\nconst matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;\nconst stringToIcon = (value, validate, allowSimpleName, provider = \"\") => {\n  const colonSeparated = value.split(\":\");\n  if (value.slice(0, 1) === \"@\") {\n    if (colonSeparated.length < 2 || colonSeparated.length > 3) {\n      return null;\n    }\n    provider = colonSeparated.shift().slice(1);\n  }\n  if (colonSeparated.length > 3 || !colonSeparated.length) {\n    return null;\n  }\n  if (colonSeparated.length > 1) {\n    const name2 = colonSeparated.pop();\n    const prefix = colonSeparated.pop();\n    const result = {\n      // Allow provider without '@': \"provider:prefix:name\"\n      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,\n      prefix,\n      name: name2\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  const name = colonSeparated[0];\n  const dashSeparated = name.split(\"-\");\n  if (dashSeparated.length > 1) {\n    const result = {\n      provider,\n      prefix: dashSeparated.shift(),\n      name: dashSeparated.join(\"-\")\n    };\n    return validate && !validateIconName(result) ? null : result;\n  }\n  if (allowSimpleName && provider === \"\") {\n    const result = {\n      provider,\n      prefix: \"\",\n      name\n    };\n    return validate && !validateIconName(result, allowSimpleName) ? null : result;\n  }\n  return null;\n};\nconst validateIconName = (icon, allowSimpleName) => {\n  if (!icon) {\n    return false;\n  }\n  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled\n  // Check name: cannot be empty\n  ((allowSimpleName && icon.prefix === \"\" || !!icon.prefix) && !!icon.name);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/name.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/icon/transformations.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeIconTransformations: () => (/* binding */ mergeIconTransformations)\n/* harmony export */ });\nfunction mergeIconTransformations(obj1, obj2) {\n  const result = {};\n  if (!obj1.hFlip !== !obj2.hFlip) {\n    result.hFlip = true;\n  }\n  if (!obj1.vFlip !== !obj2.vFlip) {\n    result.vFlip = true;\n  }\n  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;\n  if (rotate) {\n    result.rotate = rotate;\n  }\n  return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL2ljb24vdHJhbnNmb3JtYXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGljb25pZnlcXHV0aWxzXFxsaWJcXGljb25cXHRyYW5zZm9ybWF0aW9ucy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gbWVyZ2VJY29uVHJhbnNmb3JtYXRpb25zKG9iajEsIG9iajIpIHtcbiAgY29uc3QgcmVzdWx0ID0ge307XG4gIGlmICghb2JqMS5oRmxpcCAhPT0gIW9iajIuaEZsaXApIHtcbiAgICByZXN1bHQuaEZsaXAgPSB0cnVlO1xuICB9XG4gIGlmICghb2JqMS52RmxpcCAhPT0gIW9iajIudkZsaXApIHtcbiAgICByZXN1bHQudkZsaXAgPSB0cnVlO1xuICB9XG4gIGNvbnN0IHJvdGF0ZSA9ICgob2JqMS5yb3RhdGUgfHwgMCkgKyAob2JqMi5yb3RhdGUgfHwgMCkpICUgNDtcbiAgaWYgKHJvdGF0ZSkge1xuICAgIHJlc3VsdC5yb3RhdGUgPSByb3RhdGU7XG4gIH1cbiAgcmV0dXJuIHJlc3VsdDtcbn1cblxuZXhwb3J0IHsgbWVyZ2VJY29uVHJhbnNmb3JtYXRpb25zIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/icon/transformations.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/build.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/build.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iconToSVG: () => (/* binding */ iconToSVG),\n/* harmony export */   isUnsetKeyword: () => (/* binding */ isUnsetKeyword)\n/* harmony export */ });\n/* harmony import */ var _icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../icon/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/icon/defaults.mjs\");\n/* harmony import */ var _customisations_defaults_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../customisations/defaults.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/customisations/defaults.mjs\");\n/* harmony import */ var _size_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./size.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs\");\n/* harmony import */ var _defs_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./defs.mjs */ \"(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs\");\n\n\n\n\n\nconst isUnsetKeyword = (value) => value === \"unset\" || value === \"undefined\" || value === \"none\";\nfunction iconToSVG(icon, customisations) {\n  const fullIcon = {\n    ..._icon_defaults_mjs__WEBPACK_IMPORTED_MODULE_0__.defaultIconProps,\n    ...icon\n  };\n  const fullCustomisations = {\n    ..._customisations_defaults_mjs__WEBPACK_IMPORTED_MODULE_1__.defaultIconCustomisations,\n    ...customisations\n  };\n  const box = {\n    left: fullIcon.left,\n    top: fullIcon.top,\n    width: fullIcon.width,\n    height: fullIcon.height\n  };\n  let body = fullIcon.body;\n  [fullIcon, fullCustomisations].forEach((props) => {\n    const transformations = [];\n    const hFlip = props.hFlip;\n    const vFlip = props.vFlip;\n    let rotation = props.rotate;\n    if (hFlip) {\n      if (vFlip) {\n        rotation += 2;\n      } else {\n        transformations.push(\n          \"translate(\" + (box.width + box.left).toString() + \" \" + (0 - box.top).toString() + \")\"\n        );\n        transformations.push(\"scale(-1 1)\");\n        box.top = box.left = 0;\n      }\n    } else if (vFlip) {\n      transformations.push(\n        \"translate(\" + (0 - box.left).toString() + \" \" + (box.height + box.top).toString() + \")\"\n      );\n      transformations.push(\"scale(1 -1)\");\n      box.top = box.left = 0;\n    }\n    let tempValue;\n    if (rotation < 0) {\n      rotation -= Math.floor(rotation / 4) * 4;\n    }\n    rotation = rotation % 4;\n    switch (rotation) {\n      case 1:\n        tempValue = box.height / 2 + box.top;\n        transformations.unshift(\n          \"rotate(90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n      case 2:\n        transformations.unshift(\n          \"rotate(180 \" + (box.width / 2 + box.left).toString() + \" \" + (box.height / 2 + box.top).toString() + \")\"\n        );\n        break;\n      case 3:\n        tempValue = box.width / 2 + box.left;\n        transformations.unshift(\n          \"rotate(-90 \" + tempValue.toString() + \" \" + tempValue.toString() + \")\"\n        );\n        break;\n    }\n    if (rotation % 2 === 1) {\n      if (box.left !== box.top) {\n        tempValue = box.left;\n        box.left = box.top;\n        box.top = tempValue;\n      }\n      if (box.width !== box.height) {\n        tempValue = box.width;\n        box.width = box.height;\n        box.height = tempValue;\n      }\n    }\n    if (transformations.length) {\n      body = (0,_defs_mjs__WEBPACK_IMPORTED_MODULE_2__.wrapSVGContent)(\n        body,\n        '<g transform=\"' + transformations.join(\" \") + '\">',\n        \"</g>\"\n      );\n    }\n  });\n  const customisationsWidth = fullCustomisations.width;\n  const customisationsHeight = fullCustomisations.height;\n  const boxWidth = box.width;\n  const boxHeight = box.height;\n  let width;\n  let height;\n  if (customisationsWidth === null) {\n    height = customisationsHeight === null ? \"1em\" : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n    width = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateSize)(height, boxWidth / boxHeight);\n  } else {\n    width = customisationsWidth === \"auto\" ? boxWidth : customisationsWidth;\n    height = customisationsHeight === null ? (0,_size_mjs__WEBPACK_IMPORTED_MODULE_3__.calculateSize)(width, boxHeight / boxWidth) : customisationsHeight === \"auto\" ? boxHeight : customisationsHeight;\n  }\n  const attributes = {};\n  const setAttr = (prop, value) => {\n    if (!isUnsetKeyword(value)) {\n      attributes[prop] = value.toString();\n    }\n  };\n  setAttr(\"width\", width);\n  setAttr(\"height\", height);\n  const viewBox = [box.left, box.top, boxWidth, boxHeight];\n  attributes.viewBox = viewBox.join(\" \");\n  return {\n    attributes,\n    viewBox,\n    body\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/build.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/defs.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mergeDefsAndContent: () => (/* binding */ mergeDefsAndContent),\n/* harmony export */   splitSVGDefs: () => (/* binding */ splitSVGDefs),\n/* harmony export */   wrapSVGContent: () => (/* binding */ wrapSVGContent)\n/* harmony export */ });\nfunction splitSVGDefs(content, tag = \"defs\") {\n  let defs = \"\";\n  const index = content.indexOf(\"<\" + tag);\n  while (index >= 0) {\n    const start = content.indexOf(\">\", index);\n    const end = content.indexOf(\"</\" + tag);\n    if (start === -1 || end === -1) {\n      break;\n    }\n    const endEnd = content.indexOf(\">\", end);\n    if (endEnd === -1) {\n      break;\n    }\n    defs += content.slice(start + 1, end).trim();\n    content = content.slice(0, index).trim() + content.slice(endEnd + 1);\n  }\n  return {\n    defs,\n    content\n  };\n}\nfunction mergeDefsAndContent(defs, content) {\n  return defs ? \"<defs>\" + defs + \"</defs>\" + content : content;\n}\nfunction wrapSVGContent(body, start, end) {\n  const split = splitSVGDefs(body);\n  return mergeDefsAndContent(split.defs, start + split.content + end);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9kZWZzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFNkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGljb25pZnlcXHV0aWxzXFxsaWJcXHN2Z1xcZGVmcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gc3BsaXRTVkdEZWZzKGNvbnRlbnQsIHRhZyA9IFwiZGVmc1wiKSB7XG4gIGxldCBkZWZzID0gXCJcIjtcbiAgY29uc3QgaW5kZXggPSBjb250ZW50LmluZGV4T2YoXCI8XCIgKyB0YWcpO1xuICB3aGlsZSAoaW5kZXggPj0gMCkge1xuICAgIGNvbnN0IHN0YXJ0ID0gY29udGVudC5pbmRleE9mKFwiPlwiLCBpbmRleCk7XG4gICAgY29uc3QgZW5kID0gY29udGVudC5pbmRleE9mKFwiPC9cIiArIHRhZyk7XG4gICAgaWYgKHN0YXJ0ID09PSAtMSB8fCBlbmQgPT09IC0xKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgY29uc3QgZW5kRW5kID0gY29udGVudC5pbmRleE9mKFwiPlwiLCBlbmQpO1xuICAgIGlmIChlbmRFbmQgPT09IC0xKSB7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgZGVmcyArPSBjb250ZW50LnNsaWNlKHN0YXJ0ICsgMSwgZW5kKS50cmltKCk7XG4gICAgY29udGVudCA9IGNvbnRlbnQuc2xpY2UoMCwgaW5kZXgpLnRyaW0oKSArIGNvbnRlbnQuc2xpY2UoZW5kRW5kICsgMSk7XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBkZWZzLFxuICAgIGNvbnRlbnRcbiAgfTtcbn1cbmZ1bmN0aW9uIG1lcmdlRGVmc0FuZENvbnRlbnQoZGVmcywgY29udGVudCkge1xuICByZXR1cm4gZGVmcyA/IFwiPGRlZnM+XCIgKyBkZWZzICsgXCI8L2RlZnM+XCIgKyBjb250ZW50IDogY29udGVudDtcbn1cbmZ1bmN0aW9uIHdyYXBTVkdDb250ZW50KGJvZHksIHN0YXJ0LCBlbmQpIHtcbiAgY29uc3Qgc3BsaXQgPSBzcGxpdFNWR0RlZnMoYm9keSk7XG4gIHJldHVybiBtZXJnZURlZnNBbmRDb250ZW50KHNwbGl0LmRlZnMsIHN0YXJ0ICsgc3BsaXQuY29udGVudCArIGVuZCk7XG59XG5cbmV4cG9ydCB7IG1lcmdlRGVmc0FuZENvbnRlbnQsIHNwbGl0U1ZHRGVmcywgd3JhcFNWR0NvbnRlbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/defs.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/html.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/html.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   iconToHTML: () => (/* binding */ iconToHTML)\n/* harmony export */ });\nfunction iconToHTML(body, attributes) {\n  let renderAttribsHTML = body.indexOf(\"xlink:\") === -1 ? \"\" : ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"';\n  for (const attr in attributes) {\n    renderAttribsHTML += \" \" + attr + '=\"' + attributes[attr] + '\"';\n  }\n  return '<svg xmlns=\"http://www.w3.org/2000/svg\"' + renderAttribsHTML + \">\" + body + \"</svg>\";\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9odG1sLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBpY29uaWZ5XFx1dGlsc1xcbGliXFxzdmdcXGh0bWwubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGljb25Ub0hUTUwoYm9keSwgYXR0cmlidXRlcykge1xuICBsZXQgcmVuZGVyQXR0cmlic0hUTUwgPSBib2R5LmluZGV4T2YoXCJ4bGluazpcIikgPT09IC0xID8gXCJcIiA6ICcgeG1sbnM6eGxpbms9XCJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rXCInO1xuICBmb3IgKGNvbnN0IGF0dHIgaW4gYXR0cmlidXRlcykge1xuICAgIHJlbmRlckF0dHJpYnNIVE1MICs9IFwiIFwiICsgYXR0ciArICc9XCInICsgYXR0cmlidXRlc1thdHRyXSArICdcIic7XG4gIH1cbiAgcmV0dXJuICc8c3ZnIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIicgKyByZW5kZXJBdHRyaWJzSFRNTCArIFwiPlwiICsgYm9keSArIFwiPC9zdmc+XCI7XG59XG5cbmV4cG9ydCB7IGljb25Ub0hUTUwgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/html.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/id.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/id.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   replaceIDs: () => (/* binding */ replaceIDs)\n/* harmony export */ });\nconst regex = /\\sid=\"(\\S+)\"/g;\nconst randomPrefix = \"IconifyId\" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);\nlet counter = 0;\nfunction replaceIDs(body, prefix = randomPrefix) {\n  const ids = [];\n  let match;\n  while (match = regex.exec(body)) {\n    ids.push(match[1]);\n  }\n  if (!ids.length) {\n    return body;\n  }\n  const suffix = \"suffix\" + (Math.random() * 16777216 | Date.now()).toString(16);\n  ids.forEach((id) => {\n    const newID = typeof prefix === \"function\" ? prefix(id) : prefix + (counter++).toString();\n    const escapedID = id.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n    body = body.replace(\n      // Allowed characters before id: [#;\"]\n      // Allowed characters after id: [)\"], .[a-z]\n      new RegExp('([#;\"])(' + escapedID + ')([\")]|\\\\.[a-z])', \"g\"),\n      \"$1\" + newID + suffix + \"$3\"\n    );\n  });\n  body = body.replace(new RegExp(suffix, \"g\"), \"\");\n  return body;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGljb25pZnkvdXRpbHMvbGliL3N2Zy9pZC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQztBQUMzQztBQUNBLDBDQUEwQztBQUMxQztBQUNBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTs7QUFFc0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGljb25pZnlcXHV0aWxzXFxsaWJcXHN2Z1xcaWQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHJlZ2V4ID0gL1xcc2lkPVwiKFxcUyspXCIvZztcbmNvbnN0IHJhbmRvbVByZWZpeCA9IFwiSWNvbmlmeUlkXCIgKyBEYXRlLm5vdygpLnRvU3RyaW5nKDE2KSArIChNYXRoLnJhbmRvbSgpICogMTY3NzcyMTYgfCAwKS50b1N0cmluZygxNik7XG5sZXQgY291bnRlciA9IDA7XG5mdW5jdGlvbiByZXBsYWNlSURzKGJvZHksIHByZWZpeCA9IHJhbmRvbVByZWZpeCkge1xuICBjb25zdCBpZHMgPSBbXTtcbiAgbGV0IG1hdGNoO1xuICB3aGlsZSAobWF0Y2ggPSByZWdleC5leGVjKGJvZHkpKSB7XG4gICAgaWRzLnB1c2gobWF0Y2hbMV0pO1xuICB9XG4gIGlmICghaWRzLmxlbmd0aCkge1xuICAgIHJldHVybiBib2R5O1xuICB9XG4gIGNvbnN0IHN1ZmZpeCA9IFwic3VmZml4XCIgKyAoTWF0aC5yYW5kb20oKSAqIDE2Nzc3MjE2IHwgRGF0ZS5ub3coKSkudG9TdHJpbmcoMTYpO1xuICBpZHMuZm9yRWFjaCgoaWQpID0+IHtcbiAgICBjb25zdCBuZXdJRCA9IHR5cGVvZiBwcmVmaXggPT09IFwiZnVuY3Rpb25cIiA/IHByZWZpeChpZCkgOiBwcmVmaXggKyAoY291bnRlcisrKS50b1N0cmluZygpO1xuICAgIGNvbnN0IGVzY2FwZWRJRCA9IGlkLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCBcIlxcXFwkJlwiKTtcbiAgICBib2R5ID0gYm9keS5yZXBsYWNlKFxuICAgICAgLy8gQWxsb3dlZCBjaGFyYWN0ZXJzIGJlZm9yZSBpZDogWyM7XCJdXG4gICAgICAvLyBBbGxvd2VkIGNoYXJhY3RlcnMgYWZ0ZXIgaWQ6IFspXCJdLCAuW2Etel1cbiAgICAgIG5ldyBSZWdFeHAoJyhbIztcIl0pKCcgKyBlc2NhcGVkSUQgKyAnKShbXCIpXXxcXFxcLlthLXpdKScsIFwiZ1wiKSxcbiAgICAgIFwiJDFcIiArIG5ld0lEICsgc3VmZml4ICsgXCIkM1wiXG4gICAgKTtcbiAgfSk7XG4gIGJvZHkgPSBib2R5LnJlcGxhY2UobmV3IFJlZ0V4cChzdWZmaXgsIFwiZ1wiKSwgXCJcIik7XG4gIHJldHVybiBib2R5O1xufVxuXG5leHBvcnQgeyByZXBsYWNlSURzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/id.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@iconify/utils/lib/svg/size.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSize: () => (/* binding */ calculateSize)\n/* harmony export */ });\nconst unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;\nconst unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;\nfunction calculateSize(size, ratio, precision) {\n  if (ratio === 1) {\n    return size;\n  }\n  precision = precision || 100;\n  if (typeof size === \"number\") {\n    return Math.ceil(size * ratio * precision) / precision;\n  }\n  if (typeof size !== \"string\") {\n    return size;\n  }\n  const oldParts = size.split(unitsSplit);\n  if (oldParts === null || !oldParts.length) {\n    return size;\n  }\n  const newParts = [];\n  let code = oldParts.shift();\n  let isNumber = unitsTest.test(code);\n  while (true) {\n    if (isNumber) {\n      const num = parseFloat(code);\n      if (isNaN(num)) {\n        newParts.push(code);\n      } else {\n        newParts.push(Math.ceil(num * ratio * precision) / precision);\n      }\n    } else {\n      newParts.push(code);\n    }\n    code = oldParts.shift();\n    if (code === void 0) {\n      return newParts.join(\"\");\n    }\n    isNumber = !isNumber;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@iconify/utils/lib/svg/size.mjs\n");

/***/ })

};
;