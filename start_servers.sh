#!/bin/bash

echo "Activating virtual environment..."

# Activate the virtual environment
source venv/bin/activate

# Start Django server in a new terminal
gnome-terminal -- bash -c "cd core && source ../venv/bin/activate && echo 'Starting Django on port 8000...' && python manage.py runserver; exec bash"

# Start FastAPI server in a new terminal
gnome-terminal -- bash -c "cd llm_api && source ../venv/bin/activate && echo 'Starting FastAPI on port 8001...' && uvicorn main:app --reload --host 0.0.0.0 --port 8001; exec bash"

echo "Both servers started in new terminals. You can now run the frontend with 'npm run dev' in the frontend directory."
