"""
CMS (Concept Mastery System) Utilities

This module contains utility functions for calculating CMS scores and generating
personalized learning recommendations based on the adaptive learning system.
"""

from typing import Dict, <PERSON><PERSON>, Any
from decimal import Decimal
from .models import CMSScore, DocumentTimeTracking
from documents.models import DocumentLearningTime


class CMSCalculator:
    """
    Calculator class for CMS (Concept Mastery System) scores.
    Implements the adaptive learning algorithm based on quiz performance and time efficiency.
    """
    
    # Scoring matrix based on the provided image
    TIME_SCORING_MATRIX = {
        ('high', 'excellent'): 30,    # High time, Quiz ≥ 70%
        ('high', 'moderate'): 10,     # High time, Quiz 40-69%
        ('high', 'poor'): -10,        # High time, Quiz < 40%
        ('average', 'excellent'): 20, # Average time, Quiz ≥ 70%
        ('average', 'moderate'): 15,  # Average time, Quiz 40-69%
        ('average', 'poor'): 0,       # Average time, Quiz < 40%
        ('low', 'excellent'): 30,     # Low time, Quiz ≥ 70%
        ('low', 'moderate'): 0,       # Low time, Quiz 40-69%
        ('low', 'poor'): -10,         # Low time, Quiz < 40%
    }
    
    # Time ratio thresholds for categorization
    HIGH_TIME_THRESHOLD = 1.5  # 150% or more of average time
    LOW_TIME_THRESHOLD = 0.7   # 70% or less of average time
    
    # Quiz score thresholds
    EXCELLENT_QUIZ_THRESHOLD = 70  # 70% or higher
    MODERATE_QUIZ_THRESHOLD = 40   # 40% or higher
    
    @classmethod
    def get_time_spent_for_document(cls, user, document) -> int:
        """
        Get total time spent by user on a specific document.
        
        Args:
            user: User instance
            document: Document instance
            
        Returns:
            int: Total time spent in seconds
        """
        try:
            tracking = DocumentTimeTracking.objects.get(
                username=user.username,
                file_name=document.title
            )
            return tracking.total_time_seconds
        except DocumentTimeTracking.DoesNotExist:
            # If no tracking record exists, return 0
            return 0
    
    @classmethod
    def get_average_time_for_document(cls, document) -> int:
        """
        Get predicted average learning time for a document.
        
        Args:
            document: Document instance
            
        Returns:
            int: Average learning time in seconds
        """
        try:
            learning_time = DocumentLearningTime.objects.get(document=document)
            return learning_time.predicted_time_seconds
        except DocumentLearningTime.DoesNotExist:
            # If no prediction exists, return a default value (30 minutes)
            return 1800  # 30 minutes in seconds
    
    @classmethod
    def categorize_time_efficiency(cls, time_ratio: float) -> str:
        """
        Categorize time efficiency based on ratio to average time.
        
        Args:
            time_ratio: Ratio of time_spent / average_time
            
        Returns:
            str: Time category ('high', 'average', 'low')
        """
        if time_ratio >= cls.HIGH_TIME_THRESHOLD:
            return 'high'
        elif time_ratio <= cls.LOW_TIME_THRESHOLD:
            return 'low'
        else:
            return 'average'
    
    @classmethod
    def categorize_quiz_performance(cls, quiz_score: float) -> str:
        """
        Categorize quiz performance based on score.
        
        Args:
            quiz_score: Quiz score as percentage (0-100)
            
        Returns:
            str: Performance category ('excellent', 'moderate', 'poor')
        """
        if quiz_score >= cls.EXCELLENT_QUIZ_THRESHOLD:
            return 'excellent'
        elif quiz_score >= cls.MODERATE_QUIZ_THRESHOLD:
            return 'moderate'
        else:
            return 'poor'
    
    @classmethod
    def calculate_time_component_points(cls, time_category: str, quiz_performance_category: str) -> int:
        """
        Calculate time component points based on the scoring matrix.
        
        Args:
            time_category: 'high', 'average', or 'low'
            quiz_performance_category: 'excellent', 'moderate', or 'poor'
            
        Returns:
            int: Points to add/subtract for time component (-10 to +30)
        """
        return cls.TIME_SCORING_MATRIX.get((time_category, quiz_performance_category), 0)
    
    @classmethod
    def calculate_cms_score(cls, quiz_attempt) -> Dict[str, Any]:
        """
        Calculate comprehensive CMS score for a quiz attempt.
        
        Args:
            quiz_attempt: QuizAttempt instance
            
        Returns:
            dict: Complete CMS calculation data
        """
        # Get time data
        time_spent = cls.get_time_spent_for_document(quiz_attempt.user, quiz_attempt.document)
        average_time = cls.get_average_time_for_document(quiz_attempt.document)
        
        # Calculate time ratio (handle division by zero)
        time_ratio = time_spent / average_time if average_time > 0 else 1.0
        
        # Get quiz score
        quiz_score_percentage = float(quiz_attempt.score)
        
        # Calculate quiz score component (70% of total)
        quiz_score_component = (quiz_score_percentage / 100) * 70
        
        # Categorize performance
        quiz_performance_category = cls.categorize_quiz_performance(quiz_score_percentage)
        time_category = cls.categorize_time_efficiency(time_ratio)
        
        # Calculate time component points
        time_component_points = cls.calculate_time_component_points(
            time_category, quiz_performance_category
        )
        
        # Calculate final CMS score
        cms_score = quiz_score_component + time_component_points
        
        # Ensure CMS score is within 0-100 range
        cms_score = max(0, min(100, cms_score))
        
        # Determine user zone
        user_zone = cls.determine_user_zone(cms_score)
        
        return {
            'quiz_score_percentage': quiz_score_percentage,
            'quiz_score_component': quiz_score_component,
            'quiz_performance_category': quiz_performance_category,
            'time_spent_seconds': time_spent,
            'average_time_seconds': average_time,
            'time_ratio': time_ratio,
            'time_category': time_category,
            'time_component_points': time_component_points,
            'cms_score': cms_score,
            'user_zone': user_zone
        }
    
    @classmethod
    def determine_user_zone(cls, cms_score: float) -> str:
        """
        Determine user zone based on CMS score.
        
        Args:
            cms_score: CMS score out of 100
            
        Returns:
            str: User zone classification
        """
        if cms_score >= 90:
            return 'mastered'
        elif cms_score >= 75:
            return 'stable'
        elif cms_score >= 60:
            return 'moderate_gap'
        elif cms_score >= 40:
            return 'weak'
        else:
            return 'unclear'


class RecommendationEngine:
    """
    Engine for generating personalized learning recommendations based on CMS scores.
    """
    
    # Recommendation messages based on user zones
    ZONE_RECOMMENDATIONS = {
        'mastered': {
            'message': "You've nailed it! You've unlocked the next chapter.",
            'icon': '✅',
            'color': 'green'
        },
        'stable': {
            'message': "You're doing well. Review 3 key flashcards, a quick flowchart, and a short summary to stay sharp.",
            'icon': '🟡',
            'color': 'yellow'
        },
        'moderate_gap': {
            'message': "Not bad! Go through the flashcards, check the flowchart, and read a short summary to improve.",
            'icon': '🟠',
            'color': 'orange'
        },
        'weak': {
            'message': "Let's strengthen your basics. Start with the summary, then the flowchart, and review flashcards.",
            'icon': '🔴',
            'color': 'red'
        },
        'unclear': {
            'message': "This topic needs attention. Go through the full summary, detailed flowchart, and all flashcards.",
            'icon': '❌',
            'color': 'red'
        }
    }
    
    @classmethod
    def get_recommendation(cls, user_zone: str) -> Dict[str, str]:
        """
        Get recommendation based on user zone.
        
        Args:
            user_zone: User zone classification
            
        Returns:
            dict: Recommendation data with message, icon, and color
        """
        return cls.ZONE_RECOMMENDATIONS.get(user_zone, cls.ZONE_RECOMMENDATIONS['unclear'])
    
    @classmethod
    def generate_detailed_recommendation(cls, cms_score_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate detailed recommendation with analysis.
        
        Args:
            cms_score_data: CMS calculation data from CMSCalculator
            
        Returns:
            dict: Detailed recommendation with analysis
        """
        user_zone = cms_score_data['user_zone']
        recommendation = cls.get_recommendation(user_zone)
        
        # Add detailed analysis
        analysis = {
            'quiz_performance': cls._analyze_quiz_performance(cms_score_data),
            'time_efficiency': cls._analyze_time_efficiency(cms_score_data),
            'overall_assessment': cls._generate_overall_assessment(cms_score_data)
        }
        
        return {
            'user_zone': user_zone,
            'cms_score': cms_score_data['cms_score'],
            'recommendation': recommendation,
            'analysis': analysis,
            'next_steps': cls._get_next_steps(user_zone)
        }
    
    @classmethod
    def _analyze_quiz_performance(cls, cms_data: Dict[str, Any]) -> str:
        """Generate quiz performance analysis."""
        category = cms_data['quiz_performance_category']
        score = cms_data['quiz_score_percentage']
        
        if category == 'excellent':
            return f"Excellent quiz performance ({score:.1f}%). You have a strong grasp of the concepts."
        elif category == 'moderate':
            return f"Good quiz performance ({score:.1f}%). There's room for improvement in some areas."
        else:
            return f"Quiz performance needs attention ({score:.1f}%). Focus on understanding core concepts."
    
    @classmethod
    def _analyze_time_efficiency(cls, cms_data: Dict[str, Any]) -> str:
        """Generate time efficiency analysis."""
        category = cms_data['time_category']
        ratio = cms_data['time_ratio']
        
        if category == 'low':
            return f"Efficient learning pace ({ratio:.1f}x average time). You're learning quickly."
        elif category == 'average':
            return f"Steady learning pace ({ratio:.1f}x average time). Good balance of speed and comprehension."
        else:
            return f"Thorough learning approach ({ratio:.1f}x average time). Taking time to understand deeply."
    
    @classmethod
    def _generate_overall_assessment(cls, cms_data: Dict[str, Any]) -> str:
        """Generate overall learning assessment."""
        cms_score = cms_data['cms_score']
        user_zone = cms_data['user_zone']
        
        zone_descriptions = {
            'mastered': "You have mastered this topic completely.",
            'stable': "You have a solid understanding with minor gaps.",
            'moderate_gap': "You understand the basics but need to strengthen some areas.",
            'weak': "You need to focus on building foundational understanding.",
            'unclear': "This topic requires significant attention and review."
        }
        
        return f"CMS Score: {cms_score:.1f}/100. {zone_descriptions.get(user_zone, '')}"
    
    @classmethod
    def _get_next_steps(cls, user_zone: str) -> list:
        """Get specific next steps based on user zone."""
        next_steps = {
            'mastered': [
                "Move on to the next chapter or topic",
                "Consider helping others with this topic",
                "Apply knowledge to practical problems"
            ],
            'stable': [
                "Review 3 key flashcards",
                "Quick review of the flowchart",
                "Skim through the summary"
            ],
            'moderate_gap': [
                "Study all flashcards thoroughly",
                "Analyze the complete flowchart",
                "Read the summary carefully"
            ],
            'weak': [
                "Start with the complete summary",
                "Study the flowchart step by step",
                "Practice with all flashcards multiple times"
            ],
            'unclear': [
                "Read the full summary multiple times",
                "Study the detailed flowchart carefully",
                "Master all flashcards before proceeding",
                "Consider seeking additional help or resources"
            ]
        }
        
        return next_steps.get(user_zone, next_steps['unclear'])
