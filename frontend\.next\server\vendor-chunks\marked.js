"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/marked";
exports.ids = ["vendor-chunks/marked"];
exports.modules = {

/***/ "(ssr)/./node_modules/marked/lib/marked.esm.js":
/*!***********************************************!*\
  !*** ./node_modules/marked/lib/marked.esm.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hooks: () => (/* binding */ _Hooks),\n/* harmony export */   Lexer: () => (/* binding */ _Lexer),\n/* harmony export */   Marked: () => (/* binding */ Marked),\n/* harmony export */   Parser: () => (/* binding */ _Parser),\n/* harmony export */   Renderer: () => (/* binding */ _Renderer),\n/* harmony export */   TextRenderer: () => (/* binding */ _TextRenderer),\n/* harmony export */   Tokenizer: () => (/* binding */ _Tokenizer),\n/* harmony export */   defaults: () => (/* binding */ _defaults),\n/* harmony export */   getDefaults: () => (/* binding */ _getDefaults),\n/* harmony export */   lexer: () => (/* binding */ lexer),\n/* harmony export */   marked: () => (/* binding */ marked),\n/* harmony export */   options: () => (/* binding */ options),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseInline: () => (/* binding */ parseInline),\n/* harmony export */   parser: () => (/* binding */ parser),\n/* harmony export */   setOptions: () => (/* binding */ setOptions),\n/* harmony export */   use: () => (/* binding */ use),\n/* harmony export */   walkTokens: () => (/* binding */ walkTokens)\n/* harmony export */ });\n/**\n * marked v15.0.12 - a markdown parser\n * Copyright (c) 2011-2025, Christopher Jeffrey. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n\n// src/defaults.ts\nfunction _getDefaults() {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n  _defaults = newDefaults;\n}\n\n// src/rules.ts\nvar noopTest = { exec: () => null };\nfunction edit(regex, opt = \"\") {\n  let source = typeof regex === \"string\" ? regex : regex.source;\n  const obj = {\n    replace: (name, val) => {\n      let valSource = typeof val === \"string\" ? val : val.source;\n      valSource = valSource.replace(other.caret, \"$1\");\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    }\n  };\n  return obj;\n}\nvar other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n};\nvar newline = /^(?:[ \\t]*(?:\\n|$))+/;\nvar blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nvar fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nvar hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nvar heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nvar bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nvar lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nvar lheading = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex();\nvar lheadingGfm = edit(lheadingCore).replace(/bull/g, bullet).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex();\nvar _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nvar blockText = /^[^\\n]+/;\nvar _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nvar def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", _blockLabel).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\nvar list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\nvar _tag = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\";\nvar _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nvar html = edit(\n  \"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\",\n  \"i\"\n).replace(\"comment\", _comment).replace(\"tag\", _tag).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\nvar paragraph = edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", paragraph).getRegex();\nvar blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText\n};\nvar gfmTable = edit(\n  \"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\"\n).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex();\nvar blockGfm = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", gfmTable).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", _tag).getRegex()\n};\nvar blockPedantic = {\n  ...blockNormal,\n  html: edit(\n    `^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`\n  ).replace(\"comment\", _comment).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest,\n  // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph).replace(\"hr\", hr).replace(\"heading\", \" *#{1,6} *[^\\n]\").replace(\"lheading\", lheading).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n};\nvar escape = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nvar inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nvar br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nvar inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\nvar _punctuation = /[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nvar punctuation = edit(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, _punctuationOrSpace).getRegex();\nvar _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nvar _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nvar _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\nvar blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nvar emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nvar emStrongLDelim = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuation).getRegex();\nvar emStrongLDelimGfm = edit(emStrongLDelimCore, \"u\").replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimAstCore = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\";\nvar emStrongRDelimAst = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, \"gu\").replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm).replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm).replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nvar emStrongRDelimUnd = edit(\n  \"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\",\n  \"gu\"\n).replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nvar anyPunctuation = edit(/\\\\(punct)/, \"gu\").replace(/punct/g, _punctuation).getRegex();\nvar autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\nvar _inlineComment = edit(_comment).replace(\"(?:-->|$)\", \"-->\").getRegex();\nvar tag = edit(\n  \"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\"\n).replace(\"comment\", _inlineComment).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\nvar _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nvar link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", _inlineLabel).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\nvar reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", _inlineLabel).replace(\"ref\", _blockLabel).getRegex();\nvar nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", _blockLabel).getRegex();\nvar reflinkSearch = edit(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", reflink).replace(\"nolink\", nolink).getRegex();\nvar inlineNormal = {\n  _backpedal: noopTest,\n  // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest\n};\nvar inlinePedantic = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", _inlineLabel).getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", _inlineLabel).getRegex()\n};\nvar inlineGfm = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\nvar inlineBreaks = {\n  ...inlineGfm,\n  br: edit(br).replace(\"{2,}\", \"*\").getRegex(),\n  text: edit(inlineGfm.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n};\nvar block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic\n};\nvar inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic\n};\n\n// src/helpers.ts\nvar escapeReplacements = {\n  \"&\": \"&amp;\",\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&#39;\"\n};\nvar getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape2(html2, encode) {\n  if (encode) {\n    if (other.escapeTest.test(html2)) {\n      return html2.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html2)) {\n      return html2.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n  return html2;\n}\nfunction cleanUrl(href) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return href;\n}\nfunction splitCells(tableRow, count) {\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n    let escaped = false;\n    let curr = offset;\n    while (--curr >= 0 && str[curr] === \"\\\\\") escaped = !escaped;\n    if (escaped) {\n      return \"|\";\n    } else {\n      return \" |\";\n    }\n  }), cells = row.split(other.splitPipe);\n  let i = 0;\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push(\"\");\n    }\n  }\n  for (; i < cells.length; i++) {\n    cells[i] = cells[i].trim().replace(other.slashPipe, \"|\");\n  }\n  return cells;\n}\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return \"\";\n  }\n  let suffLen = 0;\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && !invert) {\n      suffLen++;\n    } else if (currChar !== c && invert) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n  return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === \"\\\\\") {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  if (level > 0) {\n    return -2;\n  }\n  return -1;\n}\n\n// src/Tokenizer.ts\nfunction outputLink(cap, link2, raw, lexer2, rules) {\n  const href = link2.href;\n  const title = link2.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, \"$1\");\n  lexer2.state.inLink = true;\n  const token = {\n    type: cap[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw,\n    href,\n    title,\n    text,\n    tokens: lexer2.inlineTokens(text)\n  };\n  lexer2.state.inLink = false;\n  return token;\n}\nfunction indentCodeCompensation(raw, text, rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n  if (matchIndentToCode === null) {\n    return text;\n  }\n  const indentToCode = matchIndentToCode[1];\n  return text.split(\"\\n\").map((node) => {\n    const matchIndentInNode = node.match(rules.other.beginningSpace);\n    if (matchIndentInNode === null) {\n      return node;\n    }\n    const [indentInNode] = matchIndentInNode;\n    if (indentInNode.length >= indentToCode.length) {\n      return node.slice(indentToCode.length);\n    }\n    return node;\n  }).join(\"\\n\");\n}\nvar _Tokenizer = class {\n  options;\n  rules;\n  // set by the lexer\n  lexer;\n  // set by the lexer\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: \"space\",\n        raw: cap[0]\n      };\n    }\n  }\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: cap[0],\n        codeBlockStyle: \"indented\",\n        text: !this.options.pedantic ? rtrim(text, \"\\n\") : text\n      };\n    }\n  }\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : cap[2],\n        text\n      };\n    }\n  }\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, \"#\");\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          text = trimmed.trim();\n        }\n      }\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: \"hr\",\n        raw: rtrim(cap[0], \"\\n\")\n      };\n    }\n  }\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], \"\\n\").split(\"\\n\");\n      let raw = \"\";\n      let text = \"\";\n      const tokens = [];\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n        const currentRaw = currentLines.join(\"\\n\");\n        const currentText = currentRaw.replace(this.rules.other.blockquoteSetextReplace, \"\\n    $1\").replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        raw = raw ? `${raw}\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\n${currentText}` : currentText;\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n        if (lines.length === 0) {\n          break;\n        }\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"code\") {\n          break;\n        } else if (lastToken?.type === \"blockquote\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.blockquote(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === \"list\") {\n          const oldToken = lastToken;\n          const newText = oldToken.raw + \"\\n\" + lines.join(\"\\n\");\n          const newToken = this.list(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1).raw.length).split(\"\\n\");\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw,\n        tokens,\n        text\n      };\n    }\n  }\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n      const list2 = {\n        type: \"list\",\n        raw: \"\",\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : \"\",\n        loose: false,\n        items: []\n      };\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n      if (this.options.pedantic) {\n        bull = isordered ? bull : \"[*+-]\";\n      }\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      while (src) {\n        let endEarly = false;\n        let raw = \"\";\n        let itemContents = \"\";\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n        if (this.rules.block.hr.test(src)) {\n          break;\n        }\n        raw = cap[0];\n        src = src.substring(raw.length);\n        let line = cap[2].split(\"\\n\", 1)[0].replace(this.rules.other.listReplaceTabs, (t) => \" \".repeat(3 * t.length));\n        let nextLine = src.split(\"\\n\", 1)[0];\n        let blankLine = !line.trim();\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar);\n          indent = indent > 4 ? 1 : indent;\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) {\n          raw += nextLine + \"\\n\";\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n          while (src) {\n            const rawLine = src.split(\"\\n\", 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, \"  \");\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, \"    \");\n            }\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) {\n              itemContents += \"\\n\" + nextLineWithoutTabs.slice(indent);\n            } else {\n              if (blankLine) {\n                break;\n              }\n              if (line.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4) {\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n              itemContents += \"\\n\" + nextLine;\n            }\n            if (!blankLine && !nextLine.trim()) {\n              blankLine = true;\n            }\n            raw += rawLine + \"\\n\";\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n        if (!list2.loose) {\n          if (endsWithBlankLine) {\n            list2.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n        let istask = null;\n        let ischecked;\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== \"[ ] \";\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, \"\");\n          }\n        }\n        list2.items.push({\n          type: \"list_item\",\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: []\n        });\n        list2.raw += raw;\n      }\n      const lastItem = list2.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        return;\n      }\n      list2.raw = list2.raw.trimEnd();\n      for (let i = 0; i < list2.items.length; i++) {\n        this.lexer.state.top = false;\n        list2.items[i].tokens = this.lexer.blockTokens(list2.items[i].text, []);\n        if (!list2.loose) {\n          const spacers = list2.items[i].tokens.filter((t) => t.type === \"space\");\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some((t) => this.rules.other.anyLine.test(t.raw));\n          list2.loose = hasMultipleLineBreaks;\n        }\n      }\n      if (list2.loose) {\n        for (let i = 0; i < list2.items.length; i++) {\n          list2.items[i].loose = true;\n        }\n      }\n      return list2;\n    }\n  }\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: \"html\",\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === \"pre\" || cap[1] === \"script\" || cap[1] === \"style\",\n        text: cap[0]\n      };\n      return token;\n    }\n  }\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag2 = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\";\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : cap[3];\n      return {\n        type: \"def\",\n        tag: tag2,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      return;\n    }\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\");\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, \"\").split(\"\\n\") : [];\n    const item = {\n      type: \"table\",\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: []\n    };\n    if (headers.length !== aligns.length) {\n      return;\n    }\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push(\"right\");\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push(\"center\");\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push(\"left\");\n      } else {\n        item.align.push(null);\n      }\n    }\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i]\n      });\n    }\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i]\n        };\n      }));\n    }\n    return item;\n  }\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: \"heading\",\n        raw: cap[0],\n        depth: cap[2].charAt(0) === \"=\" ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === \"\\n\" ? cap[1].slice(0, -1) : cap[1];\n      return {\n        type: \"paragraph\",\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: \"escape\",\n        raw: cap[0],\n        text: cap[1]\n      };\n    }\n  }\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n      return {\n        type: \"html\",\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0]\n      };\n    }\n  }\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        if (!this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          return;\n        }\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), \"\\\\\");\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        const lastParenIndex = findClosingBracket(cap[2], \"()\");\n        if (lastParenIndex === -2) {\n          return;\n        }\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf(\"!\") === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = \"\";\n        }\n      }\n      let href = cap[2];\n      let title = \"\";\n      if (this.options.pedantic) {\n        const link2 = this.rules.other.pedanticHrefTitle.exec(href);\n        if (link2) {\n          href = link2[1];\n          title = link2[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : \"\";\n      }\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, \"$1\") : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, \"$1\") : title\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, \" \");\n      const link2 = links[linkString.toLowerCase()];\n      if (!link2) {\n        const text = cap[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link2, cap[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(src, maskedSrc, prevChar = \"\") {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n    const nextChar = match[1] || match[2] || \"\";\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      const lLength = [...match[0]].length - 1;\n      let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n      const endReg = match[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n        if (!rDelim) continue;\n        rLength = [...rDelim].length;\n        if (match[3] || match[4]) {\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) {\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue;\n          }\n        }\n        delimTotal -= rLength;\n        if (delimTotal > 0) continue;\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n        if (Math.min(lLength, rLength) % 2) {\n          const text2 = raw.slice(1, -1);\n          return {\n            type: \"em\",\n            raw,\n            text: text2,\n            tokens: this.lexer.inlineTokens(text2)\n          };\n        }\n        const text = raw.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, \" \");\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: \"codespan\",\n        raw: cap[0],\n        text\n      };\n    }\n  }\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: \"br\",\n        raw: cap[0]\n      };\n    }\n  }\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: \"del\",\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n  autolink(src) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[1];\n        href = \"mailto:\" + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  url(src) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === \"@\") {\n        text = cap[0];\n        href = \"mailto:\" + text;\n      } else {\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? \"\";\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === \"www.\") {\n          href = \"http://\" + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: \"link\",\n        raw: cap[0],\n        text,\n        href,\n        tokens: [\n          {\n            type: \"text\",\n            raw: text,\n            text\n          }\n        ]\n      };\n    }\n  }\n  inlineText(src) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: cap[0],\n        text: cap[0],\n        escaped\n      };\n    }\n  }\n};\n\n// src/Lexer.ts\nvar _Lexer = class __Lexer {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(options2) {\n    this.tokens = [];\n    this.tokens.links = /* @__PURE__ */ Object.create(null);\n    this.options = options2 || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal\n    };\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.lex(src);\n  }\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options2) {\n    const lexer2 = new __Lexer(options2);\n    return lexer2.inlineTokens(src);\n  }\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src.replace(other.carriageReturn, \"\\n\");\n    this.blockTokens(src, this.tokens);\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n    return this.tokens;\n  }\n  blockTokens(src, tokens = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, \"    \").replace(other.spaceLine, \"\");\n    }\n    while (src) {\n      let token;\n      if (this.options.extensions?.block?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== void 0) {\n          lastToken.raw += \"\\n\";\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"paragraph\" || lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.raw;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === \"paragraph\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += \"\\n\" + token.raw;\n          lastToken.text += \"\\n\" + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    this.state.top = true;\n    return tokens;\n  }\n  inline(src, tokens = []) {\n    this.inlineQueue.push({ src, tokens });\n    return tokens;\n  }\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    let maskedSrc = src;\n    let match = null;\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf(\"[\") + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"++\" + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + \"[\" + \"a\".repeat(match[0].length - 2) + \"]\" + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n    let keepPrevChar = false;\n    let prevChar = \"\";\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = \"\";\n      }\n      keepPrevChar = false;\n      let token;\n      if (this.options.extensions?.inline?.some((extTokenizer) => {\n        if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === \"text\" && lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach((getStartIndex) => {\n          tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n          if (typeof tempStart === \"number\" && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== \"_\") {\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === \"text\") {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = \"Infinite loop on byte: \" + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    return tokens;\n  }\n};\n\n// src/Renderer.ts\nvar _Renderer = class {\n  options;\n  parser;\n  // set by the parser\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  space(token) {\n    return \"\";\n  }\n  code({ text, lang, escaped }) {\n    const langString = (lang || \"\").match(other.notSpaceStart)?.[0];\n    const code = text.replace(other.endingNewline, \"\") + \"\\n\";\n    if (!langString) {\n      return \"<pre><code>\" + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n    }\n    return '<pre><code class=\"language-' + escape2(langString) + '\">' + (escaped ? code : escape2(code, true)) + \"</code></pre>\\n\";\n  }\n  blockquote({ tokens }) {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\n${body}</blockquote>\n`;\n  }\n  html({ text }) {\n    return text;\n  }\n  heading({ tokens, depth }) {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\n`;\n  }\n  hr(token) {\n    return \"<hr>\\n\";\n  }\n  list(token) {\n    const ordered = token.ordered;\n    const start = token.start;\n    let body = \"\";\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n    const type = ordered ? \"ol\" : \"ul\";\n    const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : \"\";\n    return \"<\" + type + startAttr + \">\\n\" + body + \"</\" + type + \">\\n\";\n  }\n  listitem(item) {\n    let itemBody = \"\";\n    if (item.task) {\n      const checkbox = this.checkbox({ checked: !!item.checked });\n      if (item.loose) {\n        if (item.tokens[0]?.type === \"paragraph\") {\n          item.tokens[0].text = checkbox + \" \" + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === \"text\") {\n            item.tokens[0].tokens[0].text = checkbox + \" \" + escape2(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: \"text\",\n            raw: checkbox + \" \",\n            text: checkbox + \" \",\n            escaped: true\n          });\n        }\n      } else {\n        itemBody += checkbox + \" \";\n      }\n    }\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n    return `<li>${itemBody}</li>\n`;\n  }\n  checkbox({ checked }) {\n    return \"<input \" + (checked ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({ tokens }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>\n`;\n  }\n  table(token) {\n    let header = \"\";\n    let cell = \"\";\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({ text: cell });\n    let body = \"\";\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n      cell = \"\";\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n      body += this.tablerow({ text: cell });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n    return \"<table>\\n<thead>\\n\" + header + \"</thead>\\n\" + body + \"</table>\\n\";\n  }\n  tablerow({ text }) {\n    return `<tr>\n${text}</tr>\n`;\n  }\n  tablecell(token) {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? \"th\" : \"td\";\n    const tag2 = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n    return tag2 + content + `</${type}>\n`;\n  }\n  /**\n   * span level renderer\n   */\n  strong({ tokens }) {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n  em({ tokens }) {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n  codespan({ text }) {\n    return `<code>${escape2(text, true)}</code>`;\n  }\n  br(token) {\n    return \"<br>\";\n  }\n  del({ tokens }) {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n  link({ href, title, tokens }) {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + escape2(title) + '\"';\n    }\n    out += \">\" + text + \"</a>\";\n    return out;\n  }\n  image({ href, title, text, tokens }) {\n    if (tokens) {\n      text = this.parser.parseInline(tokens, this.parser.textRenderer);\n    }\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape2(text);\n    }\n    href = cleanHref;\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape2(title)}\"`;\n    }\n    out += \">\";\n    return out;\n  }\n  text(token) {\n    return \"tokens\" in token && token.tokens ? this.parser.parseInline(token.tokens) : \"escaped\" in token && token.escaped ? token.text : escape2(token.text);\n  }\n};\n\n// src/TextRenderer.ts\nvar _TextRenderer = class {\n  // no need for block level renderers\n  strong({ text }) {\n    return text;\n  }\n  em({ text }) {\n    return text;\n  }\n  codespan({ text }) {\n    return text;\n  }\n  del({ text }) {\n    return text;\n  }\n  html({ text }) {\n    return text;\n  }\n  text({ text }) {\n    return text;\n  }\n  link({ text }) {\n    return \"\" + text;\n  }\n  image({ text }) {\n    return \"\" + text;\n  }\n  br() {\n    return \"\";\n  }\n};\n\n// src/Parser.ts\nvar _Parser = class __Parser {\n  options;\n  renderer;\n  textRenderer;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parse(tokens);\n  }\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options2) {\n    const parser2 = new __Parser(options2);\n    return parser2.parseInline(tokens);\n  }\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken;\n        const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n        if (ret !== false || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(genericToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"space\": {\n          out += this.renderer.space(token);\n          continue;\n        }\n        case \"hr\": {\n          out += this.renderer.hr(token);\n          continue;\n        }\n        case \"heading\": {\n          out += this.renderer.heading(token);\n          continue;\n        }\n        case \"code\": {\n          out += this.renderer.code(token);\n          continue;\n        }\n        case \"table\": {\n          out += this.renderer.table(token);\n          continue;\n        }\n        case \"blockquote\": {\n          out += this.renderer.blockquote(token);\n          continue;\n        }\n        case \"list\": {\n          out += this.renderer.list(token);\n          continue;\n        }\n        case \"html\": {\n          out += this.renderer.html(token);\n          continue;\n        }\n        case \"paragraph\": {\n          out += this.renderer.paragraph(token);\n          continue;\n        }\n        case \"text\": {\n          let textToken = token;\n          let body = this.renderer.text(textToken);\n          while (i + 1 < tokens.length && tokens[i + 1].type === \"text\") {\n            textToken = tokens[++i];\n            body += \"\\n\" + this.renderer.text(textToken);\n          }\n          if (top) {\n            out += this.renderer.paragraph({\n              type: \"paragraph\",\n              raw: body,\n              text: body,\n              tokens: [{ type: \"text\", raw: body, text: body, escaped: true }]\n            });\n          } else {\n            out += body;\n          }\n          continue;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer = this.renderer) {\n    let out = \"\";\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n        if (ret !== false || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(anyToken.type)) {\n          out += ret || \"\";\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case \"escape\": {\n          out += renderer.text(token);\n          break;\n        }\n        case \"html\": {\n          out += renderer.html(token);\n          break;\n        }\n        case \"link\": {\n          out += renderer.link(token);\n          break;\n        }\n        case \"image\": {\n          out += renderer.image(token);\n          break;\n        }\n        case \"strong\": {\n          out += renderer.strong(token);\n          break;\n        }\n        case \"em\": {\n          out += renderer.em(token);\n          break;\n        }\n        case \"codespan\": {\n          out += renderer.codespan(token);\n          break;\n        }\n        case \"br\": {\n          out += renderer.br(token);\n          break;\n        }\n        case \"del\": {\n          out += renderer.del(token);\n          break;\n        }\n        case \"text\": {\n          out += renderer.text(token);\n          break;\n        }\n        default: {\n          const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n          if (this.options.silent) {\n            console.error(errMsg);\n            return \"\";\n          } else {\n            throw new Error(errMsg);\n          }\n        }\n      }\n    }\n    return out;\n  }\n};\n\n// src/Hooks.ts\nvar _Hooks = class {\n  options;\n  block;\n  constructor(options2) {\n    this.options = options2 || _defaults;\n  }\n  static passThroughHooks = /* @__PURE__ */ new Set([\n    \"preprocess\",\n    \"postprocess\",\n    \"processAllTokens\"\n  ]);\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html2) {\n    return html2;\n  }\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens) {\n    return tokens;\n  }\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n};\n\n// src/Instance.ts\nvar Marked = class {\n  defaults = _getDefaults();\n  options = this.setOptions;\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n  constructor(...args) {\n    this.use(...args);\n  }\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens, callback) {\n    let values = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case \"table\": {\n          const tableToken = token;\n          for (const cell of tableToken.header) {\n            values = values.concat(this.walkTokens(cell.tokens, callback));\n          }\n          for (const row of tableToken.rows) {\n            for (const cell of row) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n          }\n          break;\n        }\n        case \"list\": {\n          const listToken = token;\n          values = values.concat(this.walkTokens(listToken.items, callback));\n          break;\n        }\n        default: {\n          const genericToken = token;\n          if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n            this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n              const tokens2 = genericToken[childTokens].flat(Infinity);\n              values = values.concat(this.walkTokens(tokens2, callback));\n            });\n          } else if (genericToken.tokens) {\n            values = values.concat(this.walkTokens(genericToken.tokens, callback));\n          }\n        }\n      }\n    }\n    return values;\n  }\n  use(...args) {\n    const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n    args.forEach((pack) => {\n      const opts = { ...pack };\n      opts.async = this.defaults.async || opts.async || false;\n      if (pack.extensions) {\n        pack.extensions.forEach((ext) => {\n          if (!ext.name) {\n            throw new Error(\"extension name required\");\n          }\n          if (\"renderer\" in ext) {\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              extensions.renderers[ext.name] = function(...args2) {\n                let ret = ext.renderer.apply(this, args2);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args2);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if (\"tokenizer\" in ext) {\n            if (!ext.level || ext.level !== \"block\" && ext.level !== \"inline\") {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) {\n              if (ext.level === \"block\") {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === \"inline\") {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if (\"childTokens\" in ext && ext.childTokens) {\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"parser\"].includes(prop)) {\n            continue;\n          }\n          const rendererProp = prop;\n          const rendererFunc = pack.renderer[rendererProp];\n          const prevRenderer = renderer[rendererProp];\n          renderer[rendererProp] = (...args2) => {\n            let ret = rendererFunc.apply(renderer, args2);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args2);\n            }\n            return ret || \"\";\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if ([\"options\", \"rules\", \"lexer\"].includes(prop)) {\n            continue;\n          }\n          const tokenizerProp = prop;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp];\n          const prevTokenizer = tokenizer[tokenizerProp];\n          tokenizer[tokenizerProp] = (...args2) => {\n            let ret = tokenizerFunc.apply(tokenizer, args2);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args2);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if ([\"options\", \"block\"].includes(prop)) {\n            continue;\n          }\n          const hooksProp = prop;\n          const hooksFunc = pack.hooks[hooksProp];\n          const prevHook = hooks[hooksProp];\n          if (_Hooks.passThroughHooks.has(prop)) {\n            hooks[hooksProp] = (arg) => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then((ret2) => {\n                  return prevHook.call(hooks, ret2);\n                });\n              }\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            hooks[hooksProp] = (...args2) => {\n              let ret = hooksFunc.apply(hooks, args2);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args2);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n      if (pack.walkTokens) {\n        const walkTokens2 = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function(token) {\n          let values = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens2) {\n            values = values.concat(walkTokens2.call(this, token));\n          }\n          return values;\n        };\n      }\n      this.defaults = { ...this.defaults, ...opts };\n    });\n    return this;\n  }\n  setOptions(opt) {\n    this.defaults = { ...this.defaults, ...opt };\n    return this;\n  }\n  lexer(src, options2) {\n    return _Lexer.lex(src, options2 ?? this.defaults);\n  }\n  parser(tokens, options2) {\n    return _Parser.parse(tokens, options2 ?? this.defaults);\n  }\n  parseMarkdown(blockType) {\n    const parse2 = (src, options2) => {\n      const origOpt = { ...options2 };\n      const opt = { ...this.defaults, ...origOpt };\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      }\n      if (typeof src === \"undefined\" || src === null) {\n        return throwError(new Error(\"marked(): input parameter is undefined or null\"));\n      }\n      if (typeof src !== \"string\") {\n        return throwError(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(src) + \", string expected\"));\n      }\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n      const lexer2 = opt.hooks ? opt.hooks.provideLexer() : blockType ? _Lexer.lex : _Lexer.lexInline;\n      const parser2 = opt.hooks ? opt.hooks.provideParser() : blockType ? _Parser.parse : _Parser.parseInline;\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then((src2) => lexer2(src2, opt)).then((tokens) => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then((tokens) => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then((tokens) => parser2(tokens, opt)).then((html2) => opt.hooks ? opt.hooks.postprocess(html2) : html2).catch(throwError);\n      }\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        let tokens = lexer2(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html2 = parser2(tokens, opt);\n        if (opt.hooks) {\n          html2 = opt.hooks.postprocess(html2);\n        }\n        return html2;\n      } catch (e) {\n        return throwError(e);\n      }\n    };\n    return parse2;\n  }\n  onError(silent, async) {\n    return (e) => {\n      e.message += \"\\nPlease report this to https://github.com/markedjs/marked.\";\n      if (silent) {\n        const msg = \"<p>An error occurred:</p><pre>\" + escape2(e.message + \"\", true) + \"</pre>\";\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n};\n\n// src/marked.ts\nvar markedInstance = new Marked();\nfunction marked(src, opt) {\n  return markedInstance.parse(src, opt);\n}\nmarked.options = marked.setOptions = function(options2) {\n  markedInstance.setOptions(options2);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\nmarked.use = function(...args) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\nmarked.walkTokens = function(tokens, callback) {\n  return markedInstance.walkTokens(tokens, callback);\n};\nmarked.parseInline = markedInstance.parseInline;\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nvar options = marked.options;\nvar setOptions = marked.setOptions;\nvar use = marked.use;\nvar walkTokens = marked.walkTokens;\nvar parseInline = marked.parseInline;\nvar parse = marked;\nvar parser = _Parser.parse;\nvar lexer = _Lexer.lex;\n\n//# sourceMappingURL=marked.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/marked/lib/marked.esm.js\n");

/***/ })

};
;