from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication
from .authentication import BearerTokenAuthentication
from .utils import get_usage_stats
from .models import DocumentTimeTracking
from django.utils import timezone
from django.core.cache import cache
import pytz

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def validate_token(request):
    """
    Validate the authentication token and return user information.
    Accepts both Token and Bearer authentication formats.
    """
    user = request.user

    # Log successful token validation for debugging
    import logging
    logger = logging.getLogger(__name__)
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    logger.info(f"Token validation successful for user: {user.username}, auth header: {auth_header[:15]}...")

    return Response({
        "id": user.id,
        "username": user.username,
        "email": user.email
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage_stats(request, user_id):
    """
    Get usage statistics for a user
    """
    # Ensure the requesting user can only access their own stats
    if int(user_id) != request.user.id:
        return Response(
            {"error": "You can only access your own usage statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        stats = get_usage_stats(request.user)
        return Response(stats)
    except Exception as e:
        return Response(
            {"error": f"Error getting usage statistics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def test_auth(request):
    """
    Test authentication between Django and FastAPI servers.

    This endpoint:
    1. Collects information about the current request authentication
    2. Makes a request to the FastAPI test-auth endpoint using the current user's token
    3. Returns both the local authentication info and the FastAPI response

    This helps diagnose authentication issues between the two servers.
    """
    import requests
    from django.conf import settings

    # Get the authentication header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    # Check if the user is authenticated
    is_authenticated = request.user.is_authenticated

    # Prepare the response with local auth info
    response_data = {
        "django_auth_info": {
            "auth_header": {
                "received": bool(auth_header),
                "value": auth_header[:10] + "..." if auth_header and len(auth_header) > 10 else auth_header,
                "format": "Unknown"
            },
            "authentication": {
                "is_authenticated": is_authenticated,
                "auth_method": str(request.auth.__class__.__name__) if request.auth else "None"
            }
        },
        "fastapi_test_results": None
    }

    # Add format information
    if auth_header:
        if auth_header.lower().startswith('token '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Token"
        elif auth_header.lower().startswith('bearer '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Bearer"
        else:
            response_data["django_auth_info"]["auth_header"]["format"] = "Raw (no prefix)"

    # Add user information if authenticated
    if is_authenticated:
        response_data["django_auth_info"]["user"] = {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email
        }

        # Get the token for the current user
        from rest_framework.authtoken.models import Token
        try:
            token, _ = Token.objects.get_or_create(user=request.user)
            token_key = token.key

            # Now test this token with the FastAPI server
            fastapi_url = getattr(settings, 'FASTAPI_URL', 'http://localhost:8001')

            # Test with different auth header formats
            auth_formats = [
                {"name": "Bearer format", "header": f"Bearer {token_key}"},
                {"name": "Token format", "header": f"Token {token_key}"},
                {"name": "Raw token", "header": token_key}
            ]

            fastapi_results = {}

            for auth_format in auth_formats:
                try:
                    # Make request to FastAPI test-auth endpoint
                    fastapi_response = requests.get(
                        f"{fastapi_url}/test-auth",
                        headers={"Authorization": auth_format["header"]},
                        timeout=5.0
                    )

                    if fastapi_response.status_code == 200:
                        fastapi_results[auth_format["name"]] = {
                            "status": "success",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.json()
                        }
                    else:
                        fastapi_results[auth_format["name"]] = {
                            "status": "error",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.text[:100] if fastapi_response.text else "No response body"
                        }
                except Exception as e:
                    fastapi_results[auth_format["name"]] = {
                        "status": "exception",
                        "error": str(e)
                    }

            response_data["fastapi_test_results"] = {
                "token_used": f"{token_key[:5]}...",
                "fastapi_url": fastapi_url,
                "results": fastapi_results
            }

        except Exception as e:
            response_data["fastapi_test_results"] = {
                "status": "error",
                "message": f"Error testing with FastAPI: {str(e)}"
            }

    return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Logout endpoint that deletes the user's auth token.
    Compatible with frontend API calls to /users/logout/
    """
    try:
        # Delete the user's token
        from rest_framework.authtoken.models import Token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass  # Token doesn't exist, which is fine

        return Response({
            "message": "Successfully logged out"
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "error": f"Error during logout: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Optimized Document Time Tracking API

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def start_document_timer(request):
    """
    Optimized timer start - minimal database operations with caching.
    Returns session start time for frontend tracking.
    """
    try:
        file_name = request.data.get('file_name')

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Get or create tracking record (uses caching internally)
        tracking = DocumentTimeTracking.get_or_create_tracking(username, file_name)

        # Store session start in cache for efficient tracking
        session_cache_key = f"timer_session_{username}_{file_name}"
        indian_tz = DocumentTimeTracking.get_indian_timezone()
        session_start = timezone.now().astimezone(indian_tz)

        cache.set(session_cache_key, {
            'start_time': session_start.isoformat(),
            'tracking_id': tracking.id,
            'is_active': True
        }, timeout=86400)  # Cache for 24 hours

        return Response({
            'message': 'Timer started successfully',
            'session_start': session_start.isoformat(),
            'tracking_id': tracking.id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error starting timer: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def stop_document_timer(request):
    """
    Optimized timer stop - uses cached session data and atomic updates.
    Adds the session time to total time.
    """
    try:
        file_name = request.data.get('file_name')
        session_time_seconds = request.data.get('session_time_seconds', 0)

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username
        session_cache_key = f"timer_session_{username}_{file_name}"

        # Get session data from cache
        session_data = cache.get(session_cache_key)

        if not session_data:
            # Fallback: try to get tracking record directly
            try:
                tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
            except DocumentTimeTracking.DoesNotExist:
                return Response({
                    'error': 'No active session or tracking record found for this file'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # Get tracking record using cached ID
            try:
                tracking = DocumentTimeTracking.objects.get(id=session_data['tracking_id'])
            except DocumentTimeTracking.DoesNotExist:
                return Response({
                    'error': 'Tracking record not found'
                }, status=status.HTTP_404_NOT_FOUND)

        # Add session time to total using optimized method
        # Convert to int if it's a string
        try:
            session_time_seconds = int(session_time_seconds)
        except (ValueError, TypeError):
            session_time_seconds = 0

        if session_time_seconds > 0:
            tracking.add_session_time(session_time_seconds)

        # Clear session cache
        cache.delete(session_cache_key)

        return Response({
            'message': 'Timer stopped successfully',
            'total_time_seconds': tracking.total_time_seconds,
            'total_time_formatted': tracking.total_time_formatted,
            'number_of_sessions': tracking.number_of_sessions
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error stopping timer: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def increment_quiz_count(request):
    """
    Increment quiz count for a document.
    """
    try:
        file_name = request.data.get('file_name')

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Get tracking record
        try:
            tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
        except DocumentTimeTracking.DoesNotExist:
            return Response({
                'error': 'No tracking record found for this file'
            }, status=status.HTTP_404_NOT_FOUND)

        # Increment quiz count
        tracking.increment_quiz_count()

        return Response({
            'message': 'Quiz count incremented successfully',
            'number_of_quizzes': tracking.number_of_quizzes
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error incrementing quiz count: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_document_stats(request):
    """
    Get time tracking stats for a specific document or all documents.
    """
    try:
        file_name = request.GET.get('file_name')
        username = request.user.username

        if file_name:
            # Get stats for specific file
            try:
                tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
                return Response({
                    'username': tracking.username,
                    'file_name': tracking.file_name,
                    'total_time_seconds': tracking.total_time_seconds,
                    'total_time_formatted': tracking.total_time_formatted,
                    'number_of_sessions': tracking.number_of_sessions,
                    'number_of_quizzes': tracking.number_of_quizzes,
                    'average_session_time': tracking.average_session_time,
                    'last_accessed': tracking.last_accessed.isoformat(),
                    'created_at': tracking.created_at.isoformat()
                }, status=status.HTTP_200_OK)
            except DocumentTimeTracking.DoesNotExist:
                return Response({
                    'error': 'No tracking record found for this file'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # Get stats for all files
            trackings = DocumentTimeTracking.objects.filter(username=username).order_by('-last_accessed')

            stats_data = []
            for tracking in trackings:
                stats_data.append({
                    'username': tracking.username,
                    'file_name': tracking.file_name,
                    'total_time_seconds': tracking.total_time_seconds,
                    'total_time_formatted': tracking.total_time_formatted,
                    'number_of_sessions': tracking.number_of_sessions,
                    'number_of_quizzes': tracking.number_of_quizzes,
                    'average_session_time': tracking.average_session_time,
                    'last_accessed': tracking.last_accessed.isoformat(),
                    'created_at': tracking.created_at.isoformat()
                })

            # Calculate totals
            total_time = sum(t.total_time_seconds for t in trackings)
            total_sessions = sum(t.number_of_sessions for t in trackings)
            total_quizzes = sum(t.number_of_quizzes for t in trackings)

            return Response({
                'files': stats_data,
                'summary': {
                    'total_files': trackings.count(),
                    'total_time_seconds': total_time,
                    'total_sessions': total_sessions,
                    'total_quizzes': total_quizzes
                }
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def batch_update_timer_sessions(request):
    """
    Efficiently batch update multiple timer sessions.
    Useful for updating multiple sessions at once to reduce API calls.
    """
    try:
        sessions = request.data.get('sessions', [])

        # Handle case where sessions might be a string (JSON parsing issue)
        if isinstance(sessions, str):
            import json
            try:
                sessions = json.loads(sessions)
            except json.JSONDecodeError:
                return Response({
                    'error': 'Invalid sessions data format'
                }, status=status.HTTP_400_BAD_REQUEST)

        if not sessions or not isinstance(sessions, list):
            return Response({
                'error': 'sessions list is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Validate and prepare session data
        session_data_list = []
        for session in sessions:
            if not isinstance(session, dict):
                continue

            file_name = session.get('file_name')
            time_seconds = session.get('time_seconds', 0)

            if not file_name or time_seconds <= 0:
                continue

            session_data_list.append({
                'username': username,
                'file_name': file_name,
                'time_seconds': time_seconds
            })

        if not session_data_list:
            return Response({
                'error': 'No valid sessions to update'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Perform batch update
        DocumentTimeTracking.batch_update_sessions(session_data_list)

        return Response({
            'message': f'Successfully updated {len(session_data_list)} sessions',
            'updated_sessions': len(session_data_list)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error batch updating sessions: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def heartbeat_timer(request):
    """
    Lightweight heartbeat endpoint to keep session alive.
    Updates last_accessed without heavy database operations.
    """
    try:
        file_name = request.data.get('file_name')

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username
        session_cache_key = f"timer_session_{username}_{file_name}"

        # Check if session exists in cache
        session_data = cache.get(session_cache_key)

        if session_data:
            # Update session timestamp in cache
            indian_tz = DocumentTimeTracking.get_indian_timezone()
            session_data['last_heartbeat'] = timezone.now().astimezone(indian_tz).isoformat()
            cache.set(session_cache_key, session_data, timeout=86400)

            return Response({
                'message': 'Heartbeat received',
                'session_active': True
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'message': 'No active session found',
                'session_active': False
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error processing heartbeat: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

