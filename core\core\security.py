from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils.translation import gettext_lazy as _
import re
from rest_framework.exceptions import Throttled
from rest_framework.throttling import UserRateThrottle

class InputValidator:
    @staticmethod
    def validate_email(email):
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False

    @staticmethod
    def validate_password(password):
        if len(password) < 8:
            return False, _("Password must be at least 8 characters long")
        if not re.search(r'[A-Z]', password):
            return False, _("Password must contain at least one uppercase letter")
        if not re.search(r'[a-z]', password):
            return False, _("Password must contain at least one lowercase letter")
        if not re.search(r'[0-9]', password):
            return False, _("Password must contain at least one number")
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, _("Password must contain at least one special character")
        return True, None

    @staticmethod
    def sanitize_input(input_str):
        # Remove potentially harmful characters
        return re.sub(r'[<>"\']', '', input_str)

class CustomUserRateThrottle(UserRateThrottle):
    def throttle_failure(self):
        wait = self.wait()
        detail = {
            "message": _("Request was throttled"),
            "available_in": f"{wait} seconds",
            "rate": self.rate
        }
        raise Throttled(detail=detail)

class SecurityHeaders:
    @staticmethod
    def get_headers():
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'",
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        } 