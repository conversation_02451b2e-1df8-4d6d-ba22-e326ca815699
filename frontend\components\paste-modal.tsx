"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Youtube, Globe, FileText } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useRouter } from "next/navigation"

interface PasteModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

export function PasteModal({ isOpen, setIsOpen }: PasteModalProps) {
  const [youtubeUrl, setYoutubeUrl] = useState("")
  const [websiteUrl, setWebsiteUrl] = useState("")
  const [textContent, setTextContent] = useState("")
  const [activeTab, setActiveTab] = useState("youtube")
  const router = useRouter()

  const handleSubmit = () => {
    // Store the content in localStorage to simulate persistence
    if (activeTab === "youtube" && youtubeUrl) {
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "youtube",
          url: youtubeUrl,
        }),
      )
    } else if (activeTab === "website" && websiteUrl) {
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "website",
          url: websiteUrl,
        }),
      )
    } else if (activeTab === "text" && textContent) {
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "text",
          content: textContent,
        }),
      )
    }

    setIsOpen(false)
    router.push("/process?type=paste")
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-center">Paste Content</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="youtube" className="mt-4" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="youtube" className="data-[state=active]:bg-purple-600">
              <Youtube className="h-4 w-4 mr-2" />
              YouTube
            </TabsTrigger>
            <TabsTrigger value="website" className="data-[state=active]:bg-purple-600">
              <Globe className="h-4 w-4 mr-2" />
              Website
            </TabsTrigger>
            <TabsTrigger value="text" className="data-[state=active]:bg-purple-600">
              <FileText className="h-4 w-4 mr-2" />
              Text
            </TabsTrigger>
          </TabsList>

          <TabsContent value="youtube" className="space-y-4">
            <div>
              <label className="text-sm text-neutral-400 mb-2 block">Paste YouTube URL</label>
              <Input
                placeholder="https://www.youtube.com/watch?v=..."
                value={youtubeUrl}
                onChange={(e) => setYoutubeUrl(e.target.value)}
                className="bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
              />
            </div>
            <p className="text-xs text-neutral-500">Enter a YouTube video URL to extract and analyze its content</p>
          </TabsContent>

          <TabsContent value="website" className="space-y-4">
            <div>
              <label className="text-sm text-neutral-400 mb-2 block">Paste Website URL</label>
              <Input
                placeholder="https://example.com"
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className="bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
              />
            </div>
            <p className="text-xs text-neutral-500">Enter a website URL to extract and analyze its content</p>
          </TabsContent>

          <TabsContent value="text" className="space-y-4">
            <div>
              <label className="text-sm text-neutral-400 mb-2 block">Paste Text Content</label>
              <Textarea
                placeholder="Paste or type your text here..."
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                className="min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
              />
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button
            className="bg-purple-600 hover:bg-purple-700"
            onClick={handleSubmit}
            disabled={
              (activeTab === "youtube" && !youtubeUrl) ||
              (activeTab === "website" && !websiteUrl) ||
              (activeTab === "text" && !textContent)
            }
          >
            Process Content
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
