# Generated by Django 4.2.21 on 2025-07-23 17:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0014_quizattempt_quizanswer_and_more'),
        ('users', '0002_student_email_verification_sent_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CMSScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quiz_score_percentage', models.DecimalField(decimal_places=2, help_text='Original quiz score as percentage (0-100)', max_digits=5)),
                ('quiz_score_component', models.DecimalField(decimal_places=2, help_text='Quiz score scaled to 70 points (0-70)', max_digits=5)),
                ('quiz_performance_category', models.CharField(choices=[('excellent', 'Quiz ≥ 70%'), ('moderate', 'Quiz 40-69%'), ('poor', 'Quiz < 40%')], help_text='Quiz performance category based on score', max_length=20)),
                ('time_spent_seconds', models.PositiveIntegerField(help_text='Total time spent learning this document')),
                ('average_time_seconds', models.PositiveIntegerField(help_text='Predicted average learning time for this document')),
                ('time_ratio', models.DecimalField(decimal_places=2, help_text='Ratio of time spent to average time (time_spent/average_time)', max_digits=5)),
                ('time_category', models.CharField(choices=[('high', 'High - More time than average'), ('average', 'Average - Around average time'), ('low', 'Low - Less time than average')], help_text='Time category based on ratio to average', max_length=20)),
                ('time_component_points', models.IntegerField(help_text='Points awarded for time efficiency (-10 to +30)')),
                ('cms_score', models.DecimalField(decimal_places=2, help_text='Final CMS score out of 100', max_digits=5)),
                ('user_zone', models.CharField(choices=[('mastered', 'Mastered (90-100)'), ('stable', 'Stable (75-89)'), ('moderate_gap', 'Moderate Gap (60-74)'), ('weak', 'Weak (40-59)'), ('unclear', 'Unclear (< 40)')], help_text='User zone classification based on CMS score', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cms_scores', to='documents.document')),
                ('quiz_attempt', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='cms_score', to='documents.quizattempt')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cms_scores', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'CMS Score',
                'verbose_name_plural': 'CMS Scores',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['student', 'document'], name='users_cmssc_student_dc8ce4_idx'), models.Index(fields=['cms_score'], name='users_cmssc_cms_sco_32346b_idx'), models.Index(fields=['user_zone'], name='users_cmssc_user_zo_fe11b6_idx'), models.Index(fields=['created_at'], name='users_cmssc_created_2e13f7_idx')],
            },
        ),
    ]
