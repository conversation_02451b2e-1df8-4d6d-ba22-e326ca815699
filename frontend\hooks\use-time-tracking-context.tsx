'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { timeTrackingApi } from '@/lib/api';
import { TimeTrackingSession, TimeTrackingStats } from './use-time-tracking';

interface TimeTrackingContextType {
  // Global state
  activeSessions: Map<number, TimeTrackingSession>;
  documentStats: Map<number, TimeTrackingStats>;
  isGlobalLoading: boolean;
  globalError: string | null;
  
  // Global actions
  pauseAllSessions: (reason?: string) => Promise<void>;
  resumeAllSessions: () => Promise<void>;
  endAllSessions: () => Promise<void>;
  getUserOverview: () => Promise<any>;
  refreshDocumentStats: (documentId: number) => Promise<void>;
  
  // Session management
  registerSession: (documentId: number, session: TimeTrackingSession) => void;
  unregisterSession: (documentId: number) => void;
  updateSession: (documentId: number, session: TimeTrackingSession) => void;
  
  // Quiz integration
  notifyQuizStart: (documentId: number) => Promise<void>;
  notifyQuizEnd: (documentId: number) => Promise<void>;
}

const TimeTrackingContext = createContext<TimeTrackingContextType | undefined>(undefined);

export const useTimeTrackingContext = () => {
  const context = useContext(TimeTrackingContext);
  if (!context) {
    throw new Error('useTimeTrackingContext must be used within a TimeTrackingProvider');
  }
  return context;
};

interface TimeTrackingProviderProps {
  children: React.ReactNode;
}

export const TimeTrackingProvider: React.FC<TimeTrackingProviderProps> = ({ children }) => {
  const [activeSessions, setActiveSessions] = useState<Map<number, TimeTrackingSession>>(new Map());
  const [documentStats, setDocumentStats] = useState<Map<number, TimeTrackingStats>>(new Map());
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const [globalError, setGlobalError] = useState<string | null>(null);

  // Register a new session
  const registerSession = useCallback((documentId: number, session: TimeTrackingSession) => {
    setActiveSessions(prev => new Map(prev.set(documentId, session)));
  }, []);

  // Unregister a session
  const unregisterSession = useCallback((documentId: number) => {
    setActiveSessions(prev => {
      const newMap = new Map(prev);
      newMap.delete(documentId);
      return newMap;
    });
  }, []);

  // Update an existing session
  const updateSession = useCallback((documentId: number, session: TimeTrackingSession) => {
    setActiveSessions(prev => {
      if (prev.has(documentId)) {
        return new Map(prev.set(documentId, session));
      }
      return prev;
    });
  }, []);

  // Pause all active sessions
  const pauseAllSessions = useCallback(async (reason: string = 'Global pause') => {
    setIsGlobalLoading(true);
    setGlobalError(null);

    try {
      const pausePromises = Array.from(activeSessions.entries())
        .filter(([_, session]) => session.status === 'active')
        .map(async ([documentId, session]) => {
          try {
            await timeTrackingApi.pauseSession(session.id, reason);
            const updatedSession = { ...session, status: 'paused' as const };
            updateSession(documentId, updatedSession);
            return { documentId, success: true };
          } catch (error) {
            console.error(`Failed to pause session for document ${documentId}:`, error);
            return { documentId, success: false, error };
          }
        });

      await Promise.all(pausePromises);
    } catch (error: any) {
      setGlobalError('Failed to pause some sessions');
      console.error('Error pausing all sessions:', error);
    } finally {
      setIsGlobalLoading(false);
    }
  }, [activeSessions, updateSession]);

  // Resume all paused sessions
  const resumeAllSessions = useCallback(async () => {
    setIsGlobalLoading(true);
    setGlobalError(null);

    try {
      const resumePromises = Array.from(activeSessions.entries())
        .filter(([_, session]) => session.status === 'paused')
        .map(async ([documentId, session]) => {
          try {
            await timeTrackingApi.resumeSession(session.id);
            const updatedSession = { ...session, status: 'active' as const };
            updateSession(documentId, updatedSession);
            return { documentId, success: true };
          } catch (error) {
            console.error(`Failed to resume session for document ${documentId}:`, error);
            return { documentId, success: false, error };
          }
        });

      await Promise.all(resumePromises);
    } catch (error: any) {
      setGlobalError('Failed to resume some sessions');
      console.error('Error resuming all sessions:', error);
    } finally {
      setIsGlobalLoading(false);
    }
  }, [activeSessions, updateSession]);

  // End all active sessions
  const endAllSessions = useCallback(async () => {
    setIsGlobalLoading(true);
    setGlobalError(null);

    try {
      const endPromises = Array.from(activeSessions.entries())
        .filter(([_, session]) => session.status === 'active' || session.status === 'paused')
        .map(async ([documentId, session]) => {
          try {
            const response = await timeTrackingApi.endSession(session.id);
            unregisterSession(documentId);
            
            // Update document stats
            if (response.stats) {
              setDocumentStats(prev => new Map(prev.set(documentId, response.stats)));
            }
            
            return { documentId, success: true };
          } catch (error) {
            console.error(`Failed to end session for document ${documentId}:`, error);
            return { documentId, success: false, error };
          }
        });

      await Promise.all(endPromises);
    } catch (error: any) {
      setGlobalError('Failed to end some sessions');
      console.error('Error ending all sessions:', error);
    } finally {
      setIsGlobalLoading(false);
    }
  }, [activeSessions, unregisterSession]);

  // Get user overview
  const getUserOverview = useCallback(async () => {
    setIsGlobalLoading(true);
    setGlobalError(null);

    try {
      const response = await timeTrackingApi.getUserOverview();
      
      // Update active sessions from server
      if (response.active_sessions) {
        const newActiveSessions = new Map();
        response.active_sessions.forEach((session: TimeTrackingSession) => {
          newActiveSessions.set(session.document, session);
        });
        setActiveSessions(newActiveSessions);
      }
      
      // Update document stats
      if (response.document_stats) {
        const newDocumentStats = new Map();
        response.document_stats.forEach((stats: TimeTrackingStats & { document: number }) => {
          newDocumentStats.set(stats.document, stats);
        });
        setDocumentStats(newDocumentStats);
      }
      
      return response;
    } catch (error: any) {
      setGlobalError('Failed to get user overview');
      console.error('Error getting user overview:', error);
      throw error;
    } finally {
      setIsGlobalLoading(false);
    }
  }, []);

  // Refresh document stats
  const refreshDocumentStats = useCallback(async (documentId: number) => {
    try {
      const response = await timeTrackingApi.getDocumentStats(documentId);
      setDocumentStats(prev => new Map(prev.set(documentId, response.stats)));
    } catch (error) {
      console.error(`Failed to refresh stats for document ${documentId}:`, error);
    }
  }, []);

  // Quiz integration - pause session when quiz starts
  const notifyQuizStart = useCallback(async (documentId: number) => {
    const session = activeSessions.get(documentId);
    if (session && session.status === 'active') {
      try {
        await timeTrackingApi.pauseSession(session.id, 'Taking quiz');
        const updatedSession = { ...session, status: 'paused' as const };
        updateSession(documentId, updatedSession);
      } catch (error) {
        console.error(`Failed to pause session for quiz start on document ${documentId}:`, error);
      }
    }
  }, [activeSessions, updateSession]);

  // Quiz integration - resume session when quiz ends
  const notifyQuizEnd = useCallback(async (documentId: number) => {
    const session = activeSessions.get(documentId);
    if (session && session.status === 'paused') {
      try {
        await timeTrackingApi.resumeSession(session.id);
        const updatedSession = { ...session, status: 'active' as const };
        updateSession(documentId, updatedSession);
      } catch (error) {
        console.error(`Failed to resume session for quiz end on document ${documentId}:`, error);
      }
    }
  }, [activeSessions, updateSession]);

  // Load initial data on mount
  useEffect(() => {
    getUserOverview().catch(console.error);
  }, [getUserOverview]);

  // Handle page visibility change - pause all sessions when page is hidden
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden - don't pause sessions as they should continue running
        console.log('Page hidden - sessions continue running');
      } else {
        // Page is visible again - refresh overview to sync state
        getUserOverview().catch(console.error);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [getUserOverview]);

  // Handle beforeunload - end all sessions gracefully
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Use sendBeacon for reliable delivery
      activeSessions.forEach((session, documentId) => {
        if (session.status === 'active' || session.status === 'paused') {
          const data = JSON.stringify({ session_id: session.id });
          navigator.sendBeacon('/api/users/time-tracking/end-session/', data);
        }
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [activeSessions]);

  const value: TimeTrackingContextType = {
    // State
    activeSessions,
    documentStats,
    isGlobalLoading,
    globalError,
    
    // Global actions
    pauseAllSessions,
    resumeAllSessions,
    endAllSessions,
    getUserOverview,
    refreshDocumentStats,
    
    // Session management
    registerSession,
    unregisterSession,
    updateSession,
    
    // Quiz integration
    notifyQuizStart,
    notifyQuizEnd,
  };

  return (
    <TimeTrackingContext.Provider value={value}>
      {children}
    </TimeTrackingContext.Provider>
  );
};
