"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Clock, CheckCircle, XCircle, RotateCcw, Trophy, Target, Zap, BookOpen } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { quizApi, performanceApi } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import { calculateQuizScore, formatDuration, getPerformanceLevel, QuizSubmissionResponse } from "@/lib/quiz-utils"
import { QuizFeedbackComponent } from "@/components/quiz-feedback"
// import { useTimeTrackingContext } from "@/hooks/use-time-tracking-context"

interface QuizQuestion {
  id?: number
  question: string
  answer: string
}

interface QuizInterfaceProps {
  documentId?: number
  onQuizStart?: () => void
  onQuizEnd?: () => void
}

export function QuizInterface({ documentId, onQuizStart, onQuizEnd }: QuizInterfaceProps) {
  const [questions, setQuestions] = useState<QuizQuestion[]>([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [userAnswers, setUserAnswers] = useState<string[]>([])
  const [isQuizStarted, setIsQuizStarted] = useState(false)
  const [isQuizCompleted, setIsQuizCompleted] = useState(false)
  const [startTime, setStartTime] = useState<number>(0)
  const [timeElapsed, setTimeElapsed] = useState<number>(0)
  const [score, setScore] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [feedbackData, setFeedbackData] = useState<QuizSubmissionResponse | null>(null)
  const [showFeedback, setShowFeedback] = useState(false)

  // Time tracking integration for pausing/resuming study time during quiz
  // Using props passed from parent component

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isQuizStarted && !isQuizCompleted) {
      interval = setInterval(() => {
        setTimeElapsed(Date.now() - startTime)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isQuizStarted, isQuizCompleted, startTime])

  useEffect(() => {
    if (documentId) {
      loadQuiz()
    }
  }, [documentId])

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const loadQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.getOrGenerateQuiz(documentId)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))

        if (response.generated) {
          toast({
            title: "Success",
            description: "Quiz generated successfully!",
          })
        } else {
          toast({
            title: "Quiz Loaded",
            description: "Existing quiz loaded successfully!",
          })
        }
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error loading/generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to load or generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const generateQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz generation",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.generateQuiz(documentId, 5)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))
        toast({
          title: "Success",
          description: "New quiz generated successfully!",
        })
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const startQuiz = async () => {
    setIsQuizStarted(true)
    setStartTime(Date.now())
    setTimeElapsed(0)

    // Pause time tracking when quiz starts
    try {
      onQuizStart?.()
    } catch (error) {
      console.error('Failed to pause time tracking for quiz:', error)
    }
  }

  const handleAnswerChange = (answer: string) => {
    const newAnswers = [...userAnswers]
    newAnswers[currentQuestionIndex] = answer
    setUserAnswers(newAnswers)
  }

  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const calculateScore = () => {
    return calculateQuizScore(questions, userAnswers)
  }

  const submitQuiz = async () => {
    setLoading(true)
    const timeTakenSeconds = Math.floor(timeElapsed / 1000)

    // Resume time tracking when quiz ends
    try {
      onQuizEnd?.()
    } catch (error) {
      console.error('Failed to resume time tracking after quiz:', error)
    }

    try {
      if (!documentId) {
        throw new Error('No document ID available')
      }

      // Prepare answers for submission
      const answers = questions.map((question, index) => ({
        quiz_question_id: question.id || index + 1, // Use question ID if available, fallback to index
        user_answer: userAnswers[index] || ''
      }))

      // Debug logging
      console.log('Questions data:', questions)
      console.log('Prepared answers:', answers)
      console.log('Time taken seconds:', timeTakenSeconds)

      // Submit quiz with feedback
      const response = await quizApi.submitQuiz(documentId, answers, timeTakenSeconds)

      // Debug log the response to see if recommendations are included
      console.log('Quiz submission response:', response)
      if (response.recommendations) {
        console.log('CMS Recommendations received:', response.recommendations)
      } else {
        console.log('No CMS recommendations in response')
      }

      setFeedbackData(response)
      setScore(response.overall_score)
      setShowFeedback(true)
      setIsQuizCompleted(true)

      toast({
        title: "Quiz Submitted!",
        description: `Your score: ${Math.round(response.overall_score)}% - Feedback generated successfully`,
      })

      // Also save to performance API for backward compatibility
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        if (user.id && documentId) {
          await performanceApi.createPerformance({
            student: user.id,
            document: documentId,
            quiz_score: response.overall_score,
            time_taken: timeTakenSeconds,
            remarks: `Quiz completed with ${Math.round(response.overall_score)}% score in ${formatTime(timeElapsed)}`
          })
        }
      } catch (perfError) {
        console.error('Error saving to performance API:', perfError)
        // Don't show error to user as main submission was successful
      }

    } catch (error) {
      console.error('Error submitting quiz:', error)

      // Fallback to old scoring method
      const fallbackScore = calculateScore()
      setScore(fallbackScore)
      setIsQuizCompleted(true)

      toast({
        title: "Quiz Completed!",
        description: `Your score: ${Math.round(fallbackScore)}% - Note: Detailed feedback could not be generated`,
        variant: "destructive",
      })

      // Try to save performance even if feedback failed
      try {
        const user = JSON.parse(localStorage.getItem('user') || '{}')
        if (user.id && documentId) {
          await performanceApi.createPerformance({
            student: user.id,
            document: documentId,
            quiz_score: fallbackScore,
            time_taken: timeTakenSeconds,
            remarks: `Quiz completed with ${Math.round(fallbackScore)}% score in ${formatTime(timeElapsed)}`
          })
        }
      } catch (perfError) {
        console.error('Error saving performance:', perfError)
      }
    } finally {
      setLoading(false)
    }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setUserAnswers(new Array(questions.length).fill(''))
    setIsQuizStarted(false)
    setIsQuizCompleted(false)
    setStartTime(0)
    setTimeElapsed(0)
    setScore(0)
    setFeedbackData(null)
    setShowFeedback(false)
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border border-border bg-card">
                <CardHeader className="text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="mx-auto mb-4"
                  >
                    <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                      <BookOpen className="h-8 w-8 text-primary-foreground" />
                    </div>
                  </motion.div>
                  <CardTitle className="text-2xl text-foreground">
                    Quiz Center
                  </CardTitle>
                  <CardDescription className="text-base">
                    {!documentId
                      ? "Select a document to view or generate a quiz"
                      : generating
                        ? "Creating your personalized quiz..."
                        : "Ready to test your knowledge? Generate a quiz based on your document content."
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {generating ? (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex flex-col items-center justify-center py-8 space-y-4"
                    >
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-muted border-t-primary"></div>
                      </div>
                      <motion.p
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="text-sm text-muted-foreground"
                      >
                        Analyzing your document and crafting questions...
                      </motion.p>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="grid gap-3"
                    >
                      <Button
                        onClick={loadQuiz}
                        disabled={!documentId}
                        variant="outline"
                        className="w-full h-12"
                        size="lg"
                      >
                        <Target className="h-5 w-5 mr-2" />
                        Load Existing Quiz
                      </Button>
                      <Button
                        onClick={generateQuiz}
                        disabled={!documentId}
                        className="w-full h-12"
                        size="lg"
                      >
                        <Zap className="h-5 w-5 mr-2" />
                        Generate New Quiz
                      </Button>
                    </motion.div>
                  )}
                  {!documentId && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                      className="text-sm text-muted-foreground mt-4 text-center bg-muted/50 p-3 rounded-lg border"
                    >
                      ⚠️ Please select a document to generate a quiz.
                    </motion.p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </ScrollArea>
      </div>
    )
  }

  if (isQuizCompleted) {
    // Show detailed feedback if available, otherwise show simple completion
    if (showFeedback && feedbackData) {
      return (
        <QuizFeedbackComponent
          feedbackData={feedbackData}
          onRetakeQuiz={resetQuiz}
          onNewQuiz={generateQuiz}
        />
      )
    }

    // Fallback to simple completion screen
    const performanceLevel = getPerformanceLevel(score)

    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border border-border bg-card">
                <CardHeader className="text-center">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="mx-auto mb-4"
                  >
                    <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                      <Trophy className="h-10 w-10 text-primary-foreground" />
                    </div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <CardTitle className="text-3xl text-foreground">
                      🎉 Quiz Completed!
                    </CardTitle>
                    <CardDescription className={`text-lg font-medium ${performanceLevel.color}`}>
                      {performanceLevel.level} - {performanceLevel.description}
                    </CardDescription>
                  </motion.div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="grid grid-cols-2 gap-6"
                  >
                    <div className="text-center p-4 bg-muted/30 rounded-xl border border-border">
                      <motion.p
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                        className={`text-4xl font-bold ${performanceLevel.color} mb-2`}
                      >
                        {Math.round(score)}%
                      </motion.p>
                      <p className="text-sm text-muted-foreground font-medium">Your Score</p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-xl border border-border">
                      <motion.p
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.6, type: "spring", stiffness: 300 }}
                        className="text-4xl font-bold text-foreground mb-2"
                      >
                        {formatTime(timeElapsed)}
                      </motion.p>
                      <p className="text-sm text-muted-foreground font-medium">Time Taken</p>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                    className="flex gap-3"
                  >
                    <Button
                      onClick={resetQuiz}
                      variant="outline"
                      className="flex-1 h-12 border-2 border-border hover:border-primary hover:bg-muted transition-all duration-200"
                      size="lg"
                    >
                      <RotateCcw className="h-5 w-5 mr-2" />
                      Retake Quiz
                    </Button>
                    <Button
                      onClick={generateQuiz}
                      className="flex-1 h-12"
                      size="lg"
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      New Quiz
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </ScrollArea>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="p-6 space-y-6">
          {/* Timer and Progress */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-muted/50 p-4 rounded-lg border"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                  <Clock className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="font-mono text-xl font-bold text-foreground">
                  {formatTime(timeElapsed)}
                </span>
              </div>
              <div className="text-sm font-medium bg-background px-3 py-1 rounded-full border">
                Question {currentQuestionIndex + 1} of {questions.length}
              </div>
            </div>
            <Progress
              value={((currentQuestionIndex + 1) / questions.length) * 100}
              className="h-3 bg-muted"
            />
          </motion.div>

          {/* Quiz Content */}
          <AnimatePresence mode="wait">
            {!isQuizStarted ? (
              <motion.div
                key="start-screen"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border border-border bg-card">
                  <CardHeader className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="mx-auto mb-4"
                    >
                      <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                        <Target className="h-8 w-8 text-primary-foreground" />
                      </div>
                    </motion.div>
                    <CardTitle className="text-2xl text-foreground">
                      Ready to Start?
                    </CardTitle>
                    <CardDescription className="text-lg">
                      You have {questions.length} questions to answer. Take your time and good luck! 🍀
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      onClick={startQuiz}
                      className="w-full h-12"
                      size="lg"
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      Start Quiz
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              <motion.div
                key={`question-${currentQuestionIndex}`}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border border-border bg-card">
                  <CardHeader>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      <CardTitle className="text-xl text-foreground">
                        Question {currentQuestionIndex + 1}
                      </CardTitle>
                    </motion.div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="bg-muted/30 p-4 rounded-lg border border-border"
                    >
                      <p className="text-lg leading-relaxed">{questions[currentQuestionIndex].question}</p>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <Textarea
                        placeholder="Type your detailed answer here... 📝"
                        value={userAnswers[currentQuestionIndex]}
                        onChange={(e) => handleAnswerChange(e.target.value)}
                        className="min-h-[120px] text-base border-2 border-border focus:border-primary bg-background"
                      />
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex justify-between gap-3"
                    >
                      <Button
                        onClick={previousQuestion}
                        disabled={currentQuestionIndex === 0}
                        variant="outline"
                        className="h-11 border-2 border-border hover:border-primary disabled:opacity-50"
                        size="lg"
                      >
                        ← Previous
                      </Button>

                      {currentQuestionIndex === questions.length - 1 ? (
                        <Button
                          onClick={submitQuiz}
                          disabled={loading || !userAnswers[currentQuestionIndex].trim()}
                          className="h-11"
                          size="lg"
                        >
                          {loading ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                              Submitting...
                            </>
                          ) : (
                            <>
                              <Trophy className="h-5 w-5 mr-2" />
                              Submit Quiz
                            </>
                          )}
                        </Button>
                      ) : (
                        <Button
                          onClick={nextQuestion}
                          disabled={!userAnswers[currentQuestionIndex].trim()}
                          className="h-11"
                          size="lg"
                        >
                          Next →
                        </Button>
                      )}
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </ScrollArea>
    </div>
  )
}
