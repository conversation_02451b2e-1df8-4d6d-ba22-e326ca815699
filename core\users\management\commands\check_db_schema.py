from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Check database schema for foreign key constraints'

    def handle(self, *args, **options):
        self.stdout.write("🔍 Checking database schema and foreign key constraints...")
        self.stdout.write("=" * 60)
        
        with connection.cursor() as cursor:
            # Check if our tables exist
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name LIKE '%platform%' OR name LIKE '%document%'
                ORDER BY name;
            """)
            tables = cursor.fetchall()
            
            self.stdout.write("📋 Tables found:")
            for table in tables:
                self.stdout.write(f"   - {table[0]}")
            
            # Check foreign key constraints for platform time tables
            platform_tables = [t[0] for t in tables if 'platform' in t[0]]
            
            for table in platform_tables:
                self.stdout.write(f"\n🔗 Foreign key constraints for {table}:")
                cursor.execute(f"PRAGMA foreign_key_list({table});")
                fks = cursor.fetchall()
                
                if fks:
                    for fk in fks:
                        self.stdout.write(f"   - Column: {fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[5]})")
                else:
                    self.stdout.write("   - No foreign key constraints found")
            
            # Check if foreign keys are enabled
            cursor.execute("PRAGMA foreign_keys;")
            fk_enabled = cursor.fetchone()[0]
            self.stdout.write(f"\n⚙️  Foreign keys enabled: {'Yes' if fk_enabled else 'No'}")
            
            # Check for any constraint violations
            self.stdout.write(f"\n🔍 Checking for foreign key violations...")
            cursor.execute("PRAGMA foreign_key_check;")
            violations = cursor.fetchall()
            
            if violations:
                self.stdout.write(f"❌ Found {len(violations)} foreign key violations:")
                for violation in violations:
                    self.stdout.write(f"   - Table: {violation[0]}, Row: {violation[1]}, Parent: {violation[2]}, FK: {violation[3]}")
            else:
                self.stdout.write("✅ No foreign key violations found")
            
            # Check document table structure
            self.stdout.write(f"\n📄 Document table structure:")
            cursor.execute("PRAGMA table_info(documents_document);")
            columns = cursor.fetchall()
            for col in columns:
                self.stdout.write(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        self.stdout.write("\n🎉 Database schema check completed!")
