"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels";
exports.ids = ["vendor-chunks/react-resizable-panels"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DATA_ATTRIBUTES: () => (/* binding */ DATA_ATTRIBUTES),\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   disableGlobalCursorStyles: () => (/* binding */ disableGlobalCursorStyles),\n/* harmony export */   enableGlobalCursorStyles: () => (/* binding */ enableGlobalCursorStyles),\n/* harmony export */   getIntersectingRectangle: () => (/* binding */ getIntersectingRectangle),\n/* harmony export */   getPanelElement: () => (/* binding */ getPanelElement),\n/* harmony export */   getPanelElementsForGroup: () => (/* binding */ getPanelElementsForGroup),\n/* harmony export */   getPanelGroupElement: () => (/* binding */ getPanelGroupElement),\n/* harmony export */   getResizeHandleElement: () => (/* binding */ getResizeHandleElement),\n/* harmony export */   getResizeHandleElementIndex: () => (/* binding */ getResizeHandleElementIndex),\n/* harmony export */   getResizeHandleElementsForGroup: () => (/* binding */ getResizeHandleElementsForGroup),\n/* harmony export */   getResizeHandlePanelIds: () => (/* binding */ getResizeHandlePanelIds),\n/* harmony export */   intersects: () => (/* binding */ intersects),\n/* harmony export */   setNonce: () => (/* binding */ setNonce)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useId = react__WEBPACK_IMPORTED_MODULE_0__[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) {\n      if (defaultSize == null) {\n        devWarningsRef.current.didLogMissingDefaultSizeWarning = true;\n        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags) {\n  if (constraintFlags) {\n    const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n    const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n    const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n    const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController = new AbortController();\nfunction updateListeners() {\n  listenersAbortController.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    window.addEventListener(\"pointerup\", handlePointerUp, options);\n    window.addEventListener(\"pointercancel\", handlePointerUp, options);\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  var _dataset;\n  //If the root element is the PanelGroup\n  if (rootElement instanceof HTMLElement && (rootElement === null || rootElement === void 0 ? void 0 : (_dataset = rootElement.dataset) === null || _dataset === void 0 ? void 0 : _dataset.panelGroupId) == id) {\n    return rootElement;\n  }\n\n  //Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didWarnAboutMissingResizeHandle: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toPrecision(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [dragState, setDragState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layout, setLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  const panelSizeBeforeCollapseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map());\n  const prevDeltaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n  const registerResizeHandle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupContext.Provider, {\n    value: context\n  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"inactive\");\n  const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [resizeHandler, setResizeHandler] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    state\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.node.esm.js\n");

/***/ })

};
;