"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, XCircle, AlertCircle, Trophy, Target, BookOpen, RotateCcw, Zap } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { QuizFeedback, QuizSubmissionResponse } from "@/lib/quiz-utils"

interface QuizFeedbackProps {
  feedbackData: QuizSubmissionResponse
  onRetakeQuiz?: () => void
  onNewQuiz?: () => void
}

export function QuizFeedbackComponent({ feedbackData, onRetakeQuiz, onNewQuiz }: QuizFeedbackProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500"
    if (score >= 60) return "text-yellow-500"
    return "text-red-500"
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "destructive"
  }

  const getPerformanceMessage = (score: number) => {
    if (score >= 90) return { message: "Outstanding Performance! 🌟", description: "You've mastered this topic!" }
    if (score >= 80) return { message: "Great Job! 🎉", description: "You have a solid understanding." }
    if (score >= 70) return { message: "Good Work! 👍", description: "You're on the right track." }
    if (score >= 60) return { message: "Keep Practicing! 📚", description: "You're making progress." }
    return { message: "More Study Needed 💪", description: "Don't give up, keep learning!" }
  }

  const performance = getPerformanceMessage(feedbackData.overall_score)

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1" style={{ scrollbarWidth: '10px' }}>
        <div className="p-6 space-y-6">
          {/* Header with Overall Score */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
              <CardHeader className="text-center pb-4">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="mx-auto mb-4"
                >
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                    <Trophy className="h-10 w-10 text-primary-foreground" />
                  </div>
                </motion.div>
                <CardTitle className="text-3xl font-bold text-foreground">
                  {performance.message}
                </CardTitle>
                <CardDescription className="text-lg text-muted-foreground">
                  {performance.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center space-x-8">
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.4, type: "spring", stiffness: 300 }}
                      className={`text-6xl font-bold ${getScoreColor(feedbackData.overall_score)} mb-2`}
                    >
                      {Math.round(feedbackData.overall_score)}%
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Overall Score</p>
                  </div>
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                      className="text-4xl font-bold text-foreground mb-2"
                    >
                      {feedbackData.feedback.length}
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Questions</p>
                  </div>
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.6, type: "spring", stiffness: 300 }}
                      className="text-4xl font-bold text-green-500 mb-2"
                    >
                      {feedbackData.feedback.filter(f => f.is_correct).length}
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Correct</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Question-by-Question Feedback */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
              <Target className="h-6 w-6 text-primary" />
              Detailed Feedback
            </h2>
            <div className="space-y-4">
              {feedbackData.feedback.map((feedback, index) => (
                <motion.div
                  key={feedback.question_id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index, duration: 0.3 }}
                >
                  <Card className={`border-l-4 ${feedback.is_correct ? 'border-l-green-500 bg-green-50/50 dark:bg-green-950/20' : 'border-l-red-500 bg-red-50/50 dark:bg-red-950/20'}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <span className="text-muted-foreground">Q{index + 1}.</span>
                          {feedback.is_correct ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                        </CardTitle>
                        <Badge variant={feedback.is_correct ? "default" : "destructive"}>
                          {feedback.is_correct ? "Correct" : "Incorrect"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* User Answer */}
                      <div className="bg-muted/30 p-4 rounded-lg border">
                        <h4 className="font-semibold text-sm text-muted-foreground mb-2">Your Answer:</h4>
                        <p className="text-foreground">{feedback.user_answer}</p>
                      </div>

                      {/* Correct Answer */}
                      <div className="bg-green-50/50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 className="font-semibold text-sm text-green-700 dark:text-green-300 mb-2">Correct Answer:</h4>
                        <p className="text-green-800 dark:text-green-200">{feedback.correct_answer}</p>
                      </div>

                      {/* AI Feedback */}
                      <div className="bg-blue-50/50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                        <h4 className="font-semibold text-sm text-blue-700 dark:text-blue-300 mb-2 flex items-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          AI Feedback:
                        </h4>
                        <p className="text-blue-800 dark:text-blue-200 leading-relaxed">{feedback.feedback}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="flex gap-4 pt-6"
          >
            <Button
              onClick={onRetakeQuiz}
              variant="outline"
              className="flex-1 h-12 border-2 border-border hover:border-primary hover:bg-muted transition-all duration-200"
              size="lg"
            >
              <RotateCcw className="h-5 w-5 mr-2" />
              Retake Quiz
            </Button>
            <Button
              onClick={onNewQuiz}
              className="flex-1 h-12"
              size="lg"
            >
              <Zap className="h-5 w-5 mr-2" />
              Generate New Quiz
            </Button>
          </motion.div>
        </div>
      </ScrollArea>
    </div>
  )
}
