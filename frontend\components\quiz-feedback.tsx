"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckCircle, XCircle, AlertCircle, Trophy, Target, BookOpen, RotateCcw, Zap, Brain, Clock, TrendingUp, ArrowRight } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { QuizFeedback, QuizSubmissionResponse, CMSRecommendationData } from "@/lib/quiz-utils"

interface QuizFeedbackProps {
  feedbackData: QuizSubmissionResponse
  onRetakeQuiz?: () => void
  onNewQuiz?: () => void
}

export function QuizFeedbackComponent({ feedbackData, onRetakeQuiz, onNewQuiz }: QuizFeedbackProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-500"
    if (score >= 60) return "text-yellow-500"
    return "text-red-500"
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "destructive"
  }

  const getPerformanceMessage = (score: number) => {
    if (score >= 90) return { message: "Outstanding Performance! 🌟", description: "You've mastered this topic!" }
    if (score >= 80) return { message: "Great Job! 🎉", description: "You have a solid understanding." }
    if (score >= 70) return { message: "Good Work! 👍", description: "You're on the right track." }
    if (score >= 60) return { message: "Keep Practicing! 📚", description: "You're making progress." }
    return { message: "More Study Needed 💪", description: "Don't give up, keep learning!" }
  }

  const performance = getPerformanceMessage(feedbackData.overall_score)

  const getUserZoneColor = (zone: string) => {
    switch (zone) {
      case 'mastered': return 'text-green-600 dark:text-green-400'
      case 'stable': return 'text-yellow-600 dark:text-yellow-400'
      case 'moderate_gap': return 'text-orange-600 dark:text-orange-400'
      case 'weak': return 'text-red-600 dark:text-red-400'
      case 'unclear': return 'text-red-700 dark:text-red-500'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  const getUserZoneBadgeColor = (zone: string) => {
    switch (zone) {
      case 'mastered': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'stable': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'moderate_gap': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'weak': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'unclear': return 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1" style={{ scrollbarWidth: '10px' }}>
        <div className="p-6 space-y-6">
          {/* Header with Overall Score */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
              <CardHeader className="text-center pb-4">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="mx-auto mb-4"
                >
                  <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                    <Trophy className="h-10 w-10 text-primary-foreground" />
                  </div>
                </motion.div>
                <CardTitle className="text-3xl font-bold text-foreground">
                  {performance.message}
                </CardTitle>
                <CardDescription className="text-lg text-muted-foreground">
                  {performance.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center space-x-8">
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.4, type: "spring", stiffness: 300 }}
                      className={`text-6xl font-bold ${getScoreColor(feedbackData.overall_score)} mb-2`}
                    >
                      {Math.round(feedbackData.overall_score)}%
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Overall Score</p>
                  </div>
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                      className="text-4xl font-bold text-foreground mb-2"
                    >
                      {feedbackData.feedback.length}
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Questions</p>
                  </div>
                  <div className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.6, type: "spring", stiffness: 300 }}
                      className="text-4xl font-bold text-green-500 mb-2"
                    >
                      {feedbackData.feedback.filter(f => f.is_correct).length}
                    </motion.div>
                    <p className="text-sm text-muted-foreground font-medium">Correct</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* CMS Recommendations Section */}
          {feedbackData.recommendations && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
                <Brain className="h-6 w-6 text-primary" />
                Personalized Learning Recommendations
              </h2>

              <div className="grid gap-4 md:grid-cols-2">
                {/* Main Recommendation Card */}
                <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <span className="text-2xl">{feedbackData.recommendations.recommendation.icon}</span>
                        Learning Zone
                      </CardTitle>
                      <Badge className={getUserZoneBadgeColor(feedbackData.recommendations.user_zone)}>
                        {feedbackData.recommendations.user_zone.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-foreground font-medium mb-3">
                      {feedbackData.recommendations.recommendation.message}
                    </p>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        <span>{feedbackData.recommendations.analysis.quiz_performance}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{feedbackData.recommendations.analysis.time_efficiency}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Next Steps Card */}
                <Card className="border-2 border-secondary/20">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-primary" />
                      Next Steps
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {feedbackData.recommendations.next_steps.map((step, index) => (
                        <motion.li
                          key={index}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.8 + index * 0.1 }}
                          className="flex items-start gap-2 text-sm"
                        >
                          <ArrowRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <span>{step}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Overall Assessment */}
              <Card className="mt-4 bg-muted/30">
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <AlertCircle className="h-4 w-4" />
                    <span className="font-medium">Assessment:</span>
                    <span>{feedbackData.recommendations.analysis.overall_assessment}</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Question-by-Question Feedback */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
              <Target className="h-6 w-6 text-primary" />
              Detailed Feedback
            </h2>
            <div className="space-y-4">
              {feedbackData.feedback.map((feedback, index) => (
                <motion.div
                  key={feedback.question_id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index, duration: 0.3 }}
                >
                  <Card className={`border-l-4 ${feedback.is_correct ? 'border-l-green-500 bg-green-50/50 dark:bg-green-950/20' : 'border-l-red-500 bg-red-50/50 dark:bg-red-950/20'}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <span className="text-muted-foreground">Q{index + 1}.</span>
                          {feedback.is_correct ? (
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-500" />
                          )}
                        </CardTitle>
                        <Badge variant={feedback.is_correct ? "default" : "destructive"}>
                          {feedback.is_correct ? "Correct" : "Incorrect"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* User Answer */}
                      <div className="bg-muted/30 p-4 rounded-lg border">
                        <h4 className="font-semibold text-sm text-muted-foreground mb-2">Your Answer:</h4>
                        <p className="text-foreground">{feedback.user_answer}</p>
                      </div>

                      {/* Correct Answer */}
                      <div className="bg-green-50/50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 className="font-semibold text-sm text-green-700 dark:text-green-300 mb-2">Correct Answer:</h4>
                        <p className="text-green-800 dark:text-green-200">{feedback.correct_answer}</p>
                      </div>

                      {/* AI Feedback */}
                      <div className="bg-blue-50/50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                        <h4 className="font-semibold text-sm text-blue-700 dark:text-blue-300 mb-2 flex items-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          AI Feedback:
                        </h4>
                        <p className="text-blue-800 dark:text-blue-200 leading-relaxed">{feedback.feedback}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="pt-6"
          >
            {feedbackData.recommendations && (
              <div className="mb-4 p-3 bg-primary/5 border border-primary/20 rounded-lg">
                <p className="text-sm text-muted-foreground text-center">
                  💡 <strong>Tip:</strong> Follow the personalized recommendations above to improve your understanding of this topic.
                </p>
              </div>
            )}
            <div className="flex gap-4">
            <Button
              onClick={onRetakeQuiz}
              variant="outline"
              className="flex-1 h-12 border-2 border-border hover:border-primary hover:bg-muted transition-all duration-200"
              size="lg"
            >
              <RotateCcw className="h-5 w-5 mr-2" />
              Retake Quiz
            </Button>
            <Button
              onClick={onNewQuiz}
              className="flex-1 h-12"
              size="lg"
            >
              <Zap className="h-5 w-5 mr-2" />
              Generate New Quiz
            </Button>
            </div>
          </motion.div>
        </div>
      </ScrollArea>
    </div>
  )
}
