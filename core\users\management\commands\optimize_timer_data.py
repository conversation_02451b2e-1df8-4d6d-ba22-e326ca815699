from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.utils import timezone
from django.db import transaction
from datetime import timedelta
from users.models import DocumentTimeTracking
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Optimize timer data by cleaning up old sessions and consolidating records'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=90,
            help='Number of days to keep detailed session data (default: 90)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear timer-related cache entries'
        )

    def handle(self, *args, **options):
        days_to_keep = options['days']
        dry_run = options['dry_run']
        clear_cache = options['clear_cache']

        self.stdout.write(
            self.style.SUCCESS(f'Starting timer data optimization (keeping {days_to_keep} days)')
        )

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        # Clear cache if requested
        if clear_cache:
            self._clear_timer_cache(dry_run)

        # Clean up old inactive sessions
        self._cleanup_old_sessions(days_to_keep, dry_run)

        # Optimize database indexes
        self._optimize_indexes(dry_run)

        # Generate summary statistics
        self._generate_summary()

        self.stdout.write(self.style.SUCCESS('Timer data optimization completed'))

    def _clear_timer_cache(self, dry_run):
        """Clear timer-related cache entries"""
        self.stdout.write('Clearing timer cache entries...')
        
        if not dry_run:
            # Clear all timer session cache entries
            # Note: This is a simplified approach. In production, you might want to
            # iterate through specific cache keys if you have a large cache
            cache_keys_to_clear = []
            
            # Get all tracking records to build cache keys
            for tracking in DocumentTimeTracking.objects.all():
                session_key = f"timer_session_{tracking.username}_{tracking.file_name}"
                doc_key = f"doc_tracking_{tracking.username}_{tracking.file_name}"
                cache_keys_to_clear.extend([session_key, doc_key])
            
            # Clear cache keys in batches
            for i in range(0, len(cache_keys_to_clear), 100):
                batch = cache_keys_to_clear[i:i+100]
                cache.delete_many(batch)
            
            self.stdout.write(f'Cleared {len(cache_keys_to_clear)} cache entries')
        else:
            self.stdout.write('Would clear timer cache entries')

    def _cleanup_old_sessions(self, days_to_keep, dry_run):
        """Clean up old session data that's no longer needed"""
        cutoff_date = timezone.now() - timedelta(days=days_to_keep)
        
        self.stdout.write(f'Cleaning up sessions older than {cutoff_date.date()}...')
        
        # Find records that haven't been accessed in the specified time
        old_records = DocumentTimeTracking.objects.filter(
            last_accessed__lt=cutoff_date,
            total_time_seconds=0,  # Only clean up records with no time tracked
            number_of_sessions=0   # And no sessions
        )
        
        old_count = old_records.count()
        
        if old_count > 0:
            if not dry_run:
                with transaction.atomic():
                    deleted_count, _ = old_records.delete()
                self.stdout.write(f'Deleted {deleted_count} old unused tracking records')
            else:
                self.stdout.write(f'Would delete {old_count} old unused tracking records')
        else:
            self.stdout.write('No old unused tracking records found')

        # Find and report on records with very low activity
        low_activity_records = DocumentTimeTracking.objects.filter(
            last_accessed__lt=cutoff_date,
            total_time_seconds__lt=60,  # Less than 1 minute total
            number_of_sessions__lte=1   # 1 or fewer sessions
        )
        
        low_activity_count = low_activity_records.count()
        if low_activity_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f'Found {low_activity_count} records with very low activity (consider manual review)'
                )
            )

    def _optimize_indexes(self, dry_run):
        """Optimize database indexes for better performance"""
        self.stdout.write('Checking database indexes...')
        
        # This is database-specific. For PostgreSQL, you might run ANALYZE
        # For now, we'll just report on the current state
        
        total_records = DocumentTimeTracking.objects.count()
        active_records = DocumentTimeTracking.objects.filter(
            last_accessed__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        self.stdout.write(f'Total tracking records: {total_records}')
        self.stdout.write(f'Active records (last 30 days): {active_records}')
        
        if not dry_run:
            # You could add database-specific optimization commands here
            # For example, for PostgreSQL:
            # from django.db import connection
            # with connection.cursor() as cursor:
            #     cursor.execute('ANALYZE users_documenttimetracking;')
            pass

    def _generate_summary(self):
        """Generate summary statistics"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('TIMER DATA SUMMARY')
        self.stdout.write('='*50)
        
        # Total statistics
        total_records = DocumentTimeTracking.objects.count()
        total_time = sum(
            record.total_time_seconds 
            for record in DocumentTimeTracking.objects.all()
        )
        total_sessions = sum(
            record.number_of_sessions 
            for record in DocumentTimeTracking.objects.all()
        )
        
        self.stdout.write(f'Total tracking records: {total_records}')
        self.stdout.write(f'Total time tracked: {self._format_time(total_time)}')
        self.stdout.write(f'Total sessions: {total_sessions}')
        
        if total_sessions > 0:
            avg_session_time = total_time / total_sessions
            self.stdout.write(f'Average session time: {self._format_time(int(avg_session_time))}')
        
        # Recent activity
        recent_records = DocumentTimeTracking.objects.filter(
            last_accessed__gte=timezone.now() - timedelta(days=7)
        ).count()
        
        self.stdout.write(f'Active records (last 7 days): {recent_records}')
        
        # Top files by time
        top_files = DocumentTimeTracking.objects.order_by('-total_time_seconds')[:5]
        
        if top_files:
            self.stdout.write('\nTop 5 files by total time:')
            for i, record in enumerate(top_files, 1):
                self.stdout.write(
                    f'  {i}. {record.file_name} - {self._format_time(record.total_time_seconds)} '
                    f'({record.number_of_sessions} sessions)'
                )

    def _format_time(self, seconds):
        """Format time in human readable format"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60

        if hours > 0:
            return f"{hours}h {minutes}m {secs}s"
        elif minutes > 0:
            return f"{minutes}m {secs}s"
        else:
            return f"{secs}s"
