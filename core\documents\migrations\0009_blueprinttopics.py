# Generated by Django 4.2.20 on 2025-05-09 15:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0008_document_blueprint"),
    ]

    operations = [
        migrations.CreateModel(
            name="BlueprintTopics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.Char<PERSON>ield(help_text="Title of the topic", max_length=255),
                ),
                (
                    "weightage",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Weightage of the topic in percentile",
                        max_digits=5,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "content",
                    models.ManyToManyField(
                        help_text="Relevant document embeddings for this topic",
                        related_name="topics",
                        to="documents.documentembedding",
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics",
                        to="documents.document",
                    ),
                ),
            ],
            options={
                "verbose_name": "Blueprint Topic",
                "verbose_name_plural": "Blueprint Topics",
                "ordering": ["-weightage"],
            },
        ),
    ]
