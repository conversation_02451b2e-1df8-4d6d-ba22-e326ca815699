"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/chevrotain-allstar";
exports.ids = ["vendor-chunks/chevrotain-allstar"];
exports.modules = {

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js":
/*!*******************************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/all-star-lookahead.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLStarLookaheadStrategy: () => (/* binding */ LLStarLookaheadStrategy)\n/* harmony export */ });\n/* harmony import */ var chevrotain__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! chevrotain */ \"(ssr)/./node_modules/chevrotain/lib/src/api.js\");\n/* harmony import */ var _atn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./atn.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/atn.js\");\n/* harmony import */ var _dfa_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dfa.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js\");\n/* harmony import */ var lodash_es_min_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/min.js */ \"(ssr)/./node_modules/lodash-es/min.js\");\n/* harmony import */ var lodash_es_flatMap_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! lodash-es/flatMap.js */ \"(ssr)/./node_modules/lodash-es/flatMap.js\");\n/* harmony import */ var lodash_es_uniqBy_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! lodash-es/uniqBy.js */ \"(ssr)/./node_modules/lodash-es/uniqBy.js\");\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es_flatten_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/flatten.js */ \"(ssr)/./node_modules/lodash-es/flatten.js\");\n/* harmony import */ var lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/forEach.js */ \"(ssr)/./node_modules/lodash-es/forEach.js\");\n/* harmony import */ var lodash_es_isEmpty_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/isEmpty.js */ \"(ssr)/./node_modules/lodash-es/isEmpty.js\");\n/* harmony import */ var lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es/reduce.js */ \"(ssr)/./node_modules/lodash-es/reduce.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n\n\n\n\n\n\n\n\n\n\nfunction createDFACache(startState, decision) {\n    const map = {};\n    return (predicateSet) => {\n        const key = predicateSet.toString();\n        let existing = map[key];\n        if (existing !== undefined) {\n            return existing;\n        }\n        else {\n            existing = {\n                atnStartState: startState,\n                decision,\n                states: {}\n            };\n            map[key] = existing;\n            return existing;\n        }\n    };\n}\nclass PredicateSet {\n    constructor() {\n        this.predicates = [];\n    }\n    is(index) {\n        return index >= this.predicates.length || this.predicates[index];\n    }\n    set(index, value) {\n        this.predicates[index] = value;\n    }\n    toString() {\n        let value = \"\";\n        const size = this.predicates.length;\n        for (let i = 0; i < size; i++) {\n            value += this.predicates[i] === true ? \"1\" : \"0\";\n        }\n        return value;\n    }\n}\nconst EMPTY_PREDICATES = new PredicateSet();\nclass LLStarLookaheadStrategy extends chevrotain__WEBPACK_IMPORTED_MODULE_0__.LLkLookaheadStrategy {\n    constructor(options) {\n        var _a;\n        super();\n        this.logging = (_a = options === null || options === void 0 ? void 0 : options.logging) !== null && _a !== void 0 ? _a : ((message) => console.log(message));\n    }\n    initialize(options) {\n        this.atn = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.createATN)(options.rules);\n        this.dfas = initATNSimulator(this.atn);\n    }\n    validateAmbiguousAlternationAlternatives() {\n        return [];\n    }\n    validateEmptyOrAlternatives() {\n        return [];\n    }\n    buildLookaheadForAlternation(options) {\n        const { prodOccurrence, rule, hasPredicates, dynamicTokensEnabled } = options;\n        const dfas = this.dfas;\n        const logging = this.logging;\n        const key = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.buildATNKey)(rule, 'Alternation', prodOccurrence);\n        const decisionState = this.atn.decisionMap[key];\n        const decisionIndex = decisionState.decision;\n        const partialAlts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.getLookaheadPaths)({\n            maxLookahead: 1,\n            occurrence: prodOccurrence,\n            prodType: \"Alternation\",\n            rule: rule\n        }), (currAlt) => (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(currAlt, (path) => path[0]));\n        if (isLL1Sequence(partialAlts, false) && !dynamicTokensEnabled) {\n            const choiceToAlt = (0,lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(partialAlts, (result, currAlt, idx) => {\n                (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currAlt, (currTokType) => {\n                    if (currTokType) {\n                        result[currTokType.tokenTypeIdx] = idx;\n                        (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currTokType.categoryMatches, (currExtendingType) => {\n                            result[currExtendingType] = idx;\n                        });\n                    }\n                });\n                return result;\n            }, {});\n            if (hasPredicates) {\n                return function (orAlts) {\n                    var _a;\n                    const nextToken = this.LA(1);\n                    const prediction = choiceToAlt[nextToken.tokenTypeIdx];\n                    if (orAlts !== undefined && prediction !== undefined) {\n                        const gate = (_a = orAlts[prediction]) === null || _a === void 0 ? void 0 : _a.GATE;\n                        if (gate !== undefined && gate.call(this) === false) {\n                            return undefined;\n                        }\n                    }\n                    return prediction;\n                };\n            }\n            else {\n                return function () {\n                    const nextToken = this.LA(1);\n                    return choiceToAlt[nextToken.tokenTypeIdx];\n                };\n            }\n        }\n        else if (hasPredicates) {\n            return function (orAlts) {\n                const predicates = new PredicateSet();\n                const length = orAlts === undefined ? 0 : orAlts.length;\n                for (let i = 0; i < length; i++) {\n                    const gate = orAlts === null || orAlts === void 0 ? void 0 : orAlts[i].GATE;\n                    predicates.set(i, gate === undefined || gate.call(this));\n                }\n                const result = adaptivePredict.call(this, dfas, decisionIndex, predicates, logging);\n                return typeof result === 'number' ? result : undefined;\n            };\n        }\n        else {\n            return function () {\n                const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n                return typeof result === 'number' ? result : undefined;\n            };\n        }\n    }\n    buildLookaheadForOptional(options) {\n        const { prodOccurrence, rule, prodType, dynamicTokensEnabled } = options;\n        const dfas = this.dfas;\n        const logging = this.logging;\n        const key = (0,_atn_js__WEBPACK_IMPORTED_MODULE_1__.buildATNKey)(rule, prodType, prodOccurrence);\n        const decisionState = this.atn.decisionMap[key];\n        const decisionIndex = decisionState.decision;\n        const alts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])((0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.getLookaheadPaths)({\n            maxLookahead: 1,\n            occurrence: prodOccurrence,\n            prodType,\n            rule\n        }), (e) => {\n            return (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(e, (g) => g[0]);\n        });\n        if (isLL1Sequence(alts) && alts[0][0] && !dynamicTokensEnabled) {\n            const alt = alts[0];\n            const singleTokensTypes = (0,lodash_es_flatten_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(alt);\n            if (singleTokensTypes.length === 1 &&\n                (0,lodash_es_isEmpty_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(singleTokensTypes[0].categoryMatches)) {\n                const expectedTokenType = singleTokensTypes[0];\n                const expectedTokenUniqueKey = expectedTokenType.tokenTypeIdx;\n                return function () {\n                    return this.LA(1).tokenTypeIdx === expectedTokenUniqueKey;\n                };\n            }\n            else {\n                const choiceToAlt = (0,lodash_es_reduce_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(singleTokensTypes, (result, currTokType) => {\n                    if (currTokType !== undefined) {\n                        result[currTokType.tokenTypeIdx] = true;\n                        (0,lodash_es_forEach_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(currTokType.categoryMatches, (currExtendingType) => {\n                            result[currExtendingType] = true;\n                        });\n                    }\n                    return result;\n                }, {});\n                return function () {\n                    const nextToken = this.LA(1);\n                    return choiceToAlt[nextToken.tokenTypeIdx] === true;\n                };\n            }\n        }\n        return function () {\n            const result = adaptivePredict.call(this, dfas, decisionIndex, EMPTY_PREDICATES, logging);\n            return typeof result === \"object\" ? false : result === 0;\n        };\n    }\n}\nfunction isLL1Sequence(sequences, allowEmpty = true) {\n    const fullSet = new Set();\n    for (const alt of sequences) {\n        const altSet = new Set();\n        for (const tokType of alt) {\n            if (tokType === undefined) {\n                if (allowEmpty) {\n                    // Epsilon production encountered\n                    break;\n                }\n                else {\n                    return false;\n                }\n            }\n            const indices = [tokType.tokenTypeIdx].concat(tokType.categoryMatches);\n            for (const index of indices) {\n                if (fullSet.has(index)) {\n                    if (!altSet.has(index)) {\n                        return false;\n                    }\n                }\n                else {\n                    fullSet.add(index);\n                    altSet.add(index);\n                }\n            }\n        }\n    }\n    return true;\n}\nfunction initATNSimulator(atn) {\n    const decisionLength = atn.decisionStates.length;\n    const decisionToDFA = Array(decisionLength);\n    for (let i = 0; i < decisionLength; i++) {\n        decisionToDFA[i] = createDFACache(atn.decisionStates[i], i);\n    }\n    return decisionToDFA;\n}\nfunction adaptivePredict(dfaCaches, decision, predicateSet, logging) {\n    const dfa = dfaCaches[decision](predicateSet);\n    let start = dfa.start;\n    if (start === undefined) {\n        const closure = computeStartState(dfa.atnStartState);\n        start = addDFAState(dfa, newDFAState(closure));\n        dfa.start = start;\n    }\n    const alt = performLookahead.apply(this, [dfa, start, predicateSet, logging]);\n    return alt;\n}\nfunction performLookahead(dfa, s0, predicateSet, logging) {\n    let previousD = s0;\n    let i = 1;\n    const path = [];\n    let t = this.LA(i++);\n    while (true) {\n        let d = getExistingTargetState(previousD, t);\n        if (d === undefined) {\n            d = computeLookaheadTarget.apply(this, [dfa, previousD, t, i, predicateSet, logging]);\n        }\n        if (d === _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR) {\n            return buildAdaptivePredictError(path, previousD, t);\n        }\n        if (d.isAcceptState === true) {\n            return d.prediction;\n        }\n        previousD = d;\n        path.push(t);\n        t = this.LA(i++);\n    }\n}\nfunction computeLookaheadTarget(dfa, previousD, token, lookahead, predicateSet, logging) {\n    const reach = computeReachSet(previousD.configs, token, predicateSet);\n    if (reach.size === 0) {\n        addDFAEdge(dfa, previousD, token, _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR);\n        return _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR;\n    }\n    let newState = newDFAState(reach);\n    const predictedAlt = getUniqueAlt(reach, predicateSet);\n    if (predictedAlt !== undefined) {\n        newState.isAcceptState = true;\n        newState.prediction = predictedAlt;\n        newState.configs.uniqueAlt = predictedAlt;\n    }\n    else if (hasConflictTerminatingPrediction(reach)) {\n        const prediction = (0,lodash_es_min_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(reach.alts);\n        newState.isAcceptState = true;\n        newState.prediction = prediction;\n        newState.configs.uniqueAlt = prediction;\n        reportLookaheadAmbiguity.apply(this, [dfa, lookahead, reach.alts, logging]);\n    }\n    newState = addDFAEdge(dfa, previousD, token, newState);\n    return newState;\n}\nfunction reportLookaheadAmbiguity(dfa, lookahead, ambiguityIndices, logging) {\n    const prefixPath = [];\n    for (let i = 1; i <= lookahead; i++) {\n        prefixPath.push(this.LA(i).tokenType);\n    }\n    const atnState = dfa.atnStartState;\n    const topLevelRule = atnState.rule;\n    const production = atnState.production;\n    const message = buildAmbiguityError({\n        topLevelRule,\n        ambiguityIndices,\n        production,\n        prefixPath\n    });\n    logging(message);\n}\nfunction buildAmbiguityError(options) {\n    const pathMsg = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.prefixPath, (currtok) => (0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.tokenLabel)(currtok)).join(\", \");\n    const occurrence = options.production.idx === 0 ? \"\" : options.production.idx;\n    let currMessage = `Ambiguous Alternatives Detected: <${options.ambiguityIndices.join(\", \")}> in <${getProductionDslName(options.production)}${occurrence}>` +\n        ` inside <${options.topLevelRule.name}> Rule,\\n` +\n        `<${pathMsg}> may appears as a prefix path in all these alternatives.\\n`;\n    currMessage =\n        currMessage +\n            `See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES\\n` +\n            `For Further details.`;\n    return currMessage;\n}\nfunction getProductionDslName(prod) {\n    if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return \"SUBRULE\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return \"OPTION\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return \"OR\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return \"AT_LEAST_ONE\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return \"AT_LEAST_ONE_SEP\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return \"MANY_SEP\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return \"MANY\";\n    }\n    else if (prod instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return \"CONSUME\";\n    }\n    else {\n        throw Error(\"non exhaustive match\");\n    }\n}\nfunction buildAdaptivePredictError(path, previous, current) {\n    const nextTransitions = (0,lodash_es_flatMap_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(previous.configs.elements, (e) => e.state.transitions);\n    const nextTokenTypes = (0,lodash_es_uniqBy_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(nextTransitions\n        .filter((e) => e instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.AtomTransition)\n        .map((e) => e.tokenType), (e) => e.tokenTypeIdx);\n    return {\n        actualToken: current,\n        possibleTokenTypes: nextTokenTypes,\n        tokenPath: path\n    };\n}\nfunction getExistingTargetState(state, token) {\n    return state.edges[token.tokenTypeIdx];\n}\nfunction computeReachSet(configs, token, predicateSet) {\n    const intermediate = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n    const skippedStopStates = [];\n    for (const c of configs.elements) {\n        if (predicateSet.is(c.alt) === false) {\n            continue;\n        }\n        if (c.state.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            skippedStopStates.push(c);\n            continue;\n        }\n        const transitionLength = c.state.transitions.length;\n        for (let i = 0; i < transitionLength; i++) {\n            const transition = c.state.transitions[i];\n            const target = getReachableTarget(transition, token);\n            if (target !== undefined) {\n                intermediate.add({\n                    state: target,\n                    alt: c.alt,\n                    stack: c.stack\n                });\n            }\n        }\n    }\n    let reach;\n    if (skippedStopStates.length === 0 && intermediate.size === 1) {\n        reach = intermediate;\n    }\n    if (reach === undefined) {\n        reach = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n        for (const c of intermediate.elements) {\n            closure(c, reach);\n        }\n    }\n    if (skippedStopStates.length > 0 && !hasConfigInRuleStopState(reach)) {\n        for (const c of skippedStopStates) {\n            reach.add(c);\n        }\n    }\n    return reach;\n}\nfunction getReachableTarget(transition, token) {\n    if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.AtomTransition &&\n        (0,chevrotain__WEBPACK_IMPORTED_MODULE_0__.tokenMatcher)(token, transition.tokenType)) {\n        return transition.target;\n    }\n    return undefined;\n}\nfunction getUniqueAlt(configs, predicateSet) {\n    let alt;\n    for (const c of configs.elements) {\n        if (predicateSet.is(c.alt) === true) {\n            if (alt === undefined) {\n                alt = c.alt;\n            }\n            else if (alt !== c.alt) {\n                return undefined;\n            }\n        }\n    }\n    return alt;\n}\nfunction newDFAState(closure) {\n    return {\n        configs: closure,\n        edges: {},\n        isAcceptState: false,\n        prediction: -1\n    };\n}\nfunction addDFAEdge(dfa, from, token, to) {\n    to = addDFAState(dfa, to);\n    from.edges[token.tokenTypeIdx] = to;\n    return to;\n}\nfunction addDFAState(dfa, state) {\n    if (state === _dfa_js__WEBPACK_IMPORTED_MODULE_2__.DFA_ERROR) {\n        return state;\n    }\n    // Repetitions have the same config set\n    // Therefore, storing the key of the config in a map allows us to create a loop in our DFA\n    const mapKey = state.configs.key;\n    const existing = dfa.states[mapKey];\n    if (existing !== undefined) {\n        return existing;\n    }\n    state.configs.finalize();\n    dfa.states[mapKey] = state;\n    return state;\n}\nfunction computeStartState(atnState) {\n    const configs = new _dfa_js__WEBPACK_IMPORTED_MODULE_2__.ATNConfigSet();\n    const numberOfTransitions = atnState.transitions.length;\n    for (let i = 0; i < numberOfTransitions; i++) {\n        const target = atnState.transitions[i].target;\n        const config = {\n            state: target,\n            alt: i,\n            stack: []\n        };\n        closure(config, configs);\n    }\n    return configs;\n}\nfunction closure(config, configs) {\n    const p = config.state;\n    if (p.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n        if (config.stack.length > 0) {\n            const atnStack = [...config.stack];\n            const followState = atnStack.pop();\n            const followConfig = {\n                state: followState,\n                alt: config.alt,\n                stack: atnStack\n            };\n            closure(followConfig, configs);\n        }\n        else {\n            // Dipping into outer context, simply add the config\n            // This will stop computation once every config is at the rule stop state\n            configs.add(config);\n        }\n        return;\n    }\n    if (!p.epsilonOnlyTransitions) {\n        configs.add(config);\n    }\n    const transitionLength = p.transitions.length;\n    for (let i = 0; i < transitionLength; i++) {\n        const transition = p.transitions[i];\n        const c = getEpsilonTarget(config, transition);\n        if (c !== undefined) {\n            closure(c, configs);\n        }\n    }\n}\nfunction getEpsilonTarget(config, transition) {\n    if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.EpsilonTransition) {\n        return {\n            state: transition.target,\n            alt: config.alt,\n            stack: config.stack\n        };\n    }\n    else if (transition instanceof _atn_js__WEBPACK_IMPORTED_MODULE_1__.RuleTransition) {\n        const stack = [...config.stack, transition.followState];\n        return {\n            state: transition.target,\n            alt: config.alt,\n            stack\n        };\n    }\n    return undefined;\n}\nfunction hasConfigInRuleStopState(configs) {\n    for (const c of configs.elements) {\n        if (c.state.type === _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction allConfigsInRuleStopStates(configs) {\n    for (const c of configs.elements) {\n        if (c.state.type !== _atn_js__WEBPACK_IMPORTED_MODULE_1__.ATN_RULE_STOP) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction hasConflictTerminatingPrediction(configs) {\n    if (allConfigsInRuleStopStates(configs)) {\n        return true;\n    }\n    const altSets = getConflictingAltSets(configs.elements);\n    const heuristic = hasConflictingAltSet(altSets) && !hasStateAssociatedWithOneAlt(altSets);\n    return heuristic;\n}\nfunction getConflictingAltSets(configs) {\n    const configToAlts = new Map();\n    for (const c of configs) {\n        const key = (0,_dfa_js__WEBPACK_IMPORTED_MODULE_2__.getATNConfigKey)(c, false);\n        let alts = configToAlts.get(key);\n        if (alts === undefined) {\n            alts = {};\n            configToAlts.set(key, alts);\n        }\n        alts[c.alt] = true;\n    }\n    return configToAlts;\n}\nfunction hasConflictingAltSet(altSets) {\n    for (const value of Array.from(altSets.values())) {\n        if (Object.keys(value).length > 1) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction hasStateAssociatedWithOneAlt(altSets) {\n    for (const value of Array.from(altSets.values())) {\n        if (Object.keys(value).length === 1) {\n            return true;\n        }\n    }\n    return false;\n}\n//# sourceMappingURL=all-star-lookahead.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/atn.js":
/*!****************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/atn.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ATN_BASIC: () => (/* binding */ ATN_BASIC),\n/* harmony export */   ATN_BLOCK_END: () => (/* binding */ ATN_BLOCK_END),\n/* harmony export */   ATN_INVALID_TYPE: () => (/* binding */ ATN_INVALID_TYPE),\n/* harmony export */   ATN_LOOP_END: () => (/* binding */ ATN_LOOP_END),\n/* harmony export */   ATN_PLUS_BLOCK_START: () => (/* binding */ ATN_PLUS_BLOCK_START),\n/* harmony export */   ATN_PLUS_LOOP_BACK: () => (/* binding */ ATN_PLUS_LOOP_BACK),\n/* harmony export */   ATN_RULE_START: () => (/* binding */ ATN_RULE_START),\n/* harmony export */   ATN_RULE_STOP: () => (/* binding */ ATN_RULE_STOP),\n/* harmony export */   ATN_STAR_BLOCK_START: () => (/* binding */ ATN_STAR_BLOCK_START),\n/* harmony export */   ATN_STAR_LOOP_BACK: () => (/* binding */ ATN_STAR_LOOP_BACK),\n/* harmony export */   ATN_STAR_LOOP_ENTRY: () => (/* binding */ ATN_STAR_LOOP_ENTRY),\n/* harmony export */   ATN_TOKEN_START: () => (/* binding */ ATN_TOKEN_START),\n/* harmony export */   AbstractTransition: () => (/* binding */ AbstractTransition),\n/* harmony export */   AtomTransition: () => (/* binding */ AtomTransition),\n/* harmony export */   EpsilonTransition: () => (/* binding */ EpsilonTransition),\n/* harmony export */   RuleTransition: () => (/* binding */ RuleTransition),\n/* harmony export */   buildATNKey: () => (/* binding */ buildATNKey),\n/* harmony export */   createATN: () => (/* binding */ createATN)\n/* harmony export */ });\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/* harmony import */ var lodash_es_filter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash-es/filter.js */ \"(ssr)/./node_modules/lodash-es/filter.js\");\n/* harmony import */ var chevrotain__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! chevrotain */ \"(ssr)/./node_modules/chevrotain/lib/src/api.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n\n\nfunction buildATNKey(rule, type, occurrence) {\n    return `${rule.name}_${type}_${occurrence}`;\n}\nconst ATN_INVALID_TYPE = 0;\nconst ATN_BASIC = 1;\nconst ATN_RULE_START = 2;\nconst ATN_PLUS_BLOCK_START = 4;\nconst ATN_STAR_BLOCK_START = 5;\n// Currently unused as the ATN is not used for lexing\nconst ATN_TOKEN_START = 6;\nconst ATN_RULE_STOP = 7;\nconst ATN_BLOCK_END = 8;\nconst ATN_STAR_LOOP_BACK = 9;\nconst ATN_STAR_LOOP_ENTRY = 10;\nconst ATN_PLUS_LOOP_BACK = 11;\nconst ATN_LOOP_END = 12;\nclass AbstractTransition {\n    constructor(target) {\n        this.target = target;\n    }\n    isEpsilon() {\n        return false;\n    }\n}\nclass AtomTransition extends AbstractTransition {\n    constructor(target, tokenType) {\n        super(target);\n        this.tokenType = tokenType;\n    }\n}\nclass EpsilonTransition extends AbstractTransition {\n    constructor(target) {\n        super(target);\n    }\n    isEpsilon() {\n        return true;\n    }\n}\nclass RuleTransition extends AbstractTransition {\n    constructor(ruleStart, rule, followState) {\n        super(ruleStart);\n        this.rule = rule;\n        this.followState = followState;\n    }\n    isEpsilon() {\n        return true;\n    }\n}\nfunction createATN(rules) {\n    const atn = {\n        decisionMap: {},\n        decisionStates: [],\n        ruleToStartState: new Map(),\n        ruleToStopState: new Map(),\n        states: []\n    };\n    createRuleStartAndStopATNStates(atn, rules);\n    const ruleLength = rules.length;\n    for (let i = 0; i < ruleLength; i++) {\n        const rule = rules[i];\n        const ruleBlock = block(atn, rule, rule);\n        if (ruleBlock === undefined) {\n            continue;\n        }\n        buildRuleHandle(atn, rule, ruleBlock);\n    }\n    return atn;\n}\nfunction createRuleStartAndStopATNStates(atn, rules) {\n    const ruleLength = rules.length;\n    for (let i = 0; i < ruleLength; i++) {\n        const rule = rules[i];\n        const start = newState(atn, rule, undefined, {\n            type: ATN_RULE_START\n        });\n        const stop = newState(atn, rule, undefined, {\n            type: ATN_RULE_STOP\n        });\n        start.stop = stop;\n        atn.ruleToStartState.set(rule, start);\n        atn.ruleToStopState.set(rule, stop);\n    }\n}\nfunction atom(atn, rule, production) {\n    if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Terminal) {\n        return tokenRef(atn, rule, production.terminalType, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.NonTerminal) {\n        return ruleRef(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return alternation(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return option(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return repetition(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return repetitionSep(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return repetitionMandatory(atn, rule, production);\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return repetitionMandatorySep(atn, rule, production);\n    }\n    else {\n        return block(atn, rule, production);\n    }\n}\nfunction repetition(atn, rule, repetition) {\n    const starState = newState(atn, rule, repetition, {\n        type: ATN_STAR_BLOCK_START\n    });\n    defineDecisionState(atn, starState);\n    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n    return star(atn, rule, repetition, handle);\n}\nfunction repetitionSep(atn, rule, repetition) {\n    const starState = newState(atn, rule, repetition, {\n        type: ATN_STAR_BLOCK_START\n    });\n    defineDecisionState(atn, starState);\n    const handle = makeAlts(atn, rule, starState, repetition, block(atn, rule, repetition));\n    const sep = tokenRef(atn, rule, repetition.separator, repetition);\n    return star(atn, rule, repetition, handle, sep);\n}\nfunction repetitionMandatory(atn, rule, repetition) {\n    const plusState = newState(atn, rule, repetition, {\n        type: ATN_PLUS_BLOCK_START\n    });\n    defineDecisionState(atn, plusState);\n    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n    return plus(atn, rule, repetition, handle);\n}\nfunction repetitionMandatorySep(atn, rule, repetition) {\n    const plusState = newState(atn, rule, repetition, {\n        type: ATN_PLUS_BLOCK_START\n    });\n    defineDecisionState(atn, plusState);\n    const handle = makeAlts(atn, rule, plusState, repetition, block(atn, rule, repetition));\n    const sep = tokenRef(atn, rule, repetition.separator, repetition);\n    return plus(atn, rule, repetition, handle, sep);\n}\nfunction alternation(atn, rule, alternation) {\n    const start = newState(atn, rule, alternation, {\n        type: ATN_BASIC\n    });\n    defineDecisionState(atn, start);\n    const alts = (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(alternation.definition, (e) => atom(atn, rule, e));\n    const handle = makeAlts(atn, rule, start, alternation, ...alts);\n    return handle;\n}\nfunction option(atn, rule, option) {\n    const start = newState(atn, rule, option, {\n        type: ATN_BASIC\n    });\n    defineDecisionState(atn, start);\n    const handle = makeAlts(atn, rule, start, option, block(atn, rule, option));\n    return optional(atn, rule, option, handle);\n}\nfunction block(atn, rule, block) {\n    const handles = (0,lodash_es_filter_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(block.definition, (e) => atom(atn, rule, e)), (e) => e !== undefined);\n    if (handles.length === 1) {\n        return handles[0];\n    }\n    else if (handles.length === 0) {\n        return undefined;\n    }\n    else {\n        return makeBlock(atn, handles);\n    }\n}\nfunction plus(atn, rule, plus, handle, sep) {\n    const blkStart = handle.left;\n    const blkEnd = handle.right;\n    const loop = newState(atn, rule, plus, {\n        type: ATN_PLUS_LOOP_BACK\n    });\n    defineDecisionState(atn, loop);\n    const end = newState(atn, rule, plus, {\n        type: ATN_LOOP_END\n    });\n    blkStart.loopback = loop;\n    end.loopback = loop;\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionMandatoryWithSeparator' : 'RepetitionMandatory', plus.idx)] = loop;\n    epsilon(blkEnd, loop); // block can see loop back\n    // Depending on whether we have a separator we put the exit transition at index 1 or 0\n    // This influences the chosen option in the lookahead DFA\n    if (sep === undefined) {\n        epsilon(loop, blkStart); // loop back to start\n        epsilon(loop, end); // exit\n    }\n    else {\n        epsilon(loop, end); // exit\n        // loop back to start with separator\n        epsilon(loop, sep.left);\n        epsilon(sep.right, blkStart);\n    }\n    return {\n        left: blkStart,\n        right: end\n    };\n}\nfunction star(atn, rule, star, handle, sep) {\n    const start = handle.left;\n    const end = handle.right;\n    const entry = newState(atn, rule, star, {\n        type: ATN_STAR_LOOP_ENTRY\n    });\n    defineDecisionState(atn, entry);\n    const loopEnd = newState(atn, rule, star, {\n        type: ATN_LOOP_END\n    });\n    const loop = newState(atn, rule, star, {\n        type: ATN_STAR_LOOP_BACK\n    });\n    entry.loopback = loop;\n    loopEnd.loopback = loop;\n    epsilon(entry, start); // loop enter edge (alt 2)\n    epsilon(entry, loopEnd); // bypass loop edge (alt 1)\n    epsilon(end, loop); // block end hits loop back\n    if (sep !== undefined) {\n        epsilon(loop, loopEnd); // end loop\n        // loop back to start of handle using separator\n        epsilon(loop, sep.left);\n        epsilon(sep.right, start);\n    }\n    else {\n        epsilon(loop, entry); // loop back to entry/exit decision\n    }\n    atn.decisionMap[buildATNKey(rule, sep ? 'RepetitionWithSeparator' : 'Repetition', star.idx)] = entry;\n    return {\n        left: entry,\n        right: loopEnd\n    };\n}\nfunction optional(atn, rule, optional, handle) {\n    const start = handle.left;\n    const end = handle.right;\n    epsilon(start, end);\n    atn.decisionMap[buildATNKey(rule, 'Option', optional.idx)] = start;\n    return handle;\n}\nfunction defineDecisionState(atn, state) {\n    atn.decisionStates.push(state);\n    state.decision = atn.decisionStates.length - 1;\n    return state.decision;\n}\nfunction makeAlts(atn, rule, start, production, ...alts) {\n    const end = newState(atn, rule, production, {\n        type: ATN_BLOCK_END,\n        start\n    });\n    start.end = end;\n    for (const alt of alts) {\n        if (alt !== undefined) {\n            // hook alts up to decision block\n            epsilon(start, alt.left);\n            epsilon(alt.right, end);\n        }\n        else {\n            epsilon(start, end);\n        }\n    }\n    const handle = {\n        left: start,\n        right: end\n    };\n    atn.decisionMap[buildATNKey(rule, getProdType(production), production.idx)] = start;\n    return handle;\n}\nfunction getProdType(production) {\n    if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Alternation) {\n        return 'Alternation';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Option) {\n        return 'Option';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.Repetition) {\n        return 'Repetition';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionWithSeparator) {\n        return 'RepetitionWithSeparator';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatory) {\n        return 'RepetitionMandatory';\n    }\n    else if (production instanceof chevrotain__WEBPACK_IMPORTED_MODULE_0__.RepetitionMandatoryWithSeparator) {\n        return 'RepetitionMandatoryWithSeparator';\n    }\n    else {\n        throw new Error('Invalid production type encountered');\n    }\n}\nfunction makeBlock(atn, alts) {\n    const altsLength = alts.length;\n    for (let i = 0; i < altsLength - 1; i++) {\n        const handle = alts[i];\n        let transition;\n        if (handle.left.transitions.length === 1) {\n            transition = handle.left.transitions[0];\n        }\n        const isRuleTransition = transition instanceof RuleTransition;\n        const ruleTransition = transition;\n        const next = alts[i + 1].left;\n        if (handle.left.type === ATN_BASIC &&\n            handle.right.type === ATN_BASIC &&\n            transition !== undefined &&\n            ((isRuleTransition && ruleTransition.followState === handle.right) ||\n                transition.target === handle.right)) {\n            // we can avoid epsilon edge to next element\n            if (isRuleTransition) {\n                ruleTransition.followState = next;\n            }\n            else {\n                transition.target = next;\n            }\n            removeState(atn, handle.right); // we skipped over this state\n        }\n        else {\n            // need epsilon if previous block's right end node is complex\n            epsilon(handle.right, next);\n        }\n    }\n    const first = alts[0];\n    const last = alts[altsLength - 1];\n    return {\n        left: first.left,\n        right: last.right\n    };\n}\nfunction tokenRef(atn, rule, tokenType, production) {\n    const left = newState(atn, rule, production, {\n        type: ATN_BASIC\n    });\n    const right = newState(atn, rule, production, {\n        type: ATN_BASIC\n    });\n    addTransition(left, new AtomTransition(right, tokenType));\n    return {\n        left,\n        right\n    };\n}\nfunction ruleRef(atn, currentRule, nonTerminal) {\n    const rule = nonTerminal.referencedRule;\n    const start = atn.ruleToStartState.get(rule);\n    const left = newState(atn, currentRule, nonTerminal, {\n        type: ATN_BASIC\n    });\n    const right = newState(atn, currentRule, nonTerminal, {\n        type: ATN_BASIC\n    });\n    const call = new RuleTransition(start, rule, right);\n    addTransition(left, call);\n    return {\n        left,\n        right\n    };\n}\nfunction buildRuleHandle(atn, rule, block) {\n    const start = atn.ruleToStartState.get(rule);\n    epsilon(start, block.left);\n    const stop = atn.ruleToStopState.get(rule);\n    epsilon(block.right, stop);\n    const handle = {\n        left: start,\n        right: stop\n    };\n    return handle;\n}\nfunction epsilon(a, b) {\n    const transition = new EpsilonTransition(b);\n    addTransition(a, transition);\n}\nfunction newState(atn, rule, production, partial) {\n    const t = Object.assign({ atn,\n        production, epsilonOnlyTransitions: false, rule, transitions: [], nextTokenWithinRule: [], stateNumber: atn.states.length }, partial);\n    atn.states.push(t);\n    return t;\n}\nfunction addTransition(state, transition) {\n    // A single ATN state can only contain epsilon transitions or non-epsilon transitions\n    // Because they are never mixed, only setting the property for the first transition is fine\n    if (state.transitions.length === 0) {\n        state.epsilonOnlyTransitions = transition.isEpsilon();\n    }\n    state.transitions.push(transition);\n}\nfunction removeState(atn, state) {\n    atn.states.splice(atn.states.indexOf(state), 1);\n}\n//# sourceMappingURL=atn.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hldnJvdGFpbi1hbGxzdGFyL2xpYi9hdG4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNtQztBQUNNO0FBQzJIO0FBQzdKO0FBQ1AsY0FBYyxVQUFVLEdBQUcsS0FBSyxHQUFHLFdBQVc7QUFDOUM7QUFDTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGdCQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsZ0RBQVE7QUFDdEM7QUFDQTtBQUNBLG1DQUFtQyxtREFBVztBQUM5QztBQUNBO0FBQ0EsbUNBQW1DLG1EQUFXO0FBQzlDO0FBQ0E7QUFDQSxtQ0FBbUMsOENBQU07QUFDekM7QUFDQTtBQUNBLG1DQUFtQyxrREFBVTtBQUM3QztBQUNBO0FBQ0EsbUNBQW1DLCtEQUF1QjtBQUMxRDtBQUNBO0FBQ0EsbUNBQW1DLDJEQUFtQjtBQUN0RDtBQUNBO0FBQ0EsbUNBQW1DLHdFQUFnQztBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsaUJBQWlCLDREQUFHO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLCtEQUFNLENBQUMsNERBQUc7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQiw2QkFBNkI7QUFDN0Isd0JBQXdCO0FBQ3hCO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixtREFBVztBQUN6QztBQUNBO0FBQ0EsbUNBQW1DLDhDQUFNO0FBQ3pDO0FBQ0E7QUFDQSxtQ0FBbUMsa0RBQVU7QUFDN0M7QUFDQTtBQUNBLG1DQUFtQywrREFBdUI7QUFDMUQ7QUFDQTtBQUNBLG1DQUFtQywyREFBbUI7QUFDdEQ7QUFDQTtBQUNBLG1DQUFtQyx3RUFBZ0M7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixvQkFBb0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCLG1JQUFtSTtBQUNuSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcY2hldnJvdGFpbi1hbGxzdGFyXFxsaWJcXGF0bi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqXG4gKiBDb3B5cmlnaHQgMjAyMiBUeXBlRm94IEdtYkhcbiAqIFRoaXMgcHJvZ3JhbSBhbmQgdGhlIGFjY29tcGFueWluZyBtYXRlcmlhbHMgYXJlIG1hZGUgYXZhaWxhYmxlIHVuZGVyIHRoZVxuICogdGVybXMgb2YgdGhlIE1JVCBMaWNlbnNlLCB3aGljaCBpcyBhdmFpbGFibGUgaW4gdGhlIHByb2plY3Qgcm9vdC5cbiAqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiovXG5pbXBvcnQgbWFwIGZyb20gXCJsb2Rhc2gtZXMvbWFwLmpzXCI7XG5pbXBvcnQgZmlsdGVyIGZyb20gXCJsb2Rhc2gtZXMvZmlsdGVyLmpzXCI7XG5pbXBvcnQgeyBBbHRlcm5hdGlvbiwgTm9uVGVybWluYWwsIE9wdGlvbiwgUmVwZXRpdGlvbk1hbmRhdG9yeSwgUmVwZXRpdGlvbiwgVGVybWluYWwsIFJlcGV0aXRpb25XaXRoU2VwYXJhdG9yLCBSZXBldGl0aW9uTWFuZGF0b3J5V2l0aFNlcGFyYXRvciB9IGZyb20gXCJjaGV2cm90YWluXCI7XG5leHBvcnQgZnVuY3Rpb24gYnVpbGRBVE5LZXkocnVsZSwgdHlwZSwgb2NjdXJyZW5jZSkge1xuICAgIHJldHVybiBgJHtydWxlLm5hbWV9XyR7dHlwZX1fJHtvY2N1cnJlbmNlfWA7XG59XG5leHBvcnQgY29uc3QgQVROX0lOVkFMSURfVFlQRSA9IDA7XG5leHBvcnQgY29uc3QgQVROX0JBU0lDID0gMTtcbmV4cG9ydCBjb25zdCBBVE5fUlVMRV9TVEFSVCA9IDI7XG5leHBvcnQgY29uc3QgQVROX1BMVVNfQkxPQ0tfU1RBUlQgPSA0O1xuZXhwb3J0IGNvbnN0IEFUTl9TVEFSX0JMT0NLX1NUQVJUID0gNTtcbi8vIEN1cnJlbnRseSB1bnVzZWQgYXMgdGhlIEFUTiBpcyBub3QgdXNlZCBmb3IgbGV4aW5nXG5leHBvcnQgY29uc3QgQVROX1RPS0VOX1NUQVJUID0gNjtcbmV4cG9ydCBjb25zdCBBVE5fUlVMRV9TVE9QID0gNztcbmV4cG9ydCBjb25zdCBBVE5fQkxPQ0tfRU5EID0gODtcbmV4cG9ydCBjb25zdCBBVE5fU1RBUl9MT09QX0JBQ0sgPSA5O1xuZXhwb3J0IGNvbnN0IEFUTl9TVEFSX0xPT1BfRU5UUlkgPSAxMDtcbmV4cG9ydCBjb25zdCBBVE5fUExVU19MT09QX0JBQ0sgPSAxMTtcbmV4cG9ydCBjb25zdCBBVE5fTE9PUF9FTkQgPSAxMjtcbmV4cG9ydCBjbGFzcyBBYnN0cmFjdFRyYW5zaXRpb24ge1xuICAgIGNvbnN0cnVjdG9yKHRhcmdldCkge1xuICAgICAgICB0aGlzLnRhcmdldCA9IHRhcmdldDtcbiAgICB9XG4gICAgaXNFcHNpbG9uKCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIEF0b21UcmFuc2l0aW9uIGV4dGVuZHMgQWJzdHJhY3RUcmFuc2l0aW9uIHtcbiAgICBjb25zdHJ1Y3Rvcih0YXJnZXQsIHRva2VuVHlwZSkge1xuICAgICAgICBzdXBlcih0YXJnZXQpO1xuICAgICAgICB0aGlzLnRva2VuVHlwZSA9IHRva2VuVHlwZTtcbiAgICB9XG59XG5leHBvcnQgY2xhc3MgRXBzaWxvblRyYW5zaXRpb24gZXh0ZW5kcyBBYnN0cmFjdFRyYW5zaXRpb24ge1xuICAgIGNvbnN0cnVjdG9yKHRhcmdldCkge1xuICAgICAgICBzdXBlcih0YXJnZXQpO1xuICAgIH1cbiAgICBpc0Vwc2lsb24oKSB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBSdWxlVHJhbnNpdGlvbiBleHRlbmRzIEFic3RyYWN0VHJhbnNpdGlvbiB7XG4gICAgY29uc3RydWN0b3IocnVsZVN0YXJ0LCBydWxlLCBmb2xsb3dTdGF0ZSkge1xuICAgICAgICBzdXBlcihydWxlU3RhcnQpO1xuICAgICAgICB0aGlzLnJ1bGUgPSBydWxlO1xuICAgICAgICB0aGlzLmZvbGxvd1N0YXRlID0gZm9sbG93U3RhdGU7XG4gICAgfVxuICAgIGlzRXBzaWxvbigpIHtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxufVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUFUTihydWxlcykge1xuICAgIGNvbnN0IGF0biA9IHtcbiAgICAgICAgZGVjaXNpb25NYXA6IHt9LFxuICAgICAgICBkZWNpc2lvblN0YXRlczogW10sXG4gICAgICAgIHJ1bGVUb1N0YXJ0U3RhdGU6IG5ldyBNYXAoKSxcbiAgICAgICAgcnVsZVRvU3RvcFN0YXRlOiBuZXcgTWFwKCksXG4gICAgICAgIHN0YXRlczogW11cbiAgICB9O1xuICAgIGNyZWF0ZVJ1bGVTdGFydEFuZFN0b3BBVE5TdGF0ZXMoYXRuLCBydWxlcyk7XG4gICAgY29uc3QgcnVsZUxlbmd0aCA9IHJ1bGVzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHJ1bGVMZW5ndGg7IGkrKykge1xuICAgICAgICBjb25zdCBydWxlID0gcnVsZXNbaV07XG4gICAgICAgIGNvbnN0IHJ1bGVCbG9jayA9IGJsb2NrKGF0biwgcnVsZSwgcnVsZSk7XG4gICAgICAgIGlmIChydWxlQmxvY2sgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgYnVpbGRSdWxlSGFuZGxlKGF0biwgcnVsZSwgcnVsZUJsb2NrKTtcbiAgICB9XG4gICAgcmV0dXJuIGF0bjtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVJ1bGVTdGFydEFuZFN0b3BBVE5TdGF0ZXMoYXRuLCBydWxlcykge1xuICAgIGNvbnN0IHJ1bGVMZW5ndGggPSBydWxlcy5sZW5ndGg7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBydWxlTGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgcnVsZSA9IHJ1bGVzW2ldO1xuICAgICAgICBjb25zdCBzdGFydCA9IG5ld1N0YXRlKGF0biwgcnVsZSwgdW5kZWZpbmVkLCB7XG4gICAgICAgICAgICB0eXBlOiBBVE5fUlVMRV9TVEFSVFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3Qgc3RvcCA9IG5ld1N0YXRlKGF0biwgcnVsZSwgdW5kZWZpbmVkLCB7XG4gICAgICAgICAgICB0eXBlOiBBVE5fUlVMRV9TVE9QXG4gICAgICAgIH0pO1xuICAgICAgICBzdGFydC5zdG9wID0gc3RvcDtcbiAgICAgICAgYXRuLnJ1bGVUb1N0YXJ0U3RhdGUuc2V0KHJ1bGUsIHN0YXJ0KTtcbiAgICAgICAgYXRuLnJ1bGVUb1N0b3BTdGF0ZS5zZXQocnVsZSwgc3RvcCk7XG4gICAgfVxufVxuZnVuY3Rpb24gYXRvbShhdG4sIHJ1bGUsIHByb2R1Y3Rpb24pIHtcbiAgICBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIFRlcm1pbmFsKSB7XG4gICAgICAgIHJldHVybiB0b2tlblJlZihhdG4sIHJ1bGUsIHByb2R1Y3Rpb24udGVybWluYWxUeXBlLCBwcm9kdWN0aW9uKTtcbiAgICB9XG4gICAgZWxzZSBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIE5vblRlcm1pbmFsKSB7XG4gICAgICAgIHJldHVybiBydWxlUmVmKGF0biwgcnVsZSwgcHJvZHVjdGlvbik7XG4gICAgfVxuICAgIGVsc2UgaWYgKHByb2R1Y3Rpb24gaW5zdGFuY2VvZiBBbHRlcm5hdGlvbikge1xuICAgICAgICByZXR1cm4gYWx0ZXJuYXRpb24oYXRuLCBydWxlLCBwcm9kdWN0aW9uKTtcbiAgICB9XG4gICAgZWxzZSBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIE9wdGlvbikge1xuICAgICAgICByZXR1cm4gb3B0aW9uKGF0biwgcnVsZSwgcHJvZHVjdGlvbik7XG4gICAgfVxuICAgIGVsc2UgaWYgKHByb2R1Y3Rpb24gaW5zdGFuY2VvZiBSZXBldGl0aW9uKSB7XG4gICAgICAgIHJldHVybiByZXBldGl0aW9uKGF0biwgcnVsZSwgcHJvZHVjdGlvbik7XG4gICAgfVxuICAgIGVsc2UgaWYgKHByb2R1Y3Rpb24gaW5zdGFuY2VvZiBSZXBldGl0aW9uV2l0aFNlcGFyYXRvcikge1xuICAgICAgICByZXR1cm4gcmVwZXRpdGlvblNlcChhdG4sIHJ1bGUsIHByb2R1Y3Rpb24pO1xuICAgIH1cbiAgICBlbHNlIGlmIChwcm9kdWN0aW9uIGluc3RhbmNlb2YgUmVwZXRpdGlvbk1hbmRhdG9yeSkge1xuICAgICAgICByZXR1cm4gcmVwZXRpdGlvbk1hbmRhdG9yeShhdG4sIHJ1bGUsIHByb2R1Y3Rpb24pO1xuICAgIH1cbiAgICBlbHNlIGlmIChwcm9kdWN0aW9uIGluc3RhbmNlb2YgUmVwZXRpdGlvbk1hbmRhdG9yeVdpdGhTZXBhcmF0b3IpIHtcbiAgICAgICAgcmV0dXJuIHJlcGV0aXRpb25NYW5kYXRvcnlTZXAoYXRuLCBydWxlLCBwcm9kdWN0aW9uKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBibG9jayhhdG4sIHJ1bGUsIHByb2R1Y3Rpb24pO1xuICAgIH1cbn1cbmZ1bmN0aW9uIHJlcGV0aXRpb24oYXRuLCBydWxlLCByZXBldGl0aW9uKSB7XG4gICAgY29uc3Qgc3RhclN0YXRlID0gbmV3U3RhdGUoYXRuLCBydWxlLCByZXBldGl0aW9uLCB7XG4gICAgICAgIHR5cGU6IEFUTl9TVEFSX0JMT0NLX1NUQVJUXG4gICAgfSk7XG4gICAgZGVmaW5lRGVjaXNpb25TdGF0ZShhdG4sIHN0YXJTdGF0ZSk7XG4gICAgY29uc3QgaGFuZGxlID0gbWFrZUFsdHMoYXRuLCBydWxlLCBzdGFyU3RhdGUsIHJlcGV0aXRpb24sIGJsb2NrKGF0biwgcnVsZSwgcmVwZXRpdGlvbikpO1xuICAgIHJldHVybiBzdGFyKGF0biwgcnVsZSwgcmVwZXRpdGlvbiwgaGFuZGxlKTtcbn1cbmZ1bmN0aW9uIHJlcGV0aXRpb25TZXAoYXRuLCBydWxlLCByZXBldGl0aW9uKSB7XG4gICAgY29uc3Qgc3RhclN0YXRlID0gbmV3U3RhdGUoYXRuLCBydWxlLCByZXBldGl0aW9uLCB7XG4gICAgICAgIHR5cGU6IEFUTl9TVEFSX0JMT0NLX1NUQVJUXG4gICAgfSk7XG4gICAgZGVmaW5lRGVjaXNpb25TdGF0ZShhdG4sIHN0YXJTdGF0ZSk7XG4gICAgY29uc3QgaGFuZGxlID0gbWFrZUFsdHMoYXRuLCBydWxlLCBzdGFyU3RhdGUsIHJlcGV0aXRpb24sIGJsb2NrKGF0biwgcnVsZSwgcmVwZXRpdGlvbikpO1xuICAgIGNvbnN0IHNlcCA9IHRva2VuUmVmKGF0biwgcnVsZSwgcmVwZXRpdGlvbi5zZXBhcmF0b3IsIHJlcGV0aXRpb24pO1xuICAgIHJldHVybiBzdGFyKGF0biwgcnVsZSwgcmVwZXRpdGlvbiwgaGFuZGxlLCBzZXApO1xufVxuZnVuY3Rpb24gcmVwZXRpdGlvbk1hbmRhdG9yeShhdG4sIHJ1bGUsIHJlcGV0aXRpb24pIHtcbiAgICBjb25zdCBwbHVzU3RhdGUgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIHJlcGV0aXRpb24sIHtcbiAgICAgICAgdHlwZTogQVROX1BMVVNfQkxPQ0tfU1RBUlRcbiAgICB9KTtcbiAgICBkZWZpbmVEZWNpc2lvblN0YXRlKGF0biwgcGx1c1N0YXRlKTtcbiAgICBjb25zdCBoYW5kbGUgPSBtYWtlQWx0cyhhdG4sIHJ1bGUsIHBsdXNTdGF0ZSwgcmVwZXRpdGlvbiwgYmxvY2soYXRuLCBydWxlLCByZXBldGl0aW9uKSk7XG4gICAgcmV0dXJuIHBsdXMoYXRuLCBydWxlLCByZXBldGl0aW9uLCBoYW5kbGUpO1xufVxuZnVuY3Rpb24gcmVwZXRpdGlvbk1hbmRhdG9yeVNlcChhdG4sIHJ1bGUsIHJlcGV0aXRpb24pIHtcbiAgICBjb25zdCBwbHVzU3RhdGUgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIHJlcGV0aXRpb24sIHtcbiAgICAgICAgdHlwZTogQVROX1BMVVNfQkxPQ0tfU1RBUlRcbiAgICB9KTtcbiAgICBkZWZpbmVEZWNpc2lvblN0YXRlKGF0biwgcGx1c1N0YXRlKTtcbiAgICBjb25zdCBoYW5kbGUgPSBtYWtlQWx0cyhhdG4sIHJ1bGUsIHBsdXNTdGF0ZSwgcmVwZXRpdGlvbiwgYmxvY2soYXRuLCBydWxlLCByZXBldGl0aW9uKSk7XG4gICAgY29uc3Qgc2VwID0gdG9rZW5SZWYoYXRuLCBydWxlLCByZXBldGl0aW9uLnNlcGFyYXRvciwgcmVwZXRpdGlvbik7XG4gICAgcmV0dXJuIHBsdXMoYXRuLCBydWxlLCByZXBldGl0aW9uLCBoYW5kbGUsIHNlcCk7XG59XG5mdW5jdGlvbiBhbHRlcm5hdGlvbihhdG4sIHJ1bGUsIGFsdGVybmF0aW9uKSB7XG4gICAgY29uc3Qgc3RhcnQgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIGFsdGVybmF0aW9uLCB7XG4gICAgICAgIHR5cGU6IEFUTl9CQVNJQ1xuICAgIH0pO1xuICAgIGRlZmluZURlY2lzaW9uU3RhdGUoYXRuLCBzdGFydCk7XG4gICAgY29uc3QgYWx0cyA9IG1hcChhbHRlcm5hdGlvbi5kZWZpbml0aW9uLCAoZSkgPT4gYXRvbShhdG4sIHJ1bGUsIGUpKTtcbiAgICBjb25zdCBoYW5kbGUgPSBtYWtlQWx0cyhhdG4sIHJ1bGUsIHN0YXJ0LCBhbHRlcm5hdGlvbiwgLi4uYWx0cyk7XG4gICAgcmV0dXJuIGhhbmRsZTtcbn1cbmZ1bmN0aW9uIG9wdGlvbihhdG4sIHJ1bGUsIG9wdGlvbikge1xuICAgIGNvbnN0IHN0YXJ0ID0gbmV3U3RhdGUoYXRuLCBydWxlLCBvcHRpb24sIHtcbiAgICAgICAgdHlwZTogQVROX0JBU0lDXG4gICAgfSk7XG4gICAgZGVmaW5lRGVjaXNpb25TdGF0ZShhdG4sIHN0YXJ0KTtcbiAgICBjb25zdCBoYW5kbGUgPSBtYWtlQWx0cyhhdG4sIHJ1bGUsIHN0YXJ0LCBvcHRpb24sIGJsb2NrKGF0biwgcnVsZSwgb3B0aW9uKSk7XG4gICAgcmV0dXJuIG9wdGlvbmFsKGF0biwgcnVsZSwgb3B0aW9uLCBoYW5kbGUpO1xufVxuZnVuY3Rpb24gYmxvY2soYXRuLCBydWxlLCBibG9jaykge1xuICAgIGNvbnN0IGhhbmRsZXMgPSBmaWx0ZXIobWFwKGJsb2NrLmRlZmluaXRpb24sIChlKSA9PiBhdG9tKGF0biwgcnVsZSwgZSkpLCAoZSkgPT4gZSAhPT0gdW5kZWZpbmVkKTtcbiAgICBpZiAoaGFuZGxlcy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgcmV0dXJuIGhhbmRsZXNbMF07XG4gICAgfVxuICAgIGVsc2UgaWYgKGhhbmRsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4gbWFrZUJsb2NrKGF0biwgaGFuZGxlcyk7XG4gICAgfVxufVxuZnVuY3Rpb24gcGx1cyhhdG4sIHJ1bGUsIHBsdXMsIGhhbmRsZSwgc2VwKSB7XG4gICAgY29uc3QgYmxrU3RhcnQgPSBoYW5kbGUubGVmdDtcbiAgICBjb25zdCBibGtFbmQgPSBoYW5kbGUucmlnaHQ7XG4gICAgY29uc3QgbG9vcCA9IG5ld1N0YXRlKGF0biwgcnVsZSwgcGx1cywge1xuICAgICAgICB0eXBlOiBBVE5fUExVU19MT09QX0JBQ0tcbiAgICB9KTtcbiAgICBkZWZpbmVEZWNpc2lvblN0YXRlKGF0biwgbG9vcCk7XG4gICAgY29uc3QgZW5kID0gbmV3U3RhdGUoYXRuLCBydWxlLCBwbHVzLCB7XG4gICAgICAgIHR5cGU6IEFUTl9MT09QX0VORFxuICAgIH0pO1xuICAgIGJsa1N0YXJ0Lmxvb3BiYWNrID0gbG9vcDtcbiAgICBlbmQubG9vcGJhY2sgPSBsb29wO1xuICAgIGF0bi5kZWNpc2lvbk1hcFtidWlsZEFUTktleShydWxlLCBzZXAgPyAnUmVwZXRpdGlvbk1hbmRhdG9yeVdpdGhTZXBhcmF0b3InIDogJ1JlcGV0aXRpb25NYW5kYXRvcnknLCBwbHVzLmlkeCldID0gbG9vcDtcbiAgICBlcHNpbG9uKGJsa0VuZCwgbG9vcCk7IC8vIGJsb2NrIGNhbiBzZWUgbG9vcCBiYWNrXG4gICAgLy8gRGVwZW5kaW5nIG9uIHdoZXRoZXIgd2UgaGF2ZSBhIHNlcGFyYXRvciB3ZSBwdXQgdGhlIGV4aXQgdHJhbnNpdGlvbiBhdCBpbmRleCAxIG9yIDBcbiAgICAvLyBUaGlzIGluZmx1ZW5jZXMgdGhlIGNob3NlbiBvcHRpb24gaW4gdGhlIGxvb2thaGVhZCBERkFcbiAgICBpZiAoc2VwID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgZXBzaWxvbihsb29wLCBibGtTdGFydCk7IC8vIGxvb3AgYmFjayB0byBzdGFydFxuICAgICAgICBlcHNpbG9uKGxvb3AsIGVuZCk7IC8vIGV4aXRcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGVwc2lsb24obG9vcCwgZW5kKTsgLy8gZXhpdFxuICAgICAgICAvLyBsb29wIGJhY2sgdG8gc3RhcnQgd2l0aCBzZXBhcmF0b3JcbiAgICAgICAgZXBzaWxvbihsb29wLCBzZXAubGVmdCk7XG4gICAgICAgIGVwc2lsb24oc2VwLnJpZ2h0LCBibGtTdGFydCk7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIGxlZnQ6IGJsa1N0YXJ0LFxuICAgICAgICByaWdodDogZW5kXG4gICAgfTtcbn1cbmZ1bmN0aW9uIHN0YXIoYXRuLCBydWxlLCBzdGFyLCBoYW5kbGUsIHNlcCkge1xuICAgIGNvbnN0IHN0YXJ0ID0gaGFuZGxlLmxlZnQ7XG4gICAgY29uc3QgZW5kID0gaGFuZGxlLnJpZ2h0O1xuICAgIGNvbnN0IGVudHJ5ID0gbmV3U3RhdGUoYXRuLCBydWxlLCBzdGFyLCB7XG4gICAgICAgIHR5cGU6IEFUTl9TVEFSX0xPT1BfRU5UUllcbiAgICB9KTtcbiAgICBkZWZpbmVEZWNpc2lvblN0YXRlKGF0biwgZW50cnkpO1xuICAgIGNvbnN0IGxvb3BFbmQgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIHN0YXIsIHtcbiAgICAgICAgdHlwZTogQVROX0xPT1BfRU5EXG4gICAgfSk7XG4gICAgY29uc3QgbG9vcCA9IG5ld1N0YXRlKGF0biwgcnVsZSwgc3Rhciwge1xuICAgICAgICB0eXBlOiBBVE5fU1RBUl9MT09QX0JBQ0tcbiAgICB9KTtcbiAgICBlbnRyeS5sb29wYmFjayA9IGxvb3A7XG4gICAgbG9vcEVuZC5sb29wYmFjayA9IGxvb3A7XG4gICAgZXBzaWxvbihlbnRyeSwgc3RhcnQpOyAvLyBsb29wIGVudGVyIGVkZ2UgKGFsdCAyKVxuICAgIGVwc2lsb24oZW50cnksIGxvb3BFbmQpOyAvLyBieXBhc3MgbG9vcCBlZGdlIChhbHQgMSlcbiAgICBlcHNpbG9uKGVuZCwgbG9vcCk7IC8vIGJsb2NrIGVuZCBoaXRzIGxvb3AgYmFja1xuICAgIGlmIChzZXAgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBlcHNpbG9uKGxvb3AsIGxvb3BFbmQpOyAvLyBlbmQgbG9vcFxuICAgICAgICAvLyBsb29wIGJhY2sgdG8gc3RhcnQgb2YgaGFuZGxlIHVzaW5nIHNlcGFyYXRvclxuICAgICAgICBlcHNpbG9uKGxvb3AsIHNlcC5sZWZ0KTtcbiAgICAgICAgZXBzaWxvbihzZXAucmlnaHQsIHN0YXJ0KTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIGVwc2lsb24obG9vcCwgZW50cnkpOyAvLyBsb29wIGJhY2sgdG8gZW50cnkvZXhpdCBkZWNpc2lvblxuICAgIH1cbiAgICBhdG4uZGVjaXNpb25NYXBbYnVpbGRBVE5LZXkocnVsZSwgc2VwID8gJ1JlcGV0aXRpb25XaXRoU2VwYXJhdG9yJyA6ICdSZXBldGl0aW9uJywgc3Rhci5pZHgpXSA9IGVudHJ5O1xuICAgIHJldHVybiB7XG4gICAgICAgIGxlZnQ6IGVudHJ5LFxuICAgICAgICByaWdodDogbG9vcEVuZFxuICAgIH07XG59XG5mdW5jdGlvbiBvcHRpb25hbChhdG4sIHJ1bGUsIG9wdGlvbmFsLCBoYW5kbGUpIHtcbiAgICBjb25zdCBzdGFydCA9IGhhbmRsZS5sZWZ0O1xuICAgIGNvbnN0IGVuZCA9IGhhbmRsZS5yaWdodDtcbiAgICBlcHNpbG9uKHN0YXJ0LCBlbmQpO1xuICAgIGF0bi5kZWNpc2lvbk1hcFtidWlsZEFUTktleShydWxlLCAnT3B0aW9uJywgb3B0aW9uYWwuaWR4KV0gPSBzdGFydDtcbiAgICByZXR1cm4gaGFuZGxlO1xufVxuZnVuY3Rpb24gZGVmaW5lRGVjaXNpb25TdGF0ZShhdG4sIHN0YXRlKSB7XG4gICAgYXRuLmRlY2lzaW9uU3RhdGVzLnB1c2goc3RhdGUpO1xuICAgIHN0YXRlLmRlY2lzaW9uID0gYXRuLmRlY2lzaW9uU3RhdGVzLmxlbmd0aCAtIDE7XG4gICAgcmV0dXJuIHN0YXRlLmRlY2lzaW9uO1xufVxuZnVuY3Rpb24gbWFrZUFsdHMoYXRuLCBydWxlLCBzdGFydCwgcHJvZHVjdGlvbiwgLi4uYWx0cykge1xuICAgIGNvbnN0IGVuZCA9IG5ld1N0YXRlKGF0biwgcnVsZSwgcHJvZHVjdGlvbiwge1xuICAgICAgICB0eXBlOiBBVE5fQkxPQ0tfRU5ELFxuICAgICAgICBzdGFydFxuICAgIH0pO1xuICAgIHN0YXJ0LmVuZCA9IGVuZDtcbiAgICBmb3IgKGNvbnN0IGFsdCBvZiBhbHRzKSB7XG4gICAgICAgIGlmIChhbHQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgLy8gaG9vayBhbHRzIHVwIHRvIGRlY2lzaW9uIGJsb2NrXG4gICAgICAgICAgICBlcHNpbG9uKHN0YXJ0LCBhbHQubGVmdCk7XG4gICAgICAgICAgICBlcHNpbG9uKGFsdC5yaWdodCwgZW5kKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGVwc2lsb24oc3RhcnQsIGVuZCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgaGFuZGxlID0ge1xuICAgICAgICBsZWZ0OiBzdGFydCxcbiAgICAgICAgcmlnaHQ6IGVuZFxuICAgIH07XG4gICAgYXRuLmRlY2lzaW9uTWFwW2J1aWxkQVROS2V5KHJ1bGUsIGdldFByb2RUeXBlKHByb2R1Y3Rpb24pLCBwcm9kdWN0aW9uLmlkeCldID0gc3RhcnQ7XG4gICAgcmV0dXJuIGhhbmRsZTtcbn1cbmZ1bmN0aW9uIGdldFByb2RUeXBlKHByb2R1Y3Rpb24pIHtcbiAgICBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIEFsdGVybmF0aW9uKSB7XG4gICAgICAgIHJldHVybiAnQWx0ZXJuYXRpb24nO1xuICAgIH1cbiAgICBlbHNlIGlmIChwcm9kdWN0aW9uIGluc3RhbmNlb2YgT3B0aW9uKSB7XG4gICAgICAgIHJldHVybiAnT3B0aW9uJztcbiAgICB9XG4gICAgZWxzZSBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIFJlcGV0aXRpb24pIHtcbiAgICAgICAgcmV0dXJuICdSZXBldGl0aW9uJztcbiAgICB9XG4gICAgZWxzZSBpZiAocHJvZHVjdGlvbiBpbnN0YW5jZW9mIFJlcGV0aXRpb25XaXRoU2VwYXJhdG9yKSB7XG4gICAgICAgIHJldHVybiAnUmVwZXRpdGlvbldpdGhTZXBhcmF0b3InO1xuICAgIH1cbiAgICBlbHNlIGlmIChwcm9kdWN0aW9uIGluc3RhbmNlb2YgUmVwZXRpdGlvbk1hbmRhdG9yeSkge1xuICAgICAgICByZXR1cm4gJ1JlcGV0aXRpb25NYW5kYXRvcnknO1xuICAgIH1cbiAgICBlbHNlIGlmIChwcm9kdWN0aW9uIGluc3RhbmNlb2YgUmVwZXRpdGlvbk1hbmRhdG9yeVdpdGhTZXBhcmF0b3IpIHtcbiAgICAgICAgcmV0dXJuICdSZXBldGl0aW9uTWFuZGF0b3J5V2l0aFNlcGFyYXRvcic7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgcHJvZHVjdGlvbiB0eXBlIGVuY291bnRlcmVkJyk7XG4gICAgfVxufVxuZnVuY3Rpb24gbWFrZUJsb2NrKGF0biwgYWx0cykge1xuICAgIGNvbnN0IGFsdHNMZW5ndGggPSBhbHRzLmxlbmd0aDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGFsdHNMZW5ndGggLSAxOyBpKyspIHtcbiAgICAgICAgY29uc3QgaGFuZGxlID0gYWx0c1tpXTtcbiAgICAgICAgbGV0IHRyYW5zaXRpb247XG4gICAgICAgIGlmIChoYW5kbGUubGVmdC50cmFuc2l0aW9ucy5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgIHRyYW5zaXRpb24gPSBoYW5kbGUubGVmdC50cmFuc2l0aW9uc1swXTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBpc1J1bGVUcmFuc2l0aW9uID0gdHJhbnNpdGlvbiBpbnN0YW5jZW9mIFJ1bGVUcmFuc2l0aW9uO1xuICAgICAgICBjb25zdCBydWxlVHJhbnNpdGlvbiA9IHRyYW5zaXRpb247XG4gICAgICAgIGNvbnN0IG5leHQgPSBhbHRzW2kgKyAxXS5sZWZ0O1xuICAgICAgICBpZiAoaGFuZGxlLmxlZnQudHlwZSA9PT0gQVROX0JBU0lDICYmXG4gICAgICAgICAgICBoYW5kbGUucmlnaHQudHlwZSA9PT0gQVROX0JBU0lDICYmXG4gICAgICAgICAgICB0cmFuc2l0aW9uICE9PSB1bmRlZmluZWQgJiZcbiAgICAgICAgICAgICgoaXNSdWxlVHJhbnNpdGlvbiAmJiBydWxlVHJhbnNpdGlvbi5mb2xsb3dTdGF0ZSA9PT0gaGFuZGxlLnJpZ2h0KSB8fFxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb24udGFyZ2V0ID09PSBoYW5kbGUucmlnaHQpKSB7XG4gICAgICAgICAgICAvLyB3ZSBjYW4gYXZvaWQgZXBzaWxvbiBlZGdlIHRvIG5leHQgZWxlbWVudFxuICAgICAgICAgICAgaWYgKGlzUnVsZVRyYW5zaXRpb24pIHtcbiAgICAgICAgICAgICAgICBydWxlVHJhbnNpdGlvbi5mb2xsb3dTdGF0ZSA9IG5leHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uLnRhcmdldCA9IG5leHQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZW1vdmVTdGF0ZShhdG4sIGhhbmRsZS5yaWdodCk7IC8vIHdlIHNraXBwZWQgb3ZlciB0aGlzIHN0YXRlXG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBuZWVkIGVwc2lsb24gaWYgcHJldmlvdXMgYmxvY2sncyByaWdodCBlbmQgbm9kZSBpcyBjb21wbGV4XG4gICAgICAgICAgICBlcHNpbG9uKGhhbmRsZS5yaWdodCwgbmV4dCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgZmlyc3QgPSBhbHRzWzBdO1xuICAgIGNvbnN0IGxhc3QgPSBhbHRzW2FsdHNMZW5ndGggLSAxXTtcbiAgICByZXR1cm4ge1xuICAgICAgICBsZWZ0OiBmaXJzdC5sZWZ0LFxuICAgICAgICByaWdodDogbGFzdC5yaWdodFxuICAgIH07XG59XG5mdW5jdGlvbiB0b2tlblJlZihhdG4sIHJ1bGUsIHRva2VuVHlwZSwgcHJvZHVjdGlvbikge1xuICAgIGNvbnN0IGxlZnQgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIHByb2R1Y3Rpb24sIHtcbiAgICAgICAgdHlwZTogQVROX0JBU0lDXG4gICAgfSk7XG4gICAgY29uc3QgcmlnaHQgPSBuZXdTdGF0ZShhdG4sIHJ1bGUsIHByb2R1Y3Rpb24sIHtcbiAgICAgICAgdHlwZTogQVROX0JBU0lDXG4gICAgfSk7XG4gICAgYWRkVHJhbnNpdGlvbihsZWZ0LCBuZXcgQXRvbVRyYW5zaXRpb24ocmlnaHQsIHRva2VuVHlwZSkpO1xuICAgIHJldHVybiB7XG4gICAgICAgIGxlZnQsXG4gICAgICAgIHJpZ2h0XG4gICAgfTtcbn1cbmZ1bmN0aW9uIHJ1bGVSZWYoYXRuLCBjdXJyZW50UnVsZSwgbm9uVGVybWluYWwpIHtcbiAgICBjb25zdCBydWxlID0gbm9uVGVybWluYWwucmVmZXJlbmNlZFJ1bGU7XG4gICAgY29uc3Qgc3RhcnQgPSBhdG4ucnVsZVRvU3RhcnRTdGF0ZS5nZXQocnVsZSk7XG4gICAgY29uc3QgbGVmdCA9IG5ld1N0YXRlKGF0biwgY3VycmVudFJ1bGUsIG5vblRlcm1pbmFsLCB7XG4gICAgICAgIHR5cGU6IEFUTl9CQVNJQ1xuICAgIH0pO1xuICAgIGNvbnN0IHJpZ2h0ID0gbmV3U3RhdGUoYXRuLCBjdXJyZW50UnVsZSwgbm9uVGVybWluYWwsIHtcbiAgICAgICAgdHlwZTogQVROX0JBU0lDXG4gICAgfSk7XG4gICAgY29uc3QgY2FsbCA9IG5ldyBSdWxlVHJhbnNpdGlvbihzdGFydCwgcnVsZSwgcmlnaHQpO1xuICAgIGFkZFRyYW5zaXRpb24obGVmdCwgY2FsbCk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbGVmdCxcbiAgICAgICAgcmlnaHRcbiAgICB9O1xufVxuZnVuY3Rpb24gYnVpbGRSdWxlSGFuZGxlKGF0biwgcnVsZSwgYmxvY2spIHtcbiAgICBjb25zdCBzdGFydCA9IGF0bi5ydWxlVG9TdGFydFN0YXRlLmdldChydWxlKTtcbiAgICBlcHNpbG9uKHN0YXJ0LCBibG9jay5sZWZ0KTtcbiAgICBjb25zdCBzdG9wID0gYXRuLnJ1bGVUb1N0b3BTdGF0ZS5nZXQocnVsZSk7XG4gICAgZXBzaWxvbihibG9jay5yaWdodCwgc3RvcCk7XG4gICAgY29uc3QgaGFuZGxlID0ge1xuICAgICAgICBsZWZ0OiBzdGFydCxcbiAgICAgICAgcmlnaHQ6IHN0b3BcbiAgICB9O1xuICAgIHJldHVybiBoYW5kbGU7XG59XG5mdW5jdGlvbiBlcHNpbG9uKGEsIGIpIHtcbiAgICBjb25zdCB0cmFuc2l0aW9uID0gbmV3IEVwc2lsb25UcmFuc2l0aW9uKGIpO1xuICAgIGFkZFRyYW5zaXRpb24oYSwgdHJhbnNpdGlvbik7XG59XG5mdW5jdGlvbiBuZXdTdGF0ZShhdG4sIHJ1bGUsIHByb2R1Y3Rpb24sIHBhcnRpYWwpIHtcbiAgICBjb25zdCB0ID0gT2JqZWN0LmFzc2lnbih7IGF0bixcbiAgICAgICAgcHJvZHVjdGlvbiwgZXBzaWxvbk9ubHlUcmFuc2l0aW9uczogZmFsc2UsIHJ1bGUsIHRyYW5zaXRpb25zOiBbXSwgbmV4dFRva2VuV2l0aGluUnVsZTogW10sIHN0YXRlTnVtYmVyOiBhdG4uc3RhdGVzLmxlbmd0aCB9LCBwYXJ0aWFsKTtcbiAgICBhdG4uc3RhdGVzLnB1c2godCk7XG4gICAgcmV0dXJuIHQ7XG59XG5mdW5jdGlvbiBhZGRUcmFuc2l0aW9uKHN0YXRlLCB0cmFuc2l0aW9uKSB7XG4gICAgLy8gQSBzaW5nbGUgQVROIHN0YXRlIGNhbiBvbmx5IGNvbnRhaW4gZXBzaWxvbiB0cmFuc2l0aW9ucyBvciBub24tZXBzaWxvbiB0cmFuc2l0aW9uc1xuICAgIC8vIEJlY2F1c2UgdGhleSBhcmUgbmV2ZXIgbWl4ZWQsIG9ubHkgc2V0dGluZyB0aGUgcHJvcGVydHkgZm9yIHRoZSBmaXJzdCB0cmFuc2l0aW9uIGlzIGZpbmVcbiAgICBpZiAoc3RhdGUudHJhbnNpdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHN0YXRlLmVwc2lsb25Pbmx5VHJhbnNpdGlvbnMgPSB0cmFuc2l0aW9uLmlzRXBzaWxvbigpO1xuICAgIH1cbiAgICBzdGF0ZS50cmFuc2l0aW9ucy5wdXNoKHRyYW5zaXRpb24pO1xufVxuZnVuY3Rpb24gcmVtb3ZlU3RhdGUoYXRuLCBzdGF0ZSkge1xuICAgIGF0bi5zdGF0ZXMuc3BsaWNlKGF0bi5zdGF0ZXMuaW5kZXhPZihzdGF0ZSksIDEpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXRuLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/atn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js":
/*!****************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/dfa.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ATNConfigSet: () => (/* binding */ ATNConfigSet),\n/* harmony export */   DFA_ERROR: () => (/* binding */ DFA_ERROR),\n/* harmony export */   getATNConfigKey: () => (/* binding */ getATNConfigKey)\n/* harmony export */ });\n/* harmony import */ var lodash_es_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash-es/map.js */ \"(ssr)/./node_modules/lodash-es/map.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\nconst DFA_ERROR = {};\nclass ATNConfigSet {\n    constructor() {\n        this.map = {};\n        this.configs = [];\n    }\n    get size() {\n        return this.configs.length;\n    }\n    finalize() {\n        // Empties the map to free up memory\n        this.map = {};\n    }\n    add(config) {\n        const key = getATNConfigKey(config);\n        // Only add configs which don't exist in our map already\n        // While this does not influence the actual algorithm, adding them anyway would massively increase memory consumption\n        if (!(key in this.map)) {\n            this.map[key] = this.configs.length;\n            this.configs.push(config);\n        }\n    }\n    get elements() {\n        return this.configs;\n    }\n    get alts() {\n        return (0,lodash_es_map_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(this.configs, (e) => e.alt);\n    }\n    get key() {\n        let value = \"\";\n        for (const k in this.map) {\n            value += k + \":\";\n        }\n        return value;\n    }\n}\nfunction getATNConfigKey(config, alt = true) {\n    return `${alt ? `a${config.alt}` : \"\"}s${config.state.stateNumber}:${config.stack.map((e) => e.stateNumber.toString()).join(\"_\")}`;\n}\n//# sourceMappingURL=dfa.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/dfa.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/chevrotain-allstar/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/chevrotain-allstar/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLStarLookaheadStrategy: () => (/* reexport safe */ _all_star_lookahead_js__WEBPACK_IMPORTED_MODULE_0__.LLStarLookaheadStrategy)\n/* harmony export */ });\n/* harmony import */ var _all_star_lookahead_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./all-star-lookahead.js */ \"(ssr)/./node_modules/chevrotain-allstar/lib/all-star-lookahead.js\");\n/******************************************************************************\n * Copyright 2022 TypeFox GmbH\n * This program and the accompanying materials are made available under the\n * terms of the MIT License, which is available in the project root.\n ******************************************************************************/\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY2hldnJvdGFpbi1hbGxzdGFyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDa0U7QUFDbEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcY2hldnJvdGFpbi1hbGxzdGFyXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKipcbiAqIENvcHlyaWdodCAyMDIyIFR5cGVGb3ggR21iSFxuICogVGhpcyBwcm9ncmFtIGFuZCB0aGUgYWNjb21wYW55aW5nIG1hdGVyaWFscyBhcmUgbWFkZSBhdmFpbGFibGUgdW5kZXIgdGhlXG4gKiB0ZXJtcyBvZiB0aGUgTUlUIExpY2Vuc2UsIHdoaWNoIGlzIGF2YWlsYWJsZSBpbiB0aGUgcHJvamVjdCByb290LlxuICoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKi9cbmV4cG9ydCB7IExMU3Rhckxvb2thaGVhZFN0cmF0ZWd5IH0gZnJvbSAnLi9hbGwtc3Rhci1sb29rYWhlYWQuanMnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chevrotain-allstar/lib/index.js\n");

/***/ })

};
;