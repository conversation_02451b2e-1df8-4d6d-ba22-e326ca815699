# Generated by Django 4.2.20 on 2025-05-06 12:04

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0003_remove_flowchart_image_flowchart_mermaid_code"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="documentembedding",
            options={"ordering": ["chunk_number"]},
        ),
        migrations.AddField(
            model_name="documentembedding",
            name="chunk_number",
            field=models.IntegerField(
                default=8, help_text="Order of this chunk in the document"
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="documentembedding",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="documentembedding",
            name="text_chunk",
            field=models.TextField(
                default="blah", help_text="The text segment this embedding represents"
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="documentembedding",
            name="document",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="embeddings",
                to="documents.document",
            ),
        ),
        migrations.AlterField(
            model_name="documentembedding",
            name="embedding",
            field=models.JSONField(help_text="Vector embedding of the text chunk"),
        ),
        migrations.AddIndex(
            model_name="documentembedding",
            index=models.Index(
                fields=["document", "chunk_number"],
                name="documents_d_documen_895782_idx",
            ),
        ),
    ]
