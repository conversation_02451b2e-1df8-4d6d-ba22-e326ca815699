from rest_framework import serializers
from .models import ChatSession, ChatMessage

class ChatMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatMessage
        fields = ['id', 'role', 'content', 'model', 'created_at', 'tokens']
        read_only_fields = ['id', 'created_at', 'tokens']

class ChatSessionSerializer(serializers.ModelSerializer):
    messages = ChatMessageSerializer(many=True, read_only=True)
    message_count = serializers.SerializerMethodField()

    class Meta:
        model = ChatSession
        fields = ['id', 'title', 'created_at', 'updated_at', 'is_active', 'messages', 'message_count']
        read_only_fields = ['id', 'created_at', 'updated_at', 'message_count']

    def get_message_count(self, obj):
        return obj.messages.count()

class ChatRequestSerializer(serializers.Serializer):
    message = serializers.CharField(required=True)
    model = serializers.ChoiceField(choices=['openai', 'gemini'], required=True)
    session_id = serializers.IntegerField(required=False)
    context = serializers.CharField(required=False, allow_blank=True)

class ChatResponseSerializer(serializers.Serializer):
    session_id = serializers.IntegerField()
    message = serializers.CharField()
    model = serializers.CharField()
    tokens = serializers.IntegerField()
    usage_stats = serializers.JSONField()