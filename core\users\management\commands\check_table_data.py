from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Check which tables have data and their structure'

    def handle(self, *args, **options):
        print("Checking table data and structure...")
        print("=" * 60)

        with connection.cursor() as cursor:
            # Get all tables that reference documents_document
            problematic_tables = [
                'documents_blueprinttopics',
                'documents_documentembedding',
                'documents_documenttimer',
                'documents_flashcard',
                'documents_flowchart',
                'documents_quiz',
                'documents_summaryinteraction',
                'documents_summarytracking',
                'documents_timersession',
                'users_studentperformance'
            ]

            for table in problematic_tables:
                try:
                    # Check if table exists
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}';")
                    exists = cursor.fetchone()

                    if exists:
                        # Get row count
                        cursor.execute(f"SELECT COUNT(*) FROM {table};")
                        count = cursor.fetchone()[0]
                        print(f"{table}: {count} rows")
                    else:
                        print(f"{table}: Table does not exist")

                except Exception as e:
                    print(f"{table}: Error - {e}")

        print("Table data check completed!")
