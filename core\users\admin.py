from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    Student, UserUsage, StudentPerformance, UserProfile, DocumentTimeTracking
)
from chat.models import ChatSession, ChatMessage

# Register your models here.
admin.site.register(Student)
admin.site.register(UserUsage)
admin.site.register(UserProfile)
admin.site.register(StudentPerformance)
admin.site.register(ChatSession)
admin.site.register(ChatMessage)

# Simple document time tracking
@admin.register(DocumentTimeTracking)
class DocumentTimeTrackingAdmin(admin.ModelAdmin):
    list_display = ['username', 'file_name', 'total_time_formatted', 'number_of_sessions', 'number_of_quizzes', 'last_accessed']
    list_filter = ['last_accessed', 'number_of_sessions', 'number_of_quizzes']
    search_fields = ['username', 'file_name']
    readonly_fields = ['created_at', 'total_time_formatted', 'average_session_time']
    ordering = ['-last_accessed']


