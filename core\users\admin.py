from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    Student, UserUsage, StudentPerformance, UserProfile, DocumentTimeTracking, CMSScore
)
from chat.models import ChatSession, ChatMessage

# Register your models here.
admin.site.register(Student)
admin.site.register(UserUsage)
admin.site.register(UserProfile)
admin.site.register(StudentPerformance)
admin.site.register(ChatSession)
admin.site.register(ChatMessage)

# Simple document time tracking
@admin.register(DocumentTimeTracking)
class DocumentTimeTrackingAdmin(admin.ModelAdmin):
    list_display = ['username', 'file_name', 'total_time_formatted', 'number_of_sessions', 'number_of_quizzes', 'last_accessed']
    list_filter = ['last_accessed', 'number_of_sessions', 'number_of_quizzes']
    search_fields = ['username', 'file_name']
    readonly_fields = ['created_at', 'total_time_formatted', 'average_session_time']
    ordering = ['-last_accessed']


# CMS Score admin for backend monitoring
@admin.register(CMSScore)
class CMSScoreAdmin(admin.ModelAdmin):
    list_display = [
        'student_username', 'document_title', 'cms_score', 'user_zone',
        'quiz_score_percentage', 'time_category', 'created_at'
    ]
    list_filter = [
        'user_zone', 'quiz_performance_category', 'time_category', 'created_at'
    ]
    search_fields = ['student__username', 'document__title']
    readonly_fields = [
        'student', 'document', 'quiz_attempt', 'quiz_score_percentage',
        'quiz_score_component', 'quiz_performance_category', 'time_spent_seconds',
        'average_time_seconds', 'time_ratio', 'time_category', 'time_component_points',
        'cms_score', 'user_zone', 'created_at', 'updated_at'
    ]
    ordering = ['-created_at']

    def student_username(self, obj):
        return obj.student.username
    student_username.short_description = 'Student'

    def document_title(self, obj):
        return obj.document.title
    document_title.short_description = 'Document'

    # Make the admin read-only since CMS scores are auto-calculated
    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


