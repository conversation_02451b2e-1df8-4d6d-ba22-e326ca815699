from django.test import TestCase, override_settings
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth import get_user_model
from ..models import UserProfile, UserUsage
from django.utils import timezone
from django.core.files.storage import default_storage
import os
from ..utils import get_usage_stats, reset_user_usage

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)
class FileUploadAndUsageTests(TestCase):
    def setUp(self):
        # Create media directory if it doesn't exist
        media_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'media')
        os.makedirs(media_dir, exist_ok=True)
        
        self.client = APIClient()
        self.User = get_user_model()
        
        # Create test user
        self.user = self.User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
            is_email_verified=True
        )
        
        # Update existing user profile
        self.user.userprofile.is_paid = False
        self.user.userprofile.save()
        
        # Reset usage stats to ensure clean state
        reset_user_usage(self.user)
        
        # Create initial usage record
        self.user_usage = UserUsage.objects.get(
            user=self.user,
            date=timezone.now().date()
        )
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
        
        # URLs
        self.upload_url = reverse('upload-file')
        self.usage_url = reverse('get-usage')
        
        # Test file data
        self.test_file_content = b"test file content"
        self.test_filename = "test_file.txt"

    def tearDown(self):
        # Create media directory if it doesn't exist
        media_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'media')
        os.makedirs(media_dir, exist_ok=True)
        
        # Clean up any files created during testing
        for file in default_storage.listdir("")[1]:  # [1] gets files, [0] gets directories
            if file.startswith(f"{self.user.id}_"):
                default_storage.delete(file)

    def test_get_usage_authenticated(self):
        """Test getting usage stats for authenticated user"""
        response = self.client.get(self.usage_url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('chat_count', response.data)
        self.assertIn('file_upload_count', response.data)
        self.assertIn('chat_limit', response.data)
        self.assertIn('file_upload_limit', response.data)
        self.assertIn('remaining_chats', response.data)
        self.assertIn('remaining_file_uploads', response.data)
        self.assertIn('is_paid', response.data)

    def test_get_usage_unauthenticated(self):
        """Test getting usage stats without authentication"""
        self.client.force_authenticate(user=None)
        response = self.client.get(self.usage_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_successful_file_upload(self):
        """Test successful file upload"""
        test_file = SimpleUploadedFile(
            self.test_filename,
            self.test_file_content,
            content_type="text/plain"
        )
        
        response = self.client.post(
            self.upload_url,
            {'file': test_file},
            format='multipart'
        )
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('file_path', response.data)
        self.assertIn('usage_stats', response.data)
        
        # Verify usage stats were updated
        usage = UserUsage.objects.get(user=self.user, date=timezone.now().date())
        self.assertEqual(usage.file_upload_count, 1)

    def test_file_upload_no_file(self):
        """Test file upload without providing a file"""
        response = self.client.post(self.upload_url, {}, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_file_upload_limit_exceeded(self):
        """Test file upload when limit is exceeded"""
        # Set file upload count to limit
        self.user_usage.file_upload_count = self.user_usage.file_upload_limit
        self.user_usage.save()
        
        test_file = SimpleUploadedFile(
            self.test_filename,
            self.test_file_content,
            content_type="text/plain"
        )
        
        response = self.client.post(
            self.upload_url,
            {'file': test_file},
            format='multipart'
        )
        
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)

    def test_file_upload_updates_usage_stats(self):
        """Test that file upload properly updates usage stats"""
        test_file = SimpleUploadedFile(
            self.test_filename,
            self.test_file_content,
            content_type="text/plain"
        )
        
        # Get initial usage stats
        initial_response = self.client.get(self.usage_url)
        initial_upload_count = initial_response.data['file_upload_count']
        
        # Upload file
        self.client.post(
            self.upload_url,
            {'file': test_file},
            format='multipart'
        )
        
        # Get updated usage stats
        final_response = self.client.get(self.usage_url)
        final_upload_count = final_response.data['file_upload_count']
        
        self.assertEqual(final_upload_count, initial_upload_count + 1)

    def test_paid_user_limits(self):
        """Test file upload limits for paid users"""
        # Update user to paid status
        self.user.userprofile.is_paid = True
        self.user.userprofile.save()
        
        test_file = SimpleUploadedFile(
            self.test_filename,
            self.test_file_content,
            content_type="text/plain"
        )
        
        # Verify increased limits for paid user
        response = self.client.get(self.usage_url)
        self.assertEqual(response.data['file_upload_limit'], 5)  # Paid user limit
        
        # Test multiple uploads
        for _ in range(3):  # Try 3 uploads
            response = self.client.post(
                self.upload_url,
                {'file': SimpleUploadedFile(
                    self.test_filename,
                    self.test_file_content,
                    content_type="text/plain"
                )},
                format='multipart'
            )
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_usage_stats_reset(self):
        """Test that usage stats reset properly for new day"""
        # Set counts for yesterday
        yesterday = timezone.now().date() - timezone.timedelta(days=1)
        UserUsage.objects.create(
            user=self.user,
            date=yesterday,
            chat_count=5,
            file_upload_count=1
        )
        
        # Reset stats
        reset_user_usage(self.user)
        
        # Get today's stats
        response = self.client.get(self.usage_url)
        self.assertEqual(response.data['file_upload_count'], 0)
        self.assertEqual(response.data['chat_count'], 0)