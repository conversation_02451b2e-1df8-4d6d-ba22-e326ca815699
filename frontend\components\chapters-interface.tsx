"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Copy, CheckCircle, BookOpen, Sparkles, ChevronRight } from "lucide-react"
import { toast } from "sonner"
import { documentApi, chapterApi } from "@/lib/api"

interface Chapter {
  id: string
  title: string
  content: string
  subsections?: {
    title: string
    content: string
  }[]
}

interface ChaptersInterfaceProps {
  documentId?: number
}

export function ChaptersInterface({ documentId }: ChaptersInterfaceProps) {
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copiedChapter, setCopiedChapter] = useState<string | null>(null)

  useEffect(() => {
    if (documentId) {
      loadChapters()
    }
  }, [documentId])

  const loadChapters = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Use real API to generate chapters based on document content
      const response = await chapterApi.getOrGenerateChapters(documentId)

      if (response.chapters && response.chapters.length > 0) {
        // Transform API response to match our interface
        const transformedChapters: Chapter[] = response.chapters.map((chapter: any, index: number) => ({
          id: chapter.id?.toString() || (index + 1).toString(),
          title: chapter.title || `Chapter ${index + 1}`,
          content: chapter.content || '',
          subsections: chapter.subsections || []
        }))

        setChapters(transformedChapters)
        toast.success(`Generated ${transformedChapters.length} chapters successfully!`)
      } else {
        setError('No chapters were generated. Please try again.')
      }
    } catch (err) {
      console.error('Error loading chapters:', err)
      setError('Failed to load chapters. Please try again.')
      toast.error('Failed to generate chapters')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewChapters = async () => {
    if (!documentId) {
      toast.error("No document selected for chapter generation")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Use real API to generate new chapters
      const response = await chapterApi.generateChapters(documentId, 5, 'gemini')

      if (response.chapters && response.chapters.length > 0) {
        // Transform API response to match our interface
        const transformedChapters: Chapter[] = response.chapters.map((chapter: any, index: number) => ({
          id: chapter.id?.toString() || (index + 1).toString(),
          title: chapter.title || `Chapter ${index + 1}`,
          content: chapter.content || '',
          subsections: chapter.subsections || []
        }))

        setChapters(transformedChapters)
        toast.success(`Generated ${transformedChapters.length} new chapters successfully!`)
      } else {
        setError('No chapters were generated. Please try again.')
        toast.error("Failed to generate new chapters")
      }
    } catch (err) {
      console.error('Error generating chapters:', err)
      setError('Failed to generate new chapters. Please try again.')
      toast.error("Failed to generate new chapters")
    } finally {
      setIsLoading(false)
    }
  }

  const copyChapterToClipboard = async (chapter: Chapter) => {
    let content = `# ${chapter.title}\n\n${chapter.content}\n\n`
    
    if (chapter.subsections) {
      chapter.subsections.forEach(subsection => {
        content += `## ${subsection.title}\n${subsection.content}\n\n`
      })
    }

    try {
      await navigator.clipboard.writeText(content)
      setCopiedChapter(chapter.id)
      toast.success(`Chapter "${chapter.title}" copied to clipboard!`)
      
      setTimeout(() => setCopiedChapter(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy chapter")
    }
  }

  const copyAllChapters = async () => {
    if (chapters.length === 0) return

    let allContent = ""
    chapters.forEach((chapter, index) => {
      allContent += `# Chapter ${index + 1}: ${chapter.title}\n\n${chapter.content}\n\n`
      
      if (chapter.subsections) {
        chapter.subsections.forEach(subsection => {
          allContent += `## ${subsection.title}\n${subsection.content}\n\n`
        })
      }
      
      allContent += "---\n\n"
    })

    try {
      await navigator.clipboard.writeText(allContent)
      toast.success("All chapters copied to clipboard!")
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy all chapters")
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription>
              Select a document to view its chapter breakdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Please select a document to generate chapters.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription>
              {chapters.length > 0 ? "Generating new chapters..." : "Loading chapters..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadChapters} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Document Chapters
          </CardTitle>
          <CardDescription>
            AI-generated chapter breakdown of your document
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 mb-4">
            <Button
              onClick={copyAllChapters}
              variant="outline"
              className="flex-1"
              disabled={chapters.length === 0}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy All Chapters
            </Button>
            <Button
              onClick={generateNewChapters}
              className="flex-1"
              disabled={isLoading}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Generate New Chapters
            </Button>
          </div>

          <Accordion type="single" collapsible className="w-full">
            {chapters.map((chapter, index) => (
              <AccordionItem key={chapter.id} value={chapter.id}>
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">
                      Chapter {index + 1}
                    </span>
                    <ChevronRight className="h-4 w-4" />
                    <span>{chapter.title}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-2">
                    <p className="text-sm leading-relaxed">{chapter.content}</p>
                    
                    {chapter.subsections && chapter.subsections.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-medium text-sm">Subsections:</h4>
                        {chapter.subsections.map((subsection, subIndex) => (
                          <div key={subIndex} className="pl-4 border-l-2 border-muted">
                            <h5 className="font-medium text-sm mb-1">{subsection.title}</h5>
                            <p className="text-sm text-muted-foreground">{subsection.content}</p>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <Button
                      onClick={() => copyChapterToClipboard(chapter)}
                      variant="outline"
                      size="sm"
                      className="mt-3"
                    >
                      {copiedChapter === chapter.id ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Chapter
                        </>
                      )}
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  )
}
