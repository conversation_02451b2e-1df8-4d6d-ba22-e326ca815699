"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nasync function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ (undefined))\n\n  ;(0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(\n    /* c8 ignore next 7 -- hooks are client-only. */\n    function () {\n      const file = createFile(options)\n      processor.run(processor.parse(file), file, function (error, tree) {\n        setError(error)\n        setTree(tree)\n      })\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  /* c8 ignore next -- hooks are client-only. */\n  if (error) throw error\n\n  /* c8 ignore next -- hooks are client-only. */\n  return tree ? post(tree, options) : (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment)\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  // Wrap in `div` if there’s a class name.\n  if (options.className) {\n    tree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className: options.className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        tree.type === 'root' ? tree.children : [tree]\n      )\n    }\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;