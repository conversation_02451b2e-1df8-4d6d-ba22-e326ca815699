"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { FolderIcon, FolderOpenIcon } from "lucide-react"

interface DocumentGroupsProps {
  groups: Array<{
    id: number
    name: string
    description?: string
  }>
  selectedGroup: number | null
  onSelectGroup: (groupId: number | null) => void
}

export function DocumentGroups({ groups, selectedGroup, onSelectGroup }: DocumentGroupsProps) {
  return (
    <div className="space-y-2">
      <Button
        variant="ghost"
        className={cn(
          "w-full justify-start",
          selectedGroup === null && "bg-muted"
        )}
        onClick={() => onSelectGroup(null)}
      >
        <FolderIcon className="mr-2 h-4 w-4" />
        All Documents
      </Button>
      {groups.map((group) => (
        <Button
          key={group.id}
          variant="ghost"
          className={cn(
            "w-full justify-start",
            selectedGroup === group.id && "bg-muted"
          )}
          onClick={() => onSelectGroup(group.id)}
        >
          {selectedGroup === group.id ? (
            <FolderOpenIcon className="mr-2 h-4 w-4" />
          ) : (
            <FolderIcon className="mr-2 h-4 w-4" />
          )}
          {group.name}
        </Button>
      ))}
    </div>
  )
}
