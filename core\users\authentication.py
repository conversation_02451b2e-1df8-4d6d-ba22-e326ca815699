from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import AuthenticationFailed
import logging

# Configure logging
logger = logging.getLogger(__name__)

class BearerTokenAuthentication(TokenAuthentication):
    """
    Simple token based authentication using the keyword 'Bear<PERSON>' instead of 'Token'.
    
    Clients should authenticate by passing the token key in the "Authorization"
    HTTP header, prepended with the string "Bearer ".  For example:
    
        Authorization: Bearer 401f7ac837da42b97f613d789819ff93537bee6a
    """
    keyword = 'Bearer'

    def authenticate(self, request):
        """
        Attempt to authenticate the request using the token in the Authorization header.
        Logs detailed information about the authentication process for debugging.
        """
        try:
            # Call the parent class's authenticate method
            result = super().authenticate(request)
            
            if result:
                user, token = result
                logger.info(f"Bearer token authentication successful for user: {user.username}")
                return result
            return None
        except AuthenticationFailed as e:
            logger.error(f"Bearer token authentication failed: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error in Bearer token authentication: {str(e)}")
            raise
