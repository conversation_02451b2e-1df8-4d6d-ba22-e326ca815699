/**
 * Utility functions for time tracking and formatting
 */

/**
 * Format seconds into a human-readable time string
 * @param seconds - Number of seconds
 * @returns Formatted time string (e.g., "1:23:45" or "23:45")
 */
export function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

/**
 * Format milliseconds into a human-readable time string
 * @param milliseconds - Number of milliseconds
 * @returns Formatted time string
 */
export function formatTimeFromMs(milliseconds: number): string {
  return formatTime(Math.floor(milliseconds / 1000));
}

/**
 * Parse a formatted time string back to seconds
 * @param timeString - Formatted time string (e.g., "1:23:45" or "23:45")
 * @returns Number of seconds
 */
export function parseTimeToSeconds(timeString: string): number {
  const parts = timeString.split(':').map(Number);
  
  if (parts.length === 3) {
    // Hours:Minutes:Seconds
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  } else if (parts.length === 2) {
    // Minutes:Seconds
    return parts[0] * 60 + parts[1];
  } else {
    return 0;
  }
}

/**
 * Get a human-readable duration description
 * @param seconds - Number of seconds
 * @returns Human-readable description (e.g., "2 hours 30 minutes")
 */
export function getHumanReadableDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  const parts: string[] = [];

  if (hours > 0) {
    parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
  }
  
  if (minutes > 0) {
    parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
  }
  
  if (remainingSeconds > 0 && hours === 0) {
    parts.push(`${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`);
  }

  if (parts.length === 0) {
    return '0 seconds';
  }

  if (parts.length === 1) {
    return parts[0];
  }

  if (parts.length === 2) {
    return `${parts[0]} and ${parts[1]}`;
  }

  return `${parts.slice(0, -1).join(', ')}, and ${parts[parts.length - 1]}`;
}

/**
 * Calculate average session duration
 * @param totalSeconds - Total time in seconds
 * @param sessionCount - Number of sessions
 * @returns Average duration in seconds
 */
export function calculateAverageSessionDuration(totalSeconds: number, sessionCount: number): number {
  if (sessionCount === 0) return 0;
  return Math.floor(totalSeconds / sessionCount);
}

/**
 * Get study efficiency rating based on session patterns
 * @param averageSessionDuration - Average session duration in seconds
 * @param totalSessions - Total number of sessions
 * @param reopenedCount - Number of times document was reopened
 * @returns Efficiency rating object
 */
export function getStudyEfficiencyRating(
  averageSessionDuration: number,
  totalSessions: number,
  reopenedCount: number
): {
  rating: 'excellent' | 'good' | 'fair' | 'needs-improvement';
  score: number;
  feedback: string;
} {
  let score = 0;
  
  // Session duration scoring (0-40 points)
  if (averageSessionDuration >= 1800) { // 30+ minutes
    score += 40;
  } else if (averageSessionDuration >= 900) { // 15-30 minutes
    score += 30;
  } else if (averageSessionDuration >= 300) { // 5-15 minutes
    score += 20;
  } else {
    score += 10;
  }
  
  // Session consistency scoring (0-30 points)
  if (totalSessions >= 5) {
    score += 30;
  } else if (totalSessions >= 3) {
    score += 20;
  } else if (totalSessions >= 2) {
    score += 15;
  } else {
    score += 10;
  }
  
  // Reopening pattern scoring (0-30 points)
  const reopenRate = reopenedCount / Math.max(totalSessions, 1);
  if (reopenRate <= 0.2) { // Low reopen rate is good
    score += 30;
  } else if (reopenRate <= 0.4) {
    score += 20;
  } else if (reopenRate <= 0.6) {
    score += 15;
  } else {
    score += 10;
  }
  
  let rating: 'excellent' | 'good' | 'fair' | 'needs-improvement';
  let feedback: string;
  
  if (score >= 85) {
    rating = 'excellent';
    feedback = 'Outstanding study habits! You maintain focused, consistent sessions.';
  } else if (score >= 70) {
    rating = 'good';
    feedback = 'Good study patterns. Consider longer focused sessions for better retention.';
  } else if (score >= 55) {
    rating = 'fair';
    feedback = 'Room for improvement. Try to maintain longer, more consistent study sessions.';
  } else {
    rating = 'needs-improvement';
    feedback = 'Consider developing more structured study habits with longer, focused sessions.';
  }
  
  return { rating, score, feedback };
}

/**
 * Check if a session is considered "long" based on duration
 * @param seconds - Session duration in seconds
 * @returns True if session is considered long (>= 30 minutes)
 */
export function isLongSession(seconds: number): boolean {
  return seconds >= 1800; // 30 minutes
}

/**
 * Check if a session is considered "short" based on duration
 * @param seconds - Session duration in seconds
 * @returns True if session is considered short (< 5 minutes)
 */
export function isShortSession(seconds: number): boolean {
  return seconds < 300; // 5 minutes
}

/**
 * Get time of day category
 * @param date - Date object
 * @returns Time category
 */
export function getTimeOfDayCategory(date: Date): 'morning' | 'afternoon' | 'evening' | 'night' {
  const hour = date.getHours();
  
  if (hour >= 6 && hour < 12) {
    return 'morning';
  } else if (hour >= 12 && hour < 17) {
    return 'afternoon';
  } else if (hour >= 17 && hour < 22) {
    return 'evening';
  } else {
    return 'night';
  }
}

/**
 * Calculate study streak (consecutive days with study activity)
 * @param sessionDates - Array of session start dates
 * @returns Number of consecutive days with study activity
 */
export function calculateStudyStreak(sessionDates: Date[]): number {
  if (sessionDates.length === 0) return 0;
  
  // Sort dates in descending order
  const sortedDates = sessionDates
    .map(date => new Date(date.getFullYear(), date.getMonth(), date.getDate()))
    .sort((a, b) => b.getTime() - a.getTime());
  
  // Remove duplicates (same day)
  const uniqueDates = sortedDates.filter((date, index) => 
    index === 0 || date.getTime() !== sortedDates[index - 1].getTime()
  );
  
  if (uniqueDates.length === 0) return 0;
  
  let streak = 1;
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Check if the most recent session was today or yesterday
  const mostRecent = uniqueDates[0];
  const daysDiff = Math.floor((today.getTime() - mostRecent.getTime()) / (1000 * 60 * 60 * 24));
  
  if (daysDiff > 1) {
    return 0; // Streak is broken
  }
  
  // Count consecutive days
  for (let i = 1; i < uniqueDates.length; i++) {
    const currentDate = uniqueDates[i];
    const previousDate = uniqueDates[i - 1];
    const daysBetween = Math.floor((previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysBetween === 1) {
      streak++;
    } else {
      break;
    }
  }
  
  return streak;
}
