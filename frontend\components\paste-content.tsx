"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Youtube, Globe, FileText, Loader2 } from "lucide-react"
import { useTheme } from "@/components/theme-provider"

export function PasteContent() {
  const [youtubeUrl, setYoutubeUrl] = useState("")
  const [websiteUrl, setWebsiteUrl] = useState("")
  const [textContent, setTextContent] = useState("")
  const [activeTab, setActiveTab] = useState("youtube")
  const { theme } = useTheme()
  const [isProcessing, setIsProcessing] = useState(false)

  const handleProcess = () => {
    if (activeTab === "youtube" && youtubeUrl) {
      setIsProcessing(true)
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "youtube",
          url: youtubeUrl,
        }),
      )
      setTimeout(() => {
        window.location.href = "/process?type=paste"
      }, 2000)
    } else if (activeTab === "website" && websiteUrl) {
      setIsProcessing(true)
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "website",
          url: websiteUrl,
        }),
      )
      setTimeout(() => {
        window.location.href = "/process?type=paste"
      }, 2000)
    } else if (activeTab === "text" && textContent) {
      setIsProcessing(true)
      localStorage.setItem(
        "pastedContent",
        JSON.stringify({
          type: "text",
          content: textContent,
        }),
      )
      setTimeout(() => {
        window.location.href = "/process?type=paste"
      }, 2000)
    }
  }

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-semibold mb-4">Paste Content</h2>

      <Tabs defaultValue="youtube" className="flex-1" onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="youtube" className="data-[state=active]:bg-purple-600">
            <Youtube className="h-4 w-4 mr-2" />
            YouTube
          </TabsTrigger>
          <TabsTrigger value="website" className="data-[state=active]:bg-purple-600">
            <Globe className="h-4 w-4 mr-2" />
            Website
          </TabsTrigger>
          <TabsTrigger value="text" className="data-[state=active]:bg-purple-600">
            <FileText className="h-4 w-4 mr-2" />
            Text
          </TabsTrigger>
        </TabsList>

        <TabsContent value="youtube" className="space-y-4 flex-1">
          <div>
            <label className="text-sm text-neutral-400 mb-2 block">Paste YouTube URL</label>
            <Input
              placeholder="https://www.youtube.com/watch?v=..."
              value={youtubeUrl}
              onChange={(e) => setYoutubeUrl(e.target.value)}
              className={`focus-visible:ring-purple-500 ${
                theme === "light" ? "bg-white border-black" : "bg-neutral-800 border-neutral-700"
              }`}
            />
          </div>
          <p className="text-xs text-neutral-500">Enter a YouTube video URL to extract and analyze its content</p>

          <div className="mt-auto pt-4">
            <Button
              className="bg-purple-600 hover:bg-purple-700 w-full"
              disabled={!youtubeUrl || isProcessing}
              onClick={handleProcess}
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <div>Processing...</div>
                </div>
              ) : (
                <>Process YouTube Video</>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="website" className="space-y-4 flex-1">
          <div>
            <label className="text-sm text-neutral-400 mb-2 block">Paste Website URL</label>
            <Input
              placeholder="https://example.com"
              value={websiteUrl}
              onChange={(e) => setWebsiteUrl(e.target.value)}
              className={`focus-visible:ring-purple-500 ${
                theme === "light" ? "bg-white border-black" : "bg-neutral-800 border-neutral-700"
              }`}
            />
          </div>
          <p className="text-xs text-neutral-500">Enter a website URL to extract and analyze its content</p>

          <div className="mt-auto pt-4">
            <Button
              className="bg-purple-600 hover:bg-purple-700 w-full"
              disabled={!websiteUrl || isProcessing}
              onClick={handleProcess}
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <div>Processing...</div>
                </div>
              ) : (
                <>Process Website</>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="text" className="space-y-4 flex-1 flex flex-col">
          <div className="flex-1">
            <label className="text-sm text-neutral-400 mb-2 block">Paste Text Content</label>
            <Textarea
              placeholder="Paste or type your text here..."
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              className={`min-h-[200px] focus-visible:ring-purple-500 h-full ${
                theme === "light" ? "bg-white border-black" : "bg-neutral-800 border-neutral-700"
              }`}
            />
          </div>

          <div className="pt-4">
            <Button
              className="bg-purple-600 hover:bg-purple-700 w-full"
              disabled={!textContent || isProcessing}
              onClick={handleProcess}
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <div>Processing...</div>
                </div>
              ) : (
                <>Process Text</>
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
