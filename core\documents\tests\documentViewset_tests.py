from io import BytesIO
import os
import tempfile
from datetime import datetime
from unittest.mock import patch, MagicMock

from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase, override_settings
from django.urls import reverse
import fitz
from rest_framework import status
from rest_framework.test import APIClient

from documents.models import Document
from users.utils import increment_file_upload_count, get_usage_stats, UsageLimitExceeded
from documents.tasks import process_document_task
from docx import Document as DocxDocument


User = get_user_model()

# Use a temporary directory for media files during testing
TEMP_MEDIA_ROOT = tempfile.mkdtemp()

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)

@override_settings(MEDIA_ROOT=TEMP_MEDIA_ROOT)
class DocumentViewSetTest(TestCase):
    def setUp(self):
        
        self.client = APIClient()

        # Create a test student
        self.User = get_user_model()
        
        # Create test user
        self.user = self.User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
            is_email_verified=True
        )

        self.client.force_authenticate(user=self.user)

        
        # Create another user for authorization tests
        self.other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='otherpassword',
            is_active=True,
            is_email_verified=True
        )
        
        # Create test document
        self.document = Document.objects.create(
            user=self.user,
            title='Test Document',
            file=SimpleUploadedFile("dummy.txt", b"Test"),
            processing_status='completed'
        )
        
        # Create sample test files in memory
        self.test_files = {
            'pdf': self._create_pdf_file(),
            'docx': self._create_docx_file(),
            'txt': SimpleUploadedFile("sample.txt", b"This is a sample TXT file.", content_type='text/plain'),
        }

        
        # Set up the API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # URLs
        self.list_url = reverse('document-list')
        self.detail_url = reverse('document-detail', args=[self.document.id])
        self.status_url = reverse('document-processing-status', args=[self.document.id])
        self.upload_url = reverse('document-upload')
    

    def _create_pdf_file(self):
        buffer = BytesIO()
        doc = fitz.open()
        page = doc.new_page()
        page.insert_text((72, 72), "This is a sample PDF file.")
        doc.save(buffer)
        doc.close()
        buffer.seek(0)
        return SimpleUploadedFile("sample.pdf", buffer.read(), content_type="application/pdf")

    def _create_docx_file(self):
        buffer = BytesIO()
        doc = DocxDocument()
        doc.add_paragraph("This is a sample DOCX file.")
        doc.save(buffer)
        buffer.seek(0)
        return SimpleUploadedFile("sample.docx", buffer.read(), content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document")


    def tearDown(self):
        # Clean up any files created during tests
        for document in Document.objects.all():
            if document.file and os.path.isfile(document.file.path):
                os.remove(document.file.path)
    
    # Test basic CRUD operations
    def test_list_documents(self):
        """Test listing documents for authenticated user"""
        # Create a document for another user
        Document.objects.create(
            user=self.other_user,
            title="Other user's document",
            file=SimpleUploadedFile("otherfile.txt", b"Test"),
        )
        
        response = self.client.get(self.list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should only see own documents
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['title'], 'Test Document')
    
    def test_retrieve_document(self):
        """Test retrieving a specific document"""
        response = self.client.get(self.detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['title'], 'Test Document')
    
    def test_create_document(self):
        """Test creating a document via the regular create endpoint"""
        data = {
            'title': 'New Document',
            'file': self.test_files['pdf'],
        }
        
        response = self.client.post(self.list_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Document.objects.count(), 2)
        new_document = Document.objects.get(title='New Document')
        self.assertEqual(new_document.user, self.user)
    
    def test_update_document(self):
        """Test updating a document"""
        data = {'title': 'Updated Document Title'}
        
        response = self.client.patch(self.detail_url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.document.refresh_from_db()
        self.assertEqual(self.document.title, 'Updated Document Title')
    
    def test_delete_document(self):
        """Test deleting a document"""
        response = self.client.delete(self.detail_url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Document.objects.count(), 0)
    
    def test_unauthorized_access(self):
        """Test that unauthenticated users cannot access documents"""
        # Logout
        self.client.force_authenticate(user=None)
        
        # Try listing documents
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Try retrieving a specific document
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_wrong_user_access(self):
        """Test that users cannot access other users' documents"""
        # Login as different user
        self.client.force_authenticate(user=self.other_user)
        
        # Try retrieving another user's document
        response = self.client.get(self.detail_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    # Test custom actions
    def test_processing_status(self):
        """Test the processing_status custom action returns correct status and embedding count."""

        mock_embedding_1 = MagicMock()
        mock_embedding_2 = MagicMock()

        # Patch just the .all() and .count() methods on the RelatedManager
        with patch.object(type(self.document.embeddings), 'all', return_value=[mock_embedding_1, mock_embedding_2]), \
            patch.object(type(self.document.embeddings), 'count', return_value=2), \
            patch('documents.models.Document.objects.get', return_value=self.document):

            response = self.client.get(self.status_url)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['status'], 'completed')
            self.assertEqual(response.data['num_embeddings'], 2)



    @patch('users.utils.increment_file_upload_count')
    @patch('documents.tasks.process_document_task.delay')
    @patch('users.utils.get_usage_stats')
    def test_upload_file_success(self, mock_get_usage_stats, mock_task_delay, mock_increment):
        """Test uploading a document successfully triggers processing and returns correct response."""

        # Setup mock for usage stats
        mock_get_usage_stats.return_value = {'uploads': 1, 'limit': 10}

        # Create test file
        test_file = SimpleUploadedFile(
            name='test_upload.txt',
            content=b'This is a test file content',
            content_type='text/plain'
        )

        response = self.client.post(
            self.upload_url,
            {'file': test_file},
            format='multipart'
        )

        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('document_id', response.data)
        self.assertEqual(response.data['status'], 'processing')

        # Validate document was created correctly
        document_id = response.data['document_id']
        document = Document.objects.get(id=document_id)
        self.assertEqual(document.title, 'test_upload.txt')
        self.assertEqual(document.user, self.user)

        # Ensure usage tracking and async task were triggered
        mock_increment.assert_called_once_with(self.user)
        mock_task_delay.assert_called_once_with(document_id, self.client.auth)

    
    def test_upload_no_file(self):
        """Test upload with no file provided"""
        response = self.client.post(self.upload_url, {}, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'No file provided')
    
    @patch('users.utils.increment_file_upload_count')
    def test_upload_usage_limit_exceeded(self, mock_increment):
        """Test upload when usage limit is exceeded"""
        # Mock the increment function to raise an exception
        mock_increment.side_effect = UsageLimitExceeded('You have reached your upload limit')
        
        # Create a test file
        test_file = SimpleUploadedFile(
            name='test_upload.txt',
            content=b'Test content',
            content_type='text/plain'
        )
        
        response = self.client.post(
            self.upload_url, 
            {'file': test_file}, 
            format='multipart'
        )
        
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'You have reached your upload limit')
    
    @patch('django.core.files.storage.default_storage.save')
    def test_upload_storage_error(self, mock_save):
        """Test handling of errors during file storage"""
        # Mock the save function to raise an exception
        mock_save.side_effect = Exception('Storage error')
        
        # Create a test file
        test_file = SimpleUploadedFile(
            name='test_upload.txt',
            content=b'Test content',
            content_type='text/plain'
        )
        
        response = self.client.post(
            self.upload_url, 
            {'file': test_file}, 
            format='multipart'
        )
        
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn('error', response.data)
        self.assertIn('Storage error', response.data['error'])
        
        # Ensure no document was created despite the error
        self.assertEqual(Document.objects.count(), 1)  # Only the one from setUp