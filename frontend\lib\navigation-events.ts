// Navigation event system for time tracking cleanup
class NavigationEventManager {
  private listeners: Set<(destination?: string) => void> = new Set()

  // Add a listener for navigation events
  addListener(callback: (destination?: string) => void) {
    this.listeners.add(callback)
    return () => this.listeners.delete(callback)
  }

  // Trigger navigation event (call this before any navigation)
  triggerNavigation(destination?: string) {
    this.listeners.forEach(callback => {
      try {
        callback(destination)
      } catch (error) {
        console.error('Navigation event listener error:', error)
      }
    })
  }

  // Clear all listeners
  clear() {
    this.listeners.clear()
  }
}

export const navigationEvents = new NavigationEventManager()

// Helper function to wrap router navigation with event triggering
export const createNavigationWrapper = (router: any) => {
  const originalPush = router.push
  const originalReplace = router.replace
  const originalBack = router.back

  return {
    push: (...args: any[]) => {
      navigationEvents.triggerNavigation()
      return originalPush.apply(router, args)
    },
    replace: (...args: any[]) => {
      navigationEvents.triggerNavigation()
      return originalReplace.apply(router, args)
    },
    back: (...args: any[]) => {
      navigationEvents.triggerNavigation()
      return originalBack.apply(router, args)
    },
    // Restore original methods
    restore: () => {
      router.push = originalPush
      router.replace = originalReplace
      router.back = originalBack
    }
  }
}
