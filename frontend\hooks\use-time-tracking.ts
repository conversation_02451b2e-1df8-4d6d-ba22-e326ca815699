import { useState, useEffect, useRef, useCallback } from 'react';
import { timeTrackingApi } from '@/lib/api';

export interface TimeTrackingSession {
  id: number;
  document: number;
  status: 'active' | 'paused' | 'completed' | 'abandoned';
  session_start: string;
  session_end?: string;
  total_time_seconds: number;
  total_time_formatted: string;
  last_activity: string;
}

export interface TimeTrackingStats {
  total_study_time_seconds: number;
  total_study_time_formatted: string;
  total_sessions: number;
  total_quiz_pauses: number;
  view_count: number;
  reopened_at_least_once: boolean;
  first_access: string;
  last_access: string;
  average_session_duration_seconds: number;
  average_session_duration_formatted: string;
}

export interface UseTimeTrackingOptions {
  documentId: number;
  heartbeatInterval?: number; // in milliseconds, default 30000 (30 seconds)
  autoStart?: boolean; // automatically start tracking when hook is initialized
  onSessionStart?: (session: TimeTrackingSession) => void;
  onSessionEnd?: (session: TimeTrackingSession, stats: TimeTrackingStats) => void;
  onSessionPause?: (session: TimeTrackingSession) => void;
  onSessionResume?: (session: TimeTrackingSession) => void;
  onError?: (error: any) => void;
}

export const useTimeTracking = (options: UseTimeTrackingOptions) => {
  const {
    documentId,
    heartbeatInterval = 30000,
    autoStart = true,
    onSessionStart,
    onSessionEnd,
    onSessionPause,
    onSessionResume,
    onError,
  } = options;

  const [session, setSession] = useState<TimeTrackingSession | null>(null);
  const [stats, setStats] = useState<TimeTrackingStats | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionRef = useRef<TimeTrackingSession | null>(null);

  // Update session ref when session changes
  useEffect(() => {
    sessionRef.current = session;
  }, [session]);

  // Start heartbeat
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(async () => {
      const currentSession = sessionRef.current;
      if (currentSession && currentSession.status === 'active') {
        try {
          await timeTrackingApi.updateActivity(currentSession.id);
        } catch (error) {
          console.error('Heartbeat failed:', error);
          // Don't call onError for heartbeat failures as they're not critical
        }
      }
    }, heartbeatInterval);
  }, [heartbeatInterval]);

  // Stop heartbeat
  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  // Start tracking session
  const startTracking = useCallback(async () => {
    if (isTracking) return session;

    setIsLoading(true);
    setError(null);

    try {
      const response = await timeTrackingApi.startSession(documentId);
      const newSession = response.session;
      
      setSession(newSession);
      setIsTracking(true);
      setIsPaused(false);
      
      startHeartbeat();
      
      onSessionStart?.(newSession);
      
      return newSession;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to start tracking session';
      setError(errorMessage);
      onError?.(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [documentId, isTracking, session, startHeartbeat, onSessionStart, onError]);

  // Pause tracking (e.g., when starting quiz)
  const pauseTracking = useCallback(async (notes?: string) => {
    if (!session || session.status !== 'active') return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await timeTrackingApi.pauseSession(session.id, notes);
      const updatedSession = { ...session, status: 'paused' as const };
      
      setSession(updatedSession);
      setIsPaused(true);
      
      stopHeartbeat();
      
      onSessionPause?.(updatedSession);
      
      return updatedSession;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to pause tracking session';
      setError(errorMessage);
      onError?.(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [session, stopHeartbeat, onSessionPause, onError]);

  // Resume tracking (e.g., after completing quiz)
  const resumeTracking = useCallback(async () => {
    if (!session || session.status !== 'paused') return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await timeTrackingApi.resumeSession(session.id);
      const updatedSession = { ...session, status: 'active' as const };
      
      setSession(updatedSession);
      setIsPaused(false);
      
      startHeartbeat();
      
      onSessionResume?.(updatedSession);
      
      return updatedSession;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to resume tracking session';
      setError(errorMessage);
      onError?.(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [session, startHeartbeat, onSessionResume, onError]);

  // Stop tracking session
  const stopTracking = useCallback(async () => {
    if (!session || !isTracking) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await timeTrackingApi.endSession(session.id);
      const endedSession = response.session;
      const sessionStats = response.stats;
      
      setSession(endedSession);
      setStats(sessionStats);
      setIsTracking(false);
      setIsPaused(false);
      
      stopHeartbeat();
      
      onSessionEnd?.(endedSession, sessionStats);
      
      return { session: endedSession, stats: sessionStats };
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to end tracking session';
      setError(errorMessage);
      onError?.(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [session, isTracking, stopHeartbeat, onSessionEnd, onError]);

  // Get document statistics
  const getDocumentStats = useCallback(async () => {
    try {
      const response = await timeTrackingApi.getDocumentStats(documentId);
      setStats(response.stats);
      return response;
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || 'Failed to get document stats';
      setError(errorMessage);
      onError?.(error);
      throw error;
    }
  }, [documentId, onError]);

  // Auto-start tracking when component mounts
  useEffect(() => {
    if (autoStart && documentId) {
      startTracking();
    }

    // Cleanup on unmount
    return () => {
      stopHeartbeat();
      // Optionally end session on unmount
      if (sessionRef.current && sessionRef.current.status === 'active') {
        timeTrackingApi.endSession(sessionRef.current.id).catch(console.error);
      }
    };
  }, [documentId, autoStart]); // Only depend on documentId and autoStart

  // Handle page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, stop heartbeat but don't end session
        stopHeartbeat();
      } else {
        // Page is visible again, restart heartbeat if session is active
        if (sessionRef.current && sessionRef.current.status === 'active') {
          startHeartbeat();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [startHeartbeat, stopHeartbeat]);

  // Handle beforeunload event to end session gracefully
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (sessionRef.current && sessionRef.current.status === 'active') {
        // Use sendBeacon for reliable delivery
        const data = JSON.stringify({ session_id: sessionRef.current.id });
        navigator.sendBeacon('/api/users/time-tracking/end-session/', data);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return {
    // State
    session,
    stats,
    isTracking,
    isPaused,
    isLoading,
    error,
    
    // Actions
    startTracking,
    pauseTracking,
    resumeTracking,
    stopTracking,
    getDocumentStats,
    
    // Computed values
    isActive: session?.status === 'active',
    totalTime: session?.total_time_formatted || '0:00',
    sessionId: session?.id,
  };
};
