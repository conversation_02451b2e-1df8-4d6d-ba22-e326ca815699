# Testing CMS Recommendations in Frontend

## What's Been Implemented

### Backend Changes ✅
1. **CMSScore Model**: Stores CMS calculations in database
2. **CMS Calculator**: Calculates scores based on quiz performance + time efficiency
3. **Recommendation Engine**: Generates personalized learning suggestions
4. **Quiz Integration**: Automatically calculates CMS after quiz submission
5. **API Response**: Now includes `recommendations` field in quiz submission response

### Frontend Changes ✅
1. **Updated TypeScript Interfaces**: Added `CMSRecommendationData` and updated `QuizSubmissionResponse`
2. **Enhanced Quiz Feedback Component**: Now displays CMS recommendations
3. **Visual Design**: Added user zone badges, next steps, and analysis
4. **Debug Logging**: Added console logs to track recommendation data

## How to Test

### 1. Start the Servers
```bash
# Backend (Django)
cd core
python manage.py runserver

# Frontend (Next.js)
cd frontend
npm run dev
```

### 2. Create Test Data (Optional)
```bash
cd core
python manage.py test_cms --create-test-data
```

### 3. Test the Flow
1. **Upload a document** or use existing document
2. **Navigate to Quiz page**
3. **Take the quiz** and submit answers
4. **Check the results page** - you should now see:
   - Traditional quiz score and feedback
   - **NEW: Personalized Learning Recommendations section**
   - User zone classification (Mastered, Stable, Moderate Gap, Weak, Unclear)
   - Specific next steps based on performance
   - Analysis of quiz performance and time efficiency

### 4. Check Browser Console
Open browser developer tools and look for:
```
Quiz submission response: {...}
CMS Recommendations received: {...}
```

## Expected Recommendation Display

The quiz results page should now show a new section called **"Personalized Learning Recommendations"** with:

### Main Recommendation Card
- **Learning Zone Badge**: Shows user classification (e.g., "MODERATE GAP")
- **Icon and Message**: Personalized message based on performance
- **Performance Analysis**: Quiz performance and time efficiency breakdown

### Next Steps Card
- **Actionable Items**: Specific steps to improve learning
- **Progressive Disclosure**: Steps are prioritized based on user zone

### Overall Assessment
- **CMS Analysis**: Summary of learning effectiveness

## User Zone Examples

### 🟢 Mastered (90-100)
- **Message**: "You've nailed it! You've unlocked the next chapter."
- **Next Steps**: Move to next topic, help others, apply knowledge

### 🟡 Stable (75-89)
- **Message**: "You're doing well. Review 3 key flashcards, a quick flowchart, and a short summary to stay sharp."
- **Next Steps**: Light review of key materials

### 🟠 Moderate Gap (60-74)
- **Message**: "Not bad! Go through the flashcards, check the flowchart, and read a short summary to improve."
- **Next Steps**: Comprehensive review of all materials

### 🔴 Weak (40-59)
- **Message**: "Let's strengthen your basics. Start with the summary, then the flowchart, and review flashcards."
- **Next Steps**: Structured learning path starting with fundamentals

### ❌ Unclear (< 40)
- **Message**: "This topic needs attention. Go through the full summary, detailed flowchart, and all flashcards."
- **Next Steps**: Complete material review with additional help

## Troubleshooting

### If Recommendations Don't Appear:

1. **Check Backend Logs**:
   ```bash
   # Look for CMS calculation logs
   tail -f core/logs/django.log
   ```

2. **Check Browser Console**:
   - Should see "CMS Recommendations received" message
   - If you see "No CMS recommendations in response", check backend

3. **Verify Database**:
   ```bash
   cd core
   python manage.py shell
   >>> from users.models import CMSScore
   >>> CMSScore.objects.all()
   ```

4. **Check Time Tracking**:
   - Ensure user has spent time on the document
   - Check DocumentTimeTracking model has records

5. **Verify Document Learning Time**:
   - Ensure DocumentLearningTime exists for the document
   - Check if AI prediction was generated

### Common Issues:

1. **No Time Tracking**: If user hasn't spent time on document, CMS calculation may fail
2. **Missing Learning Time**: If no AI prediction exists, system uses default 30 minutes
3. **API Errors**: Check Django logs for CMS calculation errors

## API Response Format

The quiz submission now returns:
```json
{
  "message": "Quiz submitted successfully",
  "quiz_attempt_id": 123,
  "overall_score": 75.0,
  "feedback": [...],
  "recommendations": {
    "user_zone": "moderate_gap",
    "cms_score": 72.5,
    "recommendation": {
      "message": "Not bad! Go through the flashcards...",
      "icon": "🟠",
      "color": "orange"
    },
    "analysis": {
      "quiz_performance": "Good quiz performance (75.0%)...",
      "time_efficiency": "Steady learning pace...",
      "overall_assessment": "CMS Score: 72.5/100..."
    },
    "next_steps": [
      "Study all flashcards thoroughly",
      "Analyze the complete flowchart",
      "Read the summary carefully"
    ]
  },
  "status": "completed"
}
```

## Privacy Note

- **CMS Scores are NOT shown to users** (only in admin backend)
- **Only recommendations are displayed** to maintain user motivation
- **Focus is on actionable guidance** rather than numerical scores
