from django.utils import timezone
from django.core.exceptions import PermissionDenied
from django.http import HttpResponseForbidden
from .models import UserUsage
from django.contrib.auth.models import User
from django.core.cache import cache

class UsageLimitExceeded(Exception):
    """Custom exception for usage limits"""
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)

def get_or_create_user_usage(user: User) -> UserUsage:
    """Get or create user usage with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    
    today = timezone.now().date()
    cache_key = f"user_usage_{user.id}_{today}"
    cached_data = cache.get(cache_key)
    
    if cached_data:
        usage, _ = UserUsage.objects.get_or_create(
            user=user,
            date=today,
            defaults={
                'chat_count': cached_data['chat_count'],
                'file_upload_count': cached_data['file_upload_count']
            }
        )
        return usage
    
    usage, created = UserUsage.objects.get_or_create(
        user=user,
        date=today,
        defaults={'chat_count': 0, 'file_upload_count': 0}
    )
    
    if created:
        usage._update_cache()
        
    return usage

def check_chat_limit(user: User) -> bool:
    """Check chat limit with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    user_usage = get_or_create_user_usage(user)
    return user_usage.can_make_chat()

def check_file_upload_limit(user: User) -> bool:
    """Check file upload limit with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    user_usage = get_or_create_user_usage(user)
    return user_usage.can_upload_file()

def increment_chat_count(user: User):
    """Increment chat count with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    user_usage = get_or_create_user_usage(user)
    if not user_usage.can_make_chat():
        raise UsageLimitExceeded(
            f"Daily chat limit reached. Free users: 5 chats/day, Paid users: 100 chats/day"
        )
    user_usage.increment_chat_count()

def increment_file_upload_count(user: User):
    """Increment file upload count with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    user_usage = get_or_create_user_usage(user)
    if not user_usage.can_upload_file():
        raise UsageLimitExceeded(
            f"Daily file upload limit reached. Free users: 1 file/day, Paid users: 5 files/day"
        )
    user_usage.increment_file_upload_count()

def get_usage_stats(user: User) -> dict:
    """Get usage stats with caching"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    user_usage = get_or_create_user_usage(user)
    return {
        "chat_count": user_usage.chat_count,
        "file_upload_count": user_usage.file_upload_count,
        "chat_limit": user_usage.chat_limit,
        "file_upload_limit": user_usage.file_upload_limit,
        "remaining_chats": user_usage.chat_limit - user_usage.chat_count,
        "remaining_file_uploads": user_usage.file_upload_limit - user_usage.file_upload_count,
        "is_paid": user.userprofile.is_paid
    }

def reset_user_usage(user: User) -> None:
    """Reset user's usage for today"""
    if not user.is_authenticated:
        raise PermissionDenied("User must be authenticated")
    
    today = timezone.now().date()
    cache_key = f"user_usage_{user.id}_{today}"
    
    # Delete from cache
    cache.delete(cache_key)
    
    # Delete from database and create fresh record
    UserUsage.objects.filter(user=user, date=today).delete()
    usage = UserUsage.objects.create(
        user=user,
        date=today,
        chat_count=0,
        file_upload_count=0
    )
    usage._update_cache()
    return usage