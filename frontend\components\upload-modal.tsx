"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Upload, File, X } from "lucide-react"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { documentApi } from "@/lib/api"
import { LoadingOverlay } from "@/components/loading-overlay"

interface UploadModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  onUploadSuccess?: () => void
  groupId?: number | null
  groups?: { id: number; name: string }[]
}

export function UploadModal({ isOpen, setIsOpen, onUploadSuccess, groupId, groups = [] }: UploadModalProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<number | null>(groupId ?? null);
  const fileInputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Keep selectedGroup in sync with groupId prop when modal opens or groupId changes
  useEffect(() => {
    setSelectedGroup(groupId ?? null);
  }, [groupId, isOpen]);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles((prev) => [...prev, ...newFiles])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      setFiles((prev) => [...prev, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  const handleUpload = async () => {
    if (files.length === 0) return;
    setIsUploading(true);
    try {
      const response = await documentApi.uploadDocument(files[0], selectedGroup ?? undefined);

      if (response.message) {
        toast.success(response.message);

        // Clear any previous document data
        localStorage.removeItem("uploadedFiles");
        localStorage.removeItem("filePreviewUrls");
        localStorage.removeItem("currentDocumentId");

        // Store the new file info in localStorage with document ID
        if (response.document_id) {
          localStorage.setItem("currentDocumentId", response.document_id.toString());

          localStorage.setItem(
            "uploadedFiles",
            JSON.stringify(
              files.map((file) => ({
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified,
                documentId: response.document_id, // Include document ID
              })),
            ),
          );
        }

        setIsOpen(false);
        // Call the callback to refresh documents list
        onUploadSuccess?.();

        // Navigate to process page with document ID
        const documentId = response.document_id;
        if (documentId) {
          router.push(`/process?type=upload&documentId=${documentId}`);
        } else {
          router.push("/process?type=upload");
        }
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || "Failed to upload file. Please try again.");
      setIsUploading(false); // Hide loading overlay on error
    }
  }

  return (
    <>
      <LoadingOverlay
        isVisible={isUploading}
        message="Uploading and processing your file. This may take a few moments..."
      />
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-md bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-center">Upload Files</DialogTitle>
        </DialogHeader>

        {/* Group/Space Selector */}
        {groups.length > 0 && (
          <div className="mb-4">
            <label htmlFor="group-select" className="block text-sm font-medium mb-1 text-neutral-300">Select Space/Group</label>
            <select
              id="group-select"
              className="w-full p-2 rounded border border-neutral-700 bg-neutral-800 text-neutral-100"
              value={selectedGroup ?? ''}
              onChange={e => setSelectedGroup(e.target.value ? Number(e.target.value) : null)}
              disabled={isUploading}
            >
              <option value="">No Space (default)</option>
              {groups.map(group => (
                <option key={group.id} value={group.id}>{group.name}</option>
              ))}
            </select>
          </div>
        )}

        <div
          className={`mt-4 border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            isDragging ? "border-purple-500 bg-purple-500/10" : "border-neutral-700"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" multiple />

          <motion.div
            initial={{ scale: 1 }}
            animate={{ scale: isDragging ? 1.05 : 1 }}
            className="flex flex-col items-center"
          >
            <div className="p-3 bg-neutral-800 rounded-full mb-3">
              <Upload className="h-6 w-6 text-purple-500" />
            </div>
            <p className="text-neutral-300 mb-2">Drag and drop files here</p>
            <p className="text-neutral-500 text-sm mb-4">or</p>
            <Button onClick={triggerFileInput} className="bg-purple-600 hover:bg-purple-700">
              Browse Files
            </Button>
            <p className="text-neutral-500 text-xs mt-4">Supported formats: PDF, PPT, DOC, TXT, MP3, WAV</p>
          </motion.div>
        </div>

        {files.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Selected Files</h3>
            <div className="max-h-40 overflow-y-auto space-y-2">
              {files.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-neutral-800 p-2 rounded">
                  <div className="flex items-center">
                    <File className="h-4 w-4 mr-2 text-purple-500" />
                    <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                  </div>
                  <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => removeFile(index)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            <div className="mt-4 flex justify-end gap-2">
              <Button variant="outline" onClick={() => setFiles([])}>
                Clear All
              </Button>
              <Button
                className="bg-purple-600 hover:bg-purple-700"
                disabled={files.length === 0}
                onClick={handleUpload}
              >
                Upload {files.length} {files.length === 1 ? "File" : "Files"}
              </Button>
            </div>
          </div>
        )}
        </DialogContent>
      </Dialog>
    </>
  )
}
