"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-dedent";
exports.ids = ["vendor-chunks/ts-dedent"];
exports.modules = {

/***/ "(ssr)/./node_modules/ts-dedent/esm/index.js":
/*!*********************************************!*\
  !*** ./node_modules/ts-dedent/esm/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedent: () => (/* binding */ dedent),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction dedent(templ) {\n    var values = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        values[_i - 1] = arguments[_i];\n    }\n    var strings = Array.from(typeof templ === 'string' ? [templ] : templ);\n    strings[strings.length - 1] = strings[strings.length - 1].replace(/\\r?\\n([\\t ]*)$/, '');\n    var indentLengths = strings.reduce(function (arr, str) {\n        var matches = str.match(/\\n([\\t ]+|(?!\\s).)/g);\n        if (matches) {\n            return arr.concat(matches.map(function (match) { var _a, _b; return (_b = (_a = match.match(/[\\t ]/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0; }));\n        }\n        return arr;\n    }, []);\n    if (indentLengths.length) {\n        var pattern_1 = new RegExp(\"\\n[\\t ]{\" + Math.min.apply(Math, indentLengths) + \"}\", 'g');\n        strings = strings.map(function (str) { return str.replace(pattern_1, '\\n'); });\n    }\n    strings[0] = strings[0].replace(/^\\r?\\n/, '');\n    var string = strings[0];\n    values.forEach(function (value, i) {\n        var endentations = string.match(/(?:^|\\n)( *)$/);\n        var endentation = endentations ? endentations[1] : '';\n        var indentedValue = value;\n        if (typeof value === 'string' && value.includes('\\n')) {\n            indentedValue = String(value)\n                .split('\\n')\n                .map(function (str, i) {\n                return i === 0 ? str : \"\" + endentation + str;\n            })\n                .join('\\n');\n        }\n        string += indentedValue + strings[i + 1];\n    });\n    return string;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dedent);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ts-dedent/esm/index.js\n");

/***/ })

};
;