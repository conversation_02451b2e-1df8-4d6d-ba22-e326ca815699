"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Mic, Square, Play, Pause, Save } from "lucide-react"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"

interface RecordModalProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

export function RecordModal({ isOpen, setIsOpen }: RecordModalProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioURL, setAudioURL] = useState<string | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const router = useRouter()

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
      if (audioURL) {
        URL.revokeObjectURL(audioURL)
      }
    }
  }, [audioURL])

  // Reset timer when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      // Reset all states when modal closes
      setRecordingTime(0)
      setIsRecording(false)
      setIsPaused(false)
      setAudioURL(null)

      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }

      // Stop any ongoing recording
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop()
        mediaRecorderRef.current.stream?.getTracks().forEach((track) => track.stop())
      }
    }
  }, [isOpen])

  const startRecording = async () => {
    try {
      // Clear any existing timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }

      // Reset timer to 0
      setRecordingTime(0)

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" })
        const url = URL.createObjectURL(audioBlob)
        setAudioURL(url)

        // Stop all audio tracks
        stream.getTracks().forEach((track) => track.stop())
      }

      mediaRecorder.start()
      setIsRecording(true)
      setIsPaused(false)

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    } catch (error) {
      console.error("Error accessing microphone:", error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      setIsPaused(false)

      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }

      // Stop all audio tracks
      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop())
      }
    }
  }

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pause()
      setIsPaused(true)

      // Pause timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resume()
      setIsPaused(false)

      // Resume timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleProcessRecording = () => {
    if (audioURL) {
      // Store recording info in localStorage to simulate persistence
      localStorage.setItem(
        "recordedAudio",
        JSON.stringify({
          duration: formatTime(recordingTime),
          timestamp: new Date().toISOString(),
        }),
      )
    }

    setIsOpen(false)
    router.push("/process?type=record")
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          // If closing modal while recording, stop recording
          if (isRecording) {
            stopRecording()
          }
          // Clear timer and reset states
          if (timerRef.current) {
            clearInterval(timerRef.current)
            timerRef.current = null
          }
        }
        setIsOpen(open)
      }}
    >
      <DialogContent className="sm:max-w-md bg-neutral-900 border-neutral-800">
        <DialogHeader>
          <DialogTitle className="text-center">Record Audio</DialogTitle>
        </DialogHeader>

        <div className="mt-4 flex flex-col items-center">
          <motion.div
            initial={{ scale: 1 }}
            animate={{
              scale: isRecording && !isPaused ? [1, 1.1, 1] : 1,
              transition: {
                repeat: isRecording && !isPaused ? Number.POSITIVE_INFINITY : 0,
                duration: 1.5,
              },
            }}
            className="relative mb-6"
          >
            <div className={`p-8 rounded-full ${isRecording ? "bg-red-500/20" : "bg-neutral-800"}`}>
              <Mic className={`h-10 w-10 ${isRecording ? "text-red-500" : "text-purple-500"}`} />
            </div>
            {isRecording && !isPaused && (
              <motion.div
                className="absolute inset-0 rounded-full border-2 border-red-500"
                initial={{ scale: 1, opacity: 1 }}
                animate={{
                  scale: [1, 1.2, 1.2, 1],
                  opacity: [1, 0.8, 0.2, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: "easeOut",
                }}
              />
            )}
          </motion.div>

          <div className="text-2xl font-mono mb-6">{formatTime(recordingTime)}</div>

          <div className="flex gap-4 mb-6">
            {!isRecording ? (
              <Button onClick={startRecording} className="bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0">
                <Mic className="h-5 w-5" />
              </Button>
            ) : (
              <>
                <Button onClick={stopRecording} className="bg-red-600 hover:bg-red-700 rounded-full h-12 w-12 p-0">
                  <Square className="h-5 w-5" />
                </Button>

                {isPaused ? (
                  <Button
                    onClick={resumeRecording}
                    className="bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0"
                  >
                    <Play className="h-5 w-5" />
                  </Button>
                ) : (
                  <Button
                    onClick={pauseRecording}
                    className="bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0"
                  >
                    <Pause className="h-5 w-5" />
                  </Button>
                )}
              </>
            )}
          </div>

          {audioURL && (
            <div className="w-full">
              <audio ref={audioRef} src={audioURL} controls className="w-full mb-4" />
              <Button onClick={handleProcessRecording} className="w-full bg-purple-600 hover:bg-purple-700">
                <Save className="h-4 w-4 mr-2" />
                Process Recording
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
