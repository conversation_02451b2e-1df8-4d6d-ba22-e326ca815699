"use client"

import React from "react"
import { useState, useEffect } from "react"
import { Plus, CuboidIcon as Cube, Upload, Link, Mic, Menu, Sun, Moon, LogOut, FileText, Clock, CheckCircle, AlertCircle, Crown } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sidebar } from "@/components/sidebar"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { useTheme } from "@/components/theme-provider"
import { CreateSpaceDialog } from "@/components/create-space-dialog"
import { UploadModal } from "@/components/upload-modal"
import { PasteModal } from "@/components/paste-modal"
import { RecordModal } from "@/components/record-modal"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { documentApi } from "@/lib/api"
import { navigationEvents } from "@/lib/navigation-events"

interface DocumentGroup {
  id: number
  name: string
  description?: string
  created_at: string
  updated_at: string
}

interface Document {
  id: number
  title: string
  file: string
  uploaded_at: string
  processing_status: 'pending' | 'processing' | 'completed' | 'failed'
  error_message?: string
  num_embeddings: number
  group?: DocumentGroup
}

export function CogniUI() {
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [createSpaceOpen, setCreateSpaceOpen] = useState(false)
  const [uploadModalOpen, setUploadModalOpen] = useState(false)
  const [pasteModalOpen, setPasteModalOpen] = useState(false)
  const [recordModalOpen, setRecordModalOpen] = useState(false)

  // Mock user state - in a real app, this would come from your auth system
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [username, setUsername] = useState("")

  // Documents state
  const [documents, setDocuments] = useState<Document[]>([])
  const [documentsLoading, setDocumentsLoading] = useState(false)
  const [documentsError, setDocumentsError] = useState<string | null>(null)
  const [groups, setGroups] = useState<DocumentGroup[]>([])
  const [selectedGroup, setSelectedGroup] = useState<number | null>(null)

  const handleUpgradeClick = () => {
    navigationEvents.triggerNavigation()
    router.push("/subscription")
  }

  const handleSignIn = () => {
    navigationEvents.triggerNavigation()
    router.push("/auth")
  }

  const handleLogout = () => {
    // Clear localStorage
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    localStorage.removeItem("token")
    setIsLoggedIn(false)
    setUsername("")
    setDocuments([])
    navigationEvents.triggerNavigation()
    router.push("/")
  }

  // Function to fetch user documents
  const fetchDocuments = async () => {
    if (!isLoggedIn) return

    setDocumentsLoading(true)
    setDocumentsError(null)

    try {
      const response = await documentApi.getDocuments()
      setDocuments(response.results || response || [])
    } catch (error) {
      console.error('Error fetching documents:', error)
      setDocumentsError('Failed to load documents')
    } finally {
      setDocumentsLoading(false)
    }
  }

  // Handle document click - navigate to learning page
  const handleDocumentClick = (document: Document) => {
    if (document.processing_status === 'completed') {
      navigationEvents.triggerNavigation()
      router.push(`/process?documentId=${document.id}&type=upload`)
    }
  }

  // Check auth state on mount
  React.useEffect(() => {
    const userLoggedIn = localStorage.getItem("isLoggedIn") === "true"
    const storedUsername = localStorage.getItem("username")

    if (userLoggedIn && storedUsername) {
      setIsLoggedIn(true)
      setUsername(storedUsername)
    }
  }, [])

  // Fetch documents when user logs in
  useEffect(() => {
    if (isLoggedIn) {
      fetchDocuments()
    }
  }, [isLoggedIn])

  // Fetch groups when user logs in
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const groupsData = await documentApi.getGroups();
        // Ensure groups is always an array
        setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
        setGroups([]);
      }
    };

    if (isLoggedIn) {
      fetchGroups();
    }
  }, [isLoggedIn]);

  // Fetch documents when user logs in or selectedGroup changes
  useEffect(() => {
    const fetchDocumentsByGroup = async () => {
      setDocumentsLoading(true);
      setDocumentsError(null);
      try {
        const response = await documentApi.getDocuments(selectedGroup || undefined);
        setDocuments(response.results || response || []);
      } catch (error) {
        console.error('Error fetching documents:', error);
        setDocumentsError('Failed to load documents');
      } finally {
        setDocumentsLoading(false);
      }
    };

    if (isLoggedIn) {
      fetchDocumentsByGroup();
    }
  }, [isLoggedIn, selectedGroup]);

  const handleCreateGroup = () => {
    setCreateSpaceOpen(true);
  };

  return (
    <div className="flex h-screen bg-background text-foreground overflow-hidden">
      {/* Off-canvas Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
        isLoggedIn={isLoggedIn}
        username={username}
        onLogout={handleLogout}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="flex justify-between p-4">
          <Button variant="ghost" size="icon" className="h-10 w-10 rounded-full" onClick={() => setSidebarOpen(true)}>
            <Menu className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            {/* User email/login (leftmost when logged in) */}
            {isLoggedIn ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="border-2 border-gray-300 hover:border-gray-500 bg-white hover:bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-600 dark:hover:border-gray-400 dark:hover:bg-gray-800">{username}</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button className="bg-purple-600 hover:bg-purple-700" onClick={handleSignIn}>
                Sign In / Register
              </Button>
            )}

            {/* Upgrade button (middle) */}
            <Button
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
              size="sm"
              onClick={handleUpgradeClick}
            >
              <Crown className="h-4 w-4 mr-2" />
              Upgrade
            </Button>

            {/* Theme button (rightmost) */}
            <Button
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        <div className="max-w-5xl mx-auto px-4 py-8">
          <div className="flex justify-center mb-8">
            <div className="relative h-16 w-16">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                alt="Cognimosity Logo"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-center mb-12">Ready to unlock something new today?</h1>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-16">
            <button
              onClick={() => setUploadModalOpen(true)}
              className={`border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative ${
                theme === "light" ? "border-black bg-white" : "border-neutral-800"
              }`}
            >
              <div className="absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full">Popular</div>
              <div
                className={`mb-4 p-3 rounded-full transition-all duration-300 ${
                  theme === "light" ? "bg-white border border-black" : "bg-neutral-900"
                } group-hover:bg-purple-500/20`}
              >
                <Upload
                  className={`h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === "light" ? "text-black" : ""}`}
                />
              </div>
              <h3 className="text-lg font-medium mb-1">Upload</h3>
              <p className={`text-sm text-center ${theme === "light" ? "text-neutral-600" : "text-neutral-400"}`}>
                PDF, PPT, DOC, TXT, AUDIO
              </p>
            </button>

            <button
              onClick={() => setPasteModalOpen(true)}
              className={`border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ${
                theme === "light" ? "border-black bg-white" : "border-neutral-800"
              }`}
            >
              <div
                className={`mb-4 p-3 rounded-full transition-all duration-300 ${
                  theme === "light" ? "bg-white border border-black" : "bg-neutral-900"
                } group-hover:bg-purple-500/20`}
              >
                <Link
                  className={`h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === "light" ? "text-black" : ""}`}
                />
              </div>
              <h3 className="text-lg font-medium mb-1">Paste</h3>
              <p className={`text-sm text-center ${theme === "light" ? "text-neutral-600" : "text-neutral-400"}`}>
                YouTube, Website, Text
              </p>
            </button>

            <button
              onClick={() => setRecordModalOpen(true)}
              className={`border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ${
                theme === "light" ? "border-black bg-white" : "border-neutral-800"
              }`}
            >
              <div
                className={`mb-4 p-3 rounded-full transition-all duration-300 ${
                  theme === "light" ? "bg-white border border-black" : "bg-neutral-900"
                } group-hover:bg-purple-500/20`}
              >
                <Mic
                  className={`h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ${theme === "light" ? "text-black" : ""}`}
                />
              </div>
              <h3 className="text-lg font-medium mb-1">Record</h3>
              <p className={`text-sm text-center ${theme === "light" ? "text-neutral-600" : "text-neutral-400"}`}>
                Record Your Lecture
              </p>
            </button>
          </div>

          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold">My spaces</h2>
              <Button
                variant="outline"
                className="gap-2 border-dashed hover:border-purple-500 transition-all duration-300"
                onClick={handleCreateGroup}
              >
                <Plus className="h-4 w-4" />
                Add space
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {groups.length > 0 ? (
                groups.map((group) => (
                  <div
                    key={group.id}
                    className={`border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 cursor-pointer ${
                      selectedGroup === group.id ? 'border-purple-500 bg-purple-50' : theme === "light" ? "border-black bg-white" : "border-neutral-800"
                    }`}
                    onClick={() => setSelectedGroup(group.id)}
                  >
                    <div className="flex items-center gap-3">
                      <Cube className="h-5 w-5" />
                      <span className="font-medium">{group.name}</span>
                    </div>
                    <span className="text-xs text-gray-400">{new Date(group.created_at).toLocaleDateString()}</span>
                  </div>
                ))
              ) : (
                <div className={`border rounded-lg p-4 text-center ${theme === "light" ? "border-gray-200 bg-gray-50" : "border-neutral-700 bg-neutral-800"}`}>
                  No spaces yet. Create one to get started!
                </div>
              )}
            </div>
          </div>

          {/* My Documents Section - Show when logged in */}
          {isLoggedIn && (
            <div className="mb-12">
              <div className="flex flex-col gap-4">
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={selectedGroup === null ? "default" : "outline"}
                    onClick={() => setSelectedGroup(null)}
                    className="flex items-center gap-2"
                  >
                    <FileText className="h-4 w-4" />
                    All Documents
                  </Button>
                  {groups.map((group) => (
                    <Button
                      key={group.id}
                      variant={selectedGroup === group.id ? "default" : "outline"}
                      onClick={() => setSelectedGroup(group.id)}
                      className="flex items-center gap-2"
                    >
                      <Cube className="h-4 w-4" />
                      {group.name}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    className="gap-2 border-dashed hover:border-purple-500 transition-all duration-300"
                    onClick={handleCreateGroup}
                  >
                    <Plus className="h-4 w-4" />
                    Add space
                  </Button>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">
                      {selectedGroup 
                        ? `Documents in ${groups.find(g => g.id === selectedGroup)?.name}`
                        : 'All Documents'}
                    </h2>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={fetchDocuments}
                        disabled={documentsLoading}
                      >
                        {documentsLoading ? "Loading..." : "Refresh"}
                      </Button>
                      <Button
                        className="bg-purple-600 hover:bg-purple-700"
                        onClick={() => setUploadModalOpen(true)}
                      >
                        <Upload className="h-4 w-4 mr-1" /> Upload Document
                      </Button>
                    </div>
                  </div>
                  {documentsError && (
                    <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
                      {documentsError}
                    </div>
                  )}
                  {documentsLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {[1, 2, 3].map((i) => (
                        <div
                          key={i}
                          className={`border rounded-lg p-4 animate-pulse ${
                            theme === "light" ? "border-gray-200 bg-gray-50" : "border-neutral-700 bg-neutral-800"
                          }`}
                        >
                          <div className="h-4 bg-gray-300 rounded mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded mb-1"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </div>
                      ))}
                    </div>
                  ) : documents.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {documents.map((document) => (
                        <div
                          key={document.id}
                          onClick={() => handleDocumentClick(document)}
                          className={`border rounded-lg p-4 transition-all duration-300 hover:shadow-lg ${
                            document.processing_status === 'completed'
                              ? 'cursor-pointer hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)]'
                              : 'cursor-not-allowed opacity-60'
                          } ${
                            theme === "light" ? "border-black bg-white" : "border-neutral-800"
                          }`}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <FileText className="h-5 w-5 text-purple-500" />
                              <span className="font-medium text-sm truncate">{document.title}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {document.group && !selectedGroup && (
                                <span className="text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-800">
                                  {groups.find(g => g.id === Number(document.group))?.name || 'Group'}
                                </span>
                              )}
                              {document.processing_status === 'completed' && (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              )}
                              {document.processing_status === 'processing' && (
                                <Clock className="h-4 w-4 text-yellow-500 animate-spin" />
                              )}
                              {document.processing_status === 'failed' && (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                          </div>
                          <p className={`text-xs mb-2 ${theme === "light" ? "text-gray-600" : "text-gray-400"}`}>
                            Uploaded: {new Date(document.uploaded_at).toLocaleDateString()}
                          </p>
                          <div className="flex items-center justify-between">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              document.processing_status === 'completed'
                                ? 'bg-green-100 text-green-800'
                                : document.processing_status === 'processing'
                                ? 'bg-yellow-100 text-yellow-800'
                                : document.processing_status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {document.processing_status.charAt(0).toUpperCase() + document.processing_status.slice(1)}
                            </span>
                            {document.processing_status === 'completed' && (
                              <span className={`text-xs ${theme === "light" ? "text-gray-500" : "text-gray-400"}`}>
                                Click to learn
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={`border rounded-lg p-8 text-center ${
                      theme === "light" ? "border-gray-200 bg-gray-50" : "border-neutral-700 bg-neutral-800"
                    }`}>
                      <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <h3 className="text-lg font-medium mb-2">No documents {selectedGroup ? 'in this space' : 'yet'}</h3>
                      <p className={`text-sm mb-4 ${theme === "light" ? "text-gray-600" : "text-gray-400"}`}>
                        Upload your first document to get started with learning
                      </p>
                      <Button
                        onClick={() => setUploadModalOpen(true)}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        Upload Document
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold">Continue learning</h2>
              <Button variant="link" className="text-neutral-400">
                View all
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div
                className={`border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${
                  theme === "light" ? "border-black bg-white" : "border-neutral-800"
                }`}
              >
                <div className="h-32 bg-purple-900 flex items-center justify-center">
                  <div className="text-4xl font-bold text-white">MAP</div>
                </div>
              </div>

              <div
                className={`border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${
                  theme === "light" ? "border-black bg-white" : "border-neutral-800"
                }`}
              >
                <div className={`h-32 ${theme === "light" ? "bg-gray-100" : "bg-neutral-900"}`}></div>
              </div>

              <div
                className={`border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ${
                  theme === "light" ? "border-black bg-white" : "border-neutral-800"
                }`}
              >
                <div className={`h-32 ${theme === "light" ? "bg-gray-100" : "bg-neutral-900"}`}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <UploadModal
        isOpen={uploadModalOpen}
        setIsOpen={setUploadModalOpen}
        onUploadSuccess={fetchDocuments}
        groupId={selectedGroup}
        groups={groups}
      />
      <PasteModal isOpen={pasteModalOpen} setIsOpen={setPasteModalOpen} />
      <RecordModal isOpen={recordModalOpen} setIsOpen={setRecordModalOpen} />

      {/* Create Space Dialog */}
      <CreateSpaceDialog 
        open={createSpaceOpen} 
        setOpen={setCreateSpaceOpen} 
        onCreated={() => {
          // Refresh groups after creation
          (async () => {
            try {
              const groupsData = await documentApi.getGroups();
              setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);
            } catch (error) {
              setGroups([]);
            }
          })();
        }}
      />
    </div>
  )
}
