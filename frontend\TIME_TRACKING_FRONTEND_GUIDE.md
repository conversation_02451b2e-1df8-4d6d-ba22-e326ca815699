# Time Tracking Frontend Implementation Guide

This document provides a comprehensive guide to the time tracking frontend implementation for the Cognimosity learning platform.

## Overview

The time tracking system monitors user study time across documents, providing insights into learning patterns and habits. It integrates seamlessly with the existing document viewing and quiz functionality.

## Architecture

### Core Components

1. **Time Tracking Hook** (`hooks/use-time-tracking.ts`)
   - Main hook for individual document time tracking
   - Handles session start/stop, pause/resume, heartbeat
   - Automatic cleanup and error handling

2. **Time Tracking Context** (`hooks/use-time-tracking-context.tsx`)
   - Global state management for multiple sessions
   - Quiz integration (pause/resume during quizzes)
   - Bulk session operations

3. **Time Tracking Widget** (`components/time-tracking-widget.tsx`)
   - UI component for displaying timer and controls
   - Compact and full-size variants
   - Real-time updates and statistics

4. **Time Tracking Dashboard** (`components/time-tracking-dashboard.tsx`)
   - Comprehensive overview of all time tracking data
   - Session management and statistics
   - Document-specific insights

## Key Features

### Automatic Time Tracking
- Starts automatically when user accesses a document
- Continues running in background with heartbeat mechanism
- Handles page visibility changes and browser events

### Quiz Integration
- Automatically pauses time tracking when quiz starts
- Resumes tracking when quiz completes
- Maintains separate quiz time vs study time

### Session Management
- Multiple concurrent sessions for different documents
- Graceful handling of browser close/refresh
- Automatic cleanup of abandoned sessions

### Statistics and Insights
- Total study time per document
- Session count and average duration
- View count and reopening patterns
- Study efficiency ratings

## Usage Examples

### Basic Time Tracking

```tsx
import { useTimeTracking } from '@/hooks/use-time-tracking';

function DocumentViewer({ documentId }: { documentId: number }) {
  const {
    isTracking,
    totalTime,
    startTracking,
    stopTracking,
    pauseTracking,
    resumeTracking
  } = useTimeTracking({
    documentId,
    autoStart: true,
    onSessionStart: (session) => console.log('Started:', session),
    onSessionEnd: (session, stats) => console.log('Ended:', session, stats)
  });

  return (
    <div>
      <div>Study Time: {totalTime}</div>
      <div>Status: {isTracking ? 'Active' : 'Stopped'}</div>
      <button onClick={startTracking}>Start</button>
      <button onClick={stopTracking}>Stop</button>
    </div>
  );
}
```

### Time Tracking Widget

```tsx
import { TimeTrackingWidget, SimpleTimer } from '@/components/time-tracking-widget';

// Full widget with controls and stats
<TimeTrackingWidget 
  documentId={documentId}
  documentTitle="My Document"
  showStats={true}
/>

// Simple timer display
<SimpleTimer documentId={documentId} />
```

### Global Context Usage

```tsx
import { useTimeTrackingContext } from '@/hooks/use-time-tracking-context';

function GlobalControls() {
  const { 
    activeSessions, 
    pauseAllSessions, 
    resumeAllSessions,
    endAllSessions 
  } = useTimeTrackingContext();

  return (
    <div>
      <div>Active Sessions: {activeSessions.size}</div>
      <button onClick={() => pauseAllSessions('Break time')}>
        Pause All
      </button>
      <button onClick={resumeAllSessions}>Resume All</button>
      <button onClick={endAllSessions}>End All</button>
    </div>
  );
}
```

## Integration Points

### Document Viewing Page
The main document viewing interface (`app/process/page.tsx`) includes:
- TimeTrackingProvider wrapper for global state
- SimpleTimer in the header for current document
- Automatic session management

### Quiz Interface
The quiz component (`components/quiz-interface.tsx`) integrates with:
- Automatic pause when quiz starts
- Automatic resume when quiz completes
- Time tracking context for session management

### Performance Dashboard
Time tracking data is available in the performance dashboard for:
- Study time analysis
- Session pattern insights
- Learning efficiency metrics

## API Integration

### Endpoints Used
- `POST /users/time-tracking/start-session/` - Start new session
- `POST /users/time-tracking/update-activity/` - Heartbeat
- `POST /users/time-tracking/pause-resume/` - Pause/resume
- `POST /users/time-tracking/end-session/` - End session
- `GET /users/time-tracking/document/{id}/stats/` - Document stats
- `GET /users/time-tracking/overview/` - User overview

### Authentication
All API calls use token-based authentication from localStorage:
```javascript
Authorization: Token ${localStorage.getItem('token')}
```

## Configuration Options

### Hook Options
```typescript
interface UseTimeTrackingOptions {
  documentId: number;
  heartbeatInterval?: number; // Default: 30000ms (30 seconds)
  autoStart?: boolean; // Default: true
  onSessionStart?: (session) => void;
  onSessionEnd?: (session, stats) => void;
  onSessionPause?: (session) => void;
  onSessionResume?: (session) => void;
  onError?: (error) => void;
}
```

### Widget Options
```typescript
interface TimeTrackingWidgetProps {
  documentId: number;
  documentTitle?: string;
  className?: string;
  compact?: boolean; // Default: false
  showStats?: boolean; // Default: true
  onQuizStart?: () => void;
  onQuizEnd?: () => void;
}
```

## Error Handling

The system includes comprehensive error handling:
- Network failures during API calls
- Session recovery after page refresh
- Graceful degradation when backend is unavailable
- User-friendly error messages via toast notifications

## Performance Considerations

### Heartbeat Optimization
- Configurable heartbeat interval (default 30 seconds)
- Automatic pause when page is hidden
- Efficient batch operations for multiple sessions

### Memory Management
- Automatic cleanup of intervals and event listeners
- Proper session state management
- Minimal re-renders with optimized React hooks

## Browser Compatibility

### Supported Features
- Page Visibility API for background detection
- Navigator.sendBeacon for reliable session ending
- LocalStorage for session persistence
- Modern JavaScript features (ES2020+)

### Fallbacks
- Graceful degradation for older browsers
- Alternative session ending methods
- Basic timer functionality without advanced features

## Troubleshooting

### Common Issues

1. **Sessions not starting**
   - Check authentication token
   - Verify documentId is valid
   - Check network connectivity

2. **Heartbeat failures**
   - Non-critical, system continues working
   - Check console for network errors
   - Verify backend availability

3. **Time not updating**
   - Check if session is active
   - Verify component is properly mounted
   - Check for JavaScript errors

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('timeTrackingDebug', 'true');
```

## Future Enhancements

### Planned Features
- Offline time tracking with sync
- Advanced analytics and insights
- Study goal setting and tracking
- Integration with calendar systems
- Export functionality for time data

### Performance Improvements
- WebWorker for background processing
- IndexedDB for local data storage
- Real-time synchronization with WebSockets
- Advanced caching strategies

## Testing

### Unit Tests
Run component and hook tests:
```bash
npm test -- --testPathPattern=time-tracking
```

### Integration Tests
Test full user workflows:
```bash
npm run test:e2e -- --spec="time-tracking"
```

### Manual Testing Checklist
- [ ] Session starts automatically on document access
- [ ] Timer updates in real-time
- [ ] Pause/resume works correctly
- [ ] Quiz integration pauses/resumes properly
- [ ] Session ends gracefully on page close
- [ ] Statistics display correctly
- [ ] Error handling works as expected

## Support

For issues or questions regarding the time tracking implementation:
1. Check this documentation
2. Review the API documentation
3. Check browser console for errors
4. Contact the development team

---

*Last updated: 2025-01-25*
