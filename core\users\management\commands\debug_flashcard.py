from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Debug flashcard table structure and data'

    def handle(self, *args, **options):
        print("Debugging flashcard table...")
        
        with connection.cursor() as cursor:
            # Check table structure
            cursor.execute("PRAGMA table_info(documents_flashcard);")
            columns = cursor.fetchall()
            print("Flashcard table structure:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'}")
            
            # Check data
            cursor.execute("SELECT * FROM documents_flashcard LIMIT 3;")
            data = cursor.fetchall()
            print(f"\nFlashcard data ({len(data)} rows):")
            for row in data:
                print(f"  {row}")
