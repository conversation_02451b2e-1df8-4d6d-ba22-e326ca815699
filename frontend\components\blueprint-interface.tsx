"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@/components/ui/skeleton"
import { Copy, CheckCircle, FileText, Sparkles, RefreshCw, Target } from "lucide-react"
import { toast } from "sonner"
import { blueprintApi } from "@/lib/api"
import { Markdown } from "@/components/ui/markdown"

interface Topic {
  id: number; // Add id for topic
  title: string;
  weightage: number;
  content?: string; // Optional content for the topic
}

interface BlueprintInterfaceProps {
  documentId?: number;
}

export function BlueprintInterface({ documentId }: BlueprintInterfaceProps) {
  const [blueprint, setBlueprint] = useState<string>("")
  const [topics, setTopics] = useState<Topic[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [focusAreas, setFocusAreas] = useState("")
  const [showUpload, setShowUpload] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [manualBlueprint, setManualBlueprint] = useState("")
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [editedBlueprint, setEditedBlueprint] = useState<string>("")
  const [isSaving, setIsSaving] = useState(false)
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null)
  const [topicContent, setTopicContent] = useState<string>("")
  const [topicLoading, setTopicLoading] = useState(false)
  const [topicError, setTopicError] = useState<string | null>(null)

  useEffect(() => {
    if (documentId) {
      loadBlueprint()
    }
  }, [documentId])

  useEffect(() => {
    setEditedBlueprint(blueprint)
  }, [blueprint])

  const loadBlueprint = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }
    setIsLoading(true)
    setError(null)
    setShowUpload(false)
    try {
      const existingBlueprint = await blueprintApi.getBlueprint(documentId)
      if (existingBlueprint && existingBlueprint.blueprint) {
        setBlueprint(existingBlueprint.blueprint)
        // Fetch topics from BlueprintTopics endpoint
        try {
          const topics = await blueprintApi.getBlueprintTopics(documentId)
          setTopics(
            topics.map((t: any) => ({
              ...t,
              id: t.id,
              weightage: typeof t.weightage === "string" ? parseFloat(t.weightage) : t.weightage
            }))
          )
        } catch (topicErr) {
          setTopics([])
        }
        setShowUpload(false)
        return
      } else {
        setShowUpload(true)
        setBlueprint("")
        setTopics([])
        return
      }
    } catch (err: any) {
      // If 404 or error, show upload UI
      setShowUpload(true)
      setBlueprint("")
      setTopics([])
      return
    } finally {
      setIsLoading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0])
    }
  }

  const handleUploadBlueprint = async () => {
    if (!documentId || (!selectedFile && !manualBlueprint.trim())) {
      toast.error("Please select a file or enter blueprint text.")
      return
    }
    setUploading(true)
    setError(null)
    try {
      let uploadResult
      if (selectedFile) {
        uploadResult = await blueprintApi.uploadBlueprint(documentId, selectedFile)
      } else {
        uploadResult = await blueprintApi.uploadBlueprint(documentId, manualBlueprint)
      }
      toast.success('Blueprint uploaded!')
      setShowUpload(false)
      setManualBlueprint("")
      setSelectedFile(null)
      loadBlueprint()
    } catch (err: any) {
      setError(err.message || 'Failed to upload blueprint')
      toast.error(err.message || 'Failed to upload blueprint')
    } finally {
      setUploading(false)
    }
  }

  const generateNewBlueprint = async () => {
    if (!documentId) {
      toast.error("No document selected for blueprint generation")
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      console.log('Generating new blueprint for document:', documentId, 'focus areas:', focusAreas)
      const response = await blueprintApi.generateBlueprint(documentId, focusAreas)
      setBlueprint(response.blueprint)
      setTopics(response.topics || [])
      toast.success("New blueprint generated successfully!")
    } catch (err) {
      console.error('Error generating blueprint:', err)
      setError('Failed to generate new blueprint. Please try again.')
      toast.error("Failed to generate new blueprint")
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSaveBlueprint = async () => {
    if (!documentId || !editedBlueprint.trim()) {
      toast.error("Blueprint cannot be empty.")
      return
    }
    setIsSaving(true)
    setError(null)
    try {
      await blueprintApi.uploadBlueprint(documentId, editedBlueprint)
      setBlueprint(editedBlueprint)
      toast.success("Blueprint saved!")
    } catch (err: any) {
      setError(err.message || 'Failed to save blueprint')
      toast.error(err.message || 'Failed to save blueprint')
    } finally {
      setIsSaving(false)
    }
  }

  // Fetch topic content from server
  const fetchTopicContent = async (topic: Topic) => {
    if (!topic.id) return;
    setTopicLoading(true);
    setTopicError(null);
    setTopicContent("");
    setSelectedTopic(topic);
    try {
      const response = await blueprintApi.getBlueprintTopicContent(topic.id);
      // Use content_details from the backend and join all text chunks
      if (response.content_details && response.content_details.length > 0) {
        setTopicContent(response.content_details.map((c: any) => c.text).join('\n\n'));
      } else {
        setTopicContent("No content found for this topic.");
      }
    } catch (err: any) {
      setTopicError(err.message || "Failed to load topic content.");
      setTopicContent("");
    } finally {
      setTopicLoading(false);
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription>
              Select a document to view or generate its learning blueprint
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg p-4 bg-purple-600/10 border border-purple-600/30">
              <h3 className="font-medium text-purple-500 mb-2">What is a Blueprint?</h3>
              <p className="text-sm text-neutral-300">
                A Blueprint helps Cognimosity understand which topics or areas you want to prioritize.
                This will influence how content is analyzed, summarized, and presented across all features.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription>
              {blueprint ? "Generating new blueprint..." : "Loading blueprint..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadBlueprint} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (showUpload) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Upload or Enter Blueprint
            </CardTitle>
            <CardDescription>
              No blueprint found for this document. Upload a file or enter blueprint text manually.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Upload Blueprint File:</label>
              <input type="file" accept=".txt,.pdf,.docx" onChange={handleFileChange} className="block w-full text-sm" />
              {selectedFile && <div className="text-xs text-neutral-400">Selected: {selectedFile.name}</div>}
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Or Enter Blueprint Text:</label>
              <Textarea
                value={manualBlueprint}
                onChange={e => setManualBlueprint(e.target.value)}
                placeholder="Paste or type your blueprint here..."
                className="min-h-[80px] bg-neutral-900 border-neutral-700"
              />
            </div>
            <Button
              onClick={handleUploadBlueprint}
              className="w-full"
              disabled={uploading || (!selectedFile && !manualBlueprint.trim())}
            >
              {uploading ? 'Uploading...' : 'Submit Blueprint'}
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Learning Blueprint
          </CardTitle>
          <CardDescription>
            AI-generated learning blueprint for your document content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Focus Areas Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Focus Areas (Optional):</label>
            <Textarea
              value={focusAreas}
              onChange={(e) => setFocusAreas(e.target.value)}
              placeholder="Specify topics or areas you want to emphasize (e.g., machine learning, data science, algorithms)..."
              className="min-h-[60px] bg-neutral-900 border-neutral-700"
            />
          </div>

          {/* Topics Overview */}
          {topics.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Key Topics Identified:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {topics.map((topic, index) => (
                  <button
                    key={index}
                    type="button"
                    className={`flex items-center justify-between p-2 rounded transition-colors w-full text-left ${selectedTopic?.title === topic.title ? 'bg-purple-800/60 border border-purple-500' : 'bg-neutral-800 hover:bg-neutral-700'}`}
                    onClick={() => fetchTopicContent(topic)}
                  >
                    <span className="text-sm">{topic.title}</span>
                    <span className="text-xs text-purple-400 font-medium">{topic.weightage.toFixed(0)}%</span>
                  </button>
                ))}
              </div>
              {selectedTopic && (
                <div className="mt-4 p-3 rounded bg-neutral-900 border border-purple-700">
                  <h4 className="font-semibold text-purple-400 mb-1">{selectedTopic.title}</h4>
                  <div className="text-sm text-neutral-200 whitespace-pre-line">
                    {topicLoading ? (
                      <span>Loading...</span>
                    ) : topicError ? (
                      <span className="text-red-400">{topicError}</span>
                    ) : (
                      topicContent || 'No additional content for this topic.'
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Blueprint Content */}
          {blueprint && (
            <div className="border rounded-lg p-4 bg-neutral-900/50 space-y-2">
              <label className="text-sm font-medium">Edit Blueprint:</label>
              <Textarea
                value={editedBlueprint}
                onChange={e => setEditedBlueprint(e.target.value)}
                className="min-h-[120px] bg-neutral-900 border-neutral-700"
              />
            </div>
          )}

          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={handleSaveBlueprint}
              variant="outline"
              className="flex-1"
              disabled={!editedBlueprint || isSaving || isLoading || isGenerating}
            >
              {isSaving ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Save
                </>
              )}
            </Button>
            <Button
              onClick={loadBlueprint}
              variant="outline"
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload
            </Button>
            <Button
              onClick={generateNewBlueprint}
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate New"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}