"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { cn } from '@/lib/utils'

interface MarkdownProps {
  content: string
  className?: string
}

export function Markdown({ content, className }: MarkdownProps) {
  return (
    <ReactMarkdown
      className={cn(
        "prose prose-invert prose-sm max-w-none",
        "prose-headings:text-neutral-100 prose-headings:font-semibold",
        "prose-h1:text-xl prose-h1:mb-4 prose-h1:mt-6",
        "prose-h2:text-lg prose-h2:mb-3 prose-h2:mt-5",
        "prose-h3:text-base prose-h3:mb-2 prose-h3:mt-4",
        "prose-p:text-neutral-200 prose-p:leading-relaxed prose-p:mb-3",
        "prose-ul:text-neutral-200 prose-ul:mb-3",
        "prose-ol:text-neutral-200 prose-ol:mb-3",
        "prose-li:text-neutral-200 prose-li:mb-1",
        "prose-strong:text-neutral-100 prose-strong:font-semibold",
        "prose-em:text-neutral-300 prose-em:italic",
        "prose-code:text-purple-400 prose-code:bg-neutral-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm",
        "prose-pre:bg-neutral-900 prose-pre:border prose-pre:border-neutral-700 prose-pre:rounded-lg prose-pre:p-4",
        "prose-blockquote:border-l-4 prose-blockquote:border-purple-500 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-neutral-300",
        "prose-a:text-purple-400 prose-a:underline prose-a:decoration-purple-400/50 hover:prose-a:decoration-purple-400",
        "prose-table:border-collapse prose-table:border prose-table:border-neutral-700",
        "prose-th:border prose-th:border-neutral-700 prose-th:bg-neutral-800 prose-th:p-2 prose-th:text-left prose-th:font-semibold",
        "prose-td:border prose-td:border-neutral-700 prose-td:p-2",
        className
      )}
      remarkPlugins={[remarkGfm]}
      components={{
        // Custom components for better styling
        h1: ({ children }) => (
          <h1 className="text-xl font-semibold text-neutral-100 mb-4 mt-6 first:mt-0">
            {children}
          </h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-lg font-semibold text-neutral-100 mb-3 mt-5 first:mt-0">
            {children}
          </h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-base font-semibold text-neutral-100 mb-2 mt-4 first:mt-0">
            {children}
          </h3>
        ),
        p: ({ children }) => (
          <p className="text-neutral-200 leading-relaxed mb-3 last:mb-0">
            {children}
          </p>
        ),
        ul: ({ children }) => (
          <ul className="text-neutral-200 mb-3 pl-6 space-y-1">
            {children}
          </ul>
        ),
        ol: ({ children }) => (
          <ol className="text-neutral-200 mb-3 pl-6 space-y-1">
            {children}
          </ol>
        ),
        li: ({ children }) => (
          <li className="text-neutral-200">
            {children}
          </li>
        ),
        strong: ({ children }) => (
          <strong className="text-neutral-100 font-semibold">
            {children}
          </strong>
        ),
        em: ({ children }) => (
          <em className="text-neutral-300 italic">
            {children}
          </em>
        ),
        code: ({ children, className }) => {
          const isInline = !className?.includes('language-')
          if (isInline) {
            return (
              <code className="text-purple-400 bg-neutral-800 px-1 py-0.5 rounded text-sm">
                {children}
              </code>
            )
          }
          return (
            <code className={className}>
              {children}
            </code>
          )
        },
        pre: ({ children }) => (
          <pre className="bg-neutral-900 border border-neutral-700 rounded-lg p-4 overflow-x-auto mb-3">
            {children}
          </pre>
        ),
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-purple-500 pl-4 italic text-neutral-300 mb-3">
            {children}
          </blockquote>
        ),
        a: ({ children, href }) => (
          <a 
            href={href} 
            className="text-purple-400 underline decoration-purple-400/50 hover:decoration-purple-400 transition-colors"
            target="_blank"
            rel="noopener noreferrer"
          >
            {children}
          </a>
        ),
        table: ({ children }) => (
          <div className="overflow-x-auto mb-3">
            <table className="border-collapse border border-neutral-700 w-full">
              {children}
            </table>
          </div>
        ),
        th: ({ children }) => (
          <th className="border border-neutral-700 bg-neutral-800 p-2 text-left font-semibold text-neutral-100">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="border border-neutral-700 p-2 text-neutral-200">
            {children}
          </td>
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  )
}
