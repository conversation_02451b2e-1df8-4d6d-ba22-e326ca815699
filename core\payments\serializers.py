from rest_framework import serializers
from .models import Payment, Subscription, SubscriptionPlan

class SubscriptionPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubscriptionPlan
        fields = ['id', 'name', 'description', 'price', 'duration_days', 
                 'chat_limit', 'file_upload_limit', 'is_active']

class SubscriptionSerializer(serializers.ModelSerializer):
    plan = SubscriptionPlanSerializer(read_only=True)
    plan_id = serializers.PrimaryKeyRelatedField(
        queryset=SubscriptionPlan.objects.filter(is_active=True),
        source='plan',
        write_only=True
    )
    is_active = serializers.SerializerMethodField()

    class Meta:
        model = Subscription
        fields = ['id', 'plan', 'plan_id', 'start_date', 'end_date', 
                 'status', 'auto_renew', 'is_active']
        read_only_fields = ['id', 'start_date', 'end_date', 'status']

    def get_is_active(self, obj):
        return obj.is_active()

class PaymentSerializer(serializers.ModelSerializer):
    subscription = SubscriptionSerializer(read_only=True)
    receipt_url = serializers.URLField(read_only=True)

    class Meta:
        model = Payment
        fields = ['id', 'amount', 'razorpay_order_id', 'status', 
                 'payment_method', 'receipt_url', 'created_at', 'subscription']
        read_only_fields = ['id', 'razorpay_order_id', 'status', 
                          'payment_method', 'receipt_url', 'created_at']

class PaymentCreateSerializer(serializers.Serializer):
    amount = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=1)
    plan_id = serializers.PrimaryKeyRelatedField(
        queryset=SubscriptionPlan.objects.filter(is_active=True),
        required=False
    )

    def validate(self, data):
        if 'plan_id' in data:
            plan = data['plan_id']
            if data['amount'] != plan.price:
                raise serializers.ValidationError(
                    "Payment amount must match the plan price"
                )
        return data

class PaymentVerifySerializer(serializers.Serializer):
    razorpay_order_id = serializers.CharField()
    razorpay_payment_id = serializers.CharField()
    razorpay_signature = serializers.CharField() 