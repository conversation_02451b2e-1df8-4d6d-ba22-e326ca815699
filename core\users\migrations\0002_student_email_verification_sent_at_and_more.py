# Generated by Django 4.2.21 on 2025-07-18 05:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0013_documentlearningtime"),
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="student",
            name="email_verification_sent_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="email_verification_token",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="student",
            name="is_email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="student",
            name="otp",
            field=models.CharField(blank=True, max_length=6, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="student",
            name="otp_created_at",
            field=models.DateTime<PERSON>ield(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="student",
            name="email",
            field=models.EmailField(max_length=254, unique=True),
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_paid", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DocumentTimeTracking",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        help_text="Username of the student", max_length=150
                    ),
                ),
                (
                    "file_name",
                    models.CharField(
                        help_text="Name of the document file", max_length=255
                    ),
                ),
                (
                    "total_time_seconds",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total time spent on 2nd webpage in seconds",
                    ),
                ),
                (
                    "number_of_sessions",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times the 2nd webpage was accessed",
                    ),
                ),
                (
                    "number_of_quizzes",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of quizzes taken for this file"
                    ),
                ),
                (
                    "last_accessed",
                    models.DateTimeField(
                        auto_now=True,
                        help_text="Last time the 2nd webpage was accessed (Indian timezone)",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="When this record was first created",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document Time Tracking",
                "verbose_name_plural": "Document Time Tracking",
                "ordering": ["-last_accessed"],
                "indexes": [
                    models.Index(
                        fields=["username", "file_name"],
                        name="users_docum_usernam_c66d59_idx",
                    ),
                    models.Index(
                        fields=["last_accessed"], name="users_docum_last_ac_db8f4d_idx"
                    ),
                    models.Index(
                        fields=["total_time_seconds"],
                        name="users_docum_total_t_a849da_idx",
                    ),
                ],
                "unique_together": {("username", "file_name")},
            },
        ),
        migrations.CreateModel(
            name="UserUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(default=django.utils.timezone.now)),
                ("chat_count", models.IntegerField(default=0)),
                ("file_upload_count", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["user", "date"], name="users_useru_user_id_de9527_idx"
                    ),
                    models.Index(fields=["date"], name="users_useru_date_537fab_idx"),
                ],
                "unique_together": {("user", "date")},
            },
        ),
        migrations.CreateModel(
            name="StudentPerformance",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "quiz_score",
                    models.DecimalField(
                        decimal_places=2,
                        default=0.0,
                        help_text="Score achieved on the quiz (percentage)",
                        max_digits=5,
                    ),
                ),
                (
                    "time_taken",
                    models.PositiveIntegerField(
                        help_text="Time taken to complete the quiz (in seconds)"
                    ),
                ),
                (
                    "remarks",
                    models.TextField(
                        blank=True,
                        help_text="Feedback or comments on student performance",
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="student_performances",
                        to="documents.document",
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performances",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Student Performance",
                "verbose_name_plural": "Student Performances",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["student", "document"],
                        name="users_stude_student_4c8d6a_idx",
                    ),
                    models.Index(
                        fields=["quiz_score"], name="users_stude_quiz_sc_4f6341_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="users_stude_created_f685e1_idx"
                    ),
                ],
            },
        ),
    ]
