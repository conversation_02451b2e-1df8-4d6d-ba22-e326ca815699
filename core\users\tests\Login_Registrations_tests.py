from django.test import TestCase, Client, override_settings
from django.urls import reverse
from rest_framework import status
from ..models import Student
from django.utils import timezone
from unittest.mock import patch
import json

@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        }
    }
)
class AuthenticationTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.register_url = reverse('register')  # Changed from 'student-registration' to 'register
        self.otp_verify_url = reverse('verify-otp')
        self.google_signin_url = reverse('google-signin')
        
        # Test user data
        self.user_data = {
            'email': '<EMAIL>',
            'password': 'testpass123',
            'confirm_password': 'testpass123',  # Added confirm_password
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User'
        }

    def test_successful_registration(self):
        """Test successful user registration"""
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('message', response.data)
        self.assertIn('email', response.data)
        self.assertTrue(Student.objects.filter(email=self.user_data['email']).exists())

    def test_registration_with_existing_email(self):
        """Test registration with an email that already exists"""
        # Create a user first
        Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password']
        )
        
        # Try to register with the same email
        response = self.client.post(self.register_url, self.user_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)

    def test_registration_with_invalid_data(self):
        """Test registration with invalid data"""
        invalid_data = {
            'email': 'invalid-email',
            'password': 'short',
            'username': '',
        }
        response = self.client.post(self.register_url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_otp_verification(self):
        """Test OTP verification process"""
        # Create an unverified user with OTP
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=False
        )
        otp = user.generate_otp()
        
        # Test with correct OTP
        response = self.client.post(self.otp_verify_url, {
            'email': self.user_data['email'],
            'otp': otp
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Refresh user from database
        user.refresh_from_db()
        self.assertTrue(user.is_active)
        self.assertTrue(user.is_email_verified)

    def test_otp_verification_with_invalid_otp(self):
        """Test OTP verification with invalid OTP"""
        # Create an unverified user
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=False
        )
        user.generate_otp()
        
        # Test with incorrect OTP
        response = self.client.post(self.otp_verify_url, {
            'email': self.user_data['email'],
            'otp': '000000'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    # @patch('google.oauth2.id_token.verify_oauth2_token')
    # def test_google_signin(self, mock_verify_token):
    #     """Test Google Sign In"""
    #     mock_verify_token.return_value = {
    #         'email': '<EMAIL>',
    #         'given_name': 'Google',
    #         'family_name': 'User'
    #     }
        
    #     response = self.client.post(self.google_signin_url, {
    #         'token': 'fake_google_token'
    #     })
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertIn('access_token', response.data)
    #     self.assertIn('refresh_token', response.data)
    #     self.assertTrue(Student.objects.filter(email='<EMAIL>').exists())

    def test_expired_otp(self):
        """Test OTP verification with expired OTP"""
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=False
        )
        otp = user.generate_otp()
        
        # Set OTP creation time to 11 minutes ago (beyond 10-minute validity)
        user.otp_created_at = timezone.now() - timezone.timedelta(minutes=11)
        user.save()
        
        response = self.client.post(self.otp_verify_url, {
            'email': self.user_data['email'],
            'otp': otp
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_login_with_token(self):
        """Test login with token authentication"""
        # Create an active, verified user
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=True,
            is_email_verified=True
        )
        
        # Try to login
        login_url = reverse('api_token_auth')
        response = self.client.post(login_url, {
            'username': self.user_data['email'],  # Using email as username
            'password': self.user_data['password']
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data)

    def test_login_unverified_user(self):
        """Test login attempt with unverified user"""
        # Create an unverified user
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=False,
            is_email_verified=False
        )
        
        # Try to login
        login_url = reverse('api_token_auth')
        response = self.client.post(login_url, {
            'username': self.user_data['email'],
            'password': self.user_data['password']
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_login_with_invalid_credentials(self):
        """Test login attempt with invalid credentials"""
        login_url = reverse('api_token_auth')
        response = self.client.post(login_url, {
            'username': self.user_data['email'],
            'password': 'wrongpassword'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_protected_endpoint_access(self):
        """Test accessing protected endpoint with and without token"""
        # Create and verify user
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=True,
            is_email_verified=True
        )
        
        # Get auth token
        login_url = reverse('api_token_auth')
        response = self.client.post(login_url, {
            'username': self.user_data['email'],
            'password': self.user_data['password']
        })
        token = response.data['token']
        
        # Try accessing protected endpoint without token
        usage_url = reverse('get-usage')
        response = self.client.get(usage_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Try accessing protected endpoint with token
        response = self.client.get(usage_url, HTTP_AUTHORIZATION=f'Token {token}')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_email_verification_token_expiry(self):
        """Test email verification token expiry"""
        # Create user and generate verification token
        user = Student.objects.create_user(
            email=self.user_data['email'],
            username=self.user_data['username'],
            password=self.user_data['password'],
            is_active=False
        )
        token = user.generate_verification_token()
        
        # Set token creation time to yesterday
        user.email_verification_sent_at = timezone.now() - timezone.timedelta(days=1)
        user.save()
        
        # Try to verify email
        verify_url = reverse('verify-email', kwargs={'token': token})
        response = self.client.post(verify_url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
