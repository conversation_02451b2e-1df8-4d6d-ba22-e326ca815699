from django.db import models
from django.conf import settings
from users.models import Student

class DocumentGroup(models.Model):
    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='document_groups')
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['-created_at']

class Document(models.Model):
    PROCESSING_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='documents')
    group = models.ForeignKey(DocumentGroup, on_delete=models.SET_NULL, null=True, blank=True, related_name='documents')
    title = models.CharField(max_length=255)
    file = models.FileField(upload_to='documents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='pending')
    error_message = models.TextField(null=True, blank=True)
    blueprint = models.TextField(null=True, blank=True, help_text="Blueprint for the document")

    def __str__(self):
        return f"{self.title} ({self.get_processing_status_display()})"

class DocumentEmbedding(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='embeddings')
    text_chunk = models.TextField(help_text="The text segment this embedding represents")
    embedding = models.JSONField(help_text="Vector embedding of the text chunk")
    chunk_number = models.IntegerField(help_text="Order of this chunk in the document")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['chunk_number']
        indexes = [
            models.Index(fields=['document', 'chunk_number']),
        ]

    def __str__(self):
        return f"{self.document.title} - Chunk {self.chunk_number}"

class Flashcard(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    front = models.TextField()
    back = models.TextField()

class Flowchart(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    mermaid_code = models.TextField(help_text="Store Mermaid.js flowchart code")

class Quiz(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    question = models.TextField()
    answer = models.TextField()

class QuizAttempt(models.Model):
    """
    Model to track a user's quiz attempt for a specific document.
    Each attempt can have multiple answers (one per question).
    """
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='quiz_attempts')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='quiz_attempts')
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)

    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['user', 'document']),
            models.Index(fields=['start_time']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.document.title} - {self.start_time.strftime('%Y-%m-%d %H:%M')}"

class QuizAnswer(models.Model):
    """
    Model to store a user's answer to a specific quiz question.
    Includes feedback on the answer.
    """
    quiz_attempt = models.ForeignKey(QuizAttempt, on_delete=models.CASCADE, related_name='answers')
    quiz_question = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='user_answers')
    user_answer = models.TextField()
    is_correct = models.BooleanField(default=False)
    feedback = models.TextField(blank=True, null=True, help_text="AI-generated feedback on the answer")

    class Meta:
        ordering = ['quiz_question_id']
        indexes = [
            models.Index(fields=['quiz_attempt', 'quiz_question']),
            models.Index(fields=['is_correct']),
        ]

    def __str__(self):
        return f"Answer for {self.quiz_question.question[:30]}..."

class Chapter(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    content = models.TextField()
    order = models.PositiveIntegerField(default=0)
    subsections = models.JSONField(default=list, blank=True)

class BlueprintTopics(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='topics')
    title = models.CharField(max_length=255, help_text="Title of the topic")
    weightage = models.DecimalField(max_digits=5, decimal_places=2, help_text="Weightage of the topic in percentile")
    content = models.ManyToManyField(DocumentEmbedding, related_name='topics', help_text="Relevant document embeddings for this topic")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Blueprint Topic"
        verbose_name_plural = "Blueprint Topics"
        ordering = ['-weightage']

    def __str__(self):
        return f"{self.title} ({self.weightage}%)"


class DocumentLearningTime(models.Model):
    """
    Model to store predicted learning times for documents.
    Uses Gemini AI to analyze content and predict learning duration.
    """
    DIFFICULTY_CHOICES = [
        (1, 'Very Easy'),
        (2, 'Easy'),
        (3, 'Medium'),
        (4, 'Hard'),
        (5, 'Very Hard')
    ]

    CONCEPT_DENSITY_CHOICES = [
        (1, 'Low - Basic concepts'),
        (2, 'Low-Medium - Some formulas'),
        (3, 'Medium - Moderate complexity'),
        (4, 'Medium-High - Formula heavy'),
        (5, 'High - Abstract/Problem-solving intensive')
    ]

    document = models.OneToOneField(Document, on_delete=models.CASCADE, related_name='learning_time')
    predicted_time_seconds = models.PositiveIntegerField(
        help_text="Predicted learning time in seconds for age group 16-22"
    )
    topic_difficulty = models.IntegerField(
        choices=DIFFICULTY_CHOICES,
        help_text="Topic difficulty level from 1 (very easy) to 5 (very hard)"
    )
    content_length_words = models.PositiveIntegerField(
        help_text="Number of words in the document content"
    )
    concept_density = models.IntegerField(
        choices=CONCEPT_DENSITY_CHOICES,
        help_text="Concept density level based on formulas, abstractions, and problem-solving requirements"
    )
    analysis_factors = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional factors considered by Gemini AI in JSON format"
    )
    gemini_reasoning = models.TextField(
        blank=True,
        help_text="Gemini AI's reasoning for the time prediction"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Document Learning Time"
        verbose_name_plural = "Document Learning Times"
        ordering = ['-created_at']

    def __str__(self):
        hours = self.predicted_time_seconds // 3600
        minutes = (self.predicted_time_seconds % 3600) // 60
        seconds = self.predicted_time_seconds % 60

        if hours > 0:
            time_str = f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            time_str = f"{minutes}m {seconds}s"
        else:
            time_str = f"{seconds}s"

        return f"{self.document.title} - Predicted: {time_str} (Difficulty: {self.get_topic_difficulty_display()})"

    def get_time_range_display(self):
        """
        Return a user-friendly time range display.
        Provides a range of ±20% around the predicted time.
        """
        min_time = int(self.predicted_time_seconds * 0.8)
        max_time = int(self.predicted_time_seconds * 1.2)

        def format_time(seconds):
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            if hours > 0:
                return f"{hours}h {minutes}m"
            elif minutes > 0:
                return f"{minutes}m"
            else:
                return f"{seconds}s"

        return f"{format_time(min_time)} - {format_time(max_time)}"