from django.contrib import admin

# Register your models here.

from .models import Document, DocumentEmbedding, Flashcard, Flowchart, Quiz, BlueprintTopics, DocumentGroup, DocumentLearningTime


@admin.register(DocumentLearningTime)
class DocumentLearningTimeAdmin(admin.ModelAdmin):
    list_display = ['document', 'predicted_time_seconds', 'topic_difficulty', 'concept_density', 'created_at']
    list_filter = ['topic_difficulty', 'concept_density', 'created_at']
    search_fields = ['document__title', 'gemini_reasoning']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Document Information', {
            'fields': ('document',)
        }),
        ('Prediction Details', {
            'fields': ('predicted_time_seconds', 'topic_difficulty', 'concept_density', 'content_length_words')
        }),
        ('Analysis', {
            'fields': ('analysis_factors', 'gemini_reasoning'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('document')


admin.site.register(Document)
admin.site.register(DocumentEmbedding)
admin.site.register(Flashcard)
admin.site.register(Flowchart)
admin.site.register(Quiz)
admin.site.register(BlueprintTopics)
admin.site.register(DocumentGroup)
