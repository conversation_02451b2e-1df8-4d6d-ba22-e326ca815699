"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Award, ArrowLeft, Send } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"

type QuizState = "evaluation" | "results"

export function QuizzesInterface() {
  const [quizState, setQuizState] = useState<QuizState>("evaluation")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setQuizState("results")
  }

  const resetQuiz = () => {
    setQuizState("evaluation")
  }

  return (
    <div className="flex flex-col h-full bg-black">
      <div className="p-4 border-b border-neutral-800">
        <h2 className="text-xl font-medium text-center">
          {quizState === "evaluation"
            ? "Evaluation for Chapter: Introduction to Machine Learning"
            : "Evaluation Results for Chapter: Introduction to Machine Learning"}
        </h2>
      </div>

      <ScrollArea className="flex-1">
        <AnimatePresence mode="wait">
          {quizState === "evaluation" ? (
            <motion.div
              key="evaluation"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-6 p-4"
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                {[1, 2, 3, 4, 5].map((index) => (
                  <Card key={index} className="bg-neutral-800 border-neutral-700">
                    <CardHeader>
                      <CardTitle className="text-base flex justify-between">
                        <span>Question {index}</span>
                        <span className="text-purple-400">[10 marks]</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Textarea
                        placeholder="Type your answer here..."
                        className="min-h-[120px] bg-neutral-900 border-neutral-700 focus-visible:ring-purple-500"
                      />
                    </CardContent>
                  </Card>
                ))}
                <div className="flex justify-end">
                  <Button type="submit" className="bg-purple-600 hover:bg-purple-700">
                    <Send className="h-4 w-4 mr-2" />
                    Submit Answers
                  </Button>
                </div>
              </form>
            </motion.div>
          ) : (
            <motion.div
              key="results"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-6 p-4"
            >
              <Card className="bg-purple-600/20 border-purple-600/30">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-center flex-col">
                    <Award className="h-12 w-12 text-purple-500 mb-2" />
                    <h3 className="text-xl font-bold">Total Score: 35/50</h3>
                    <p className="text-lg">70%</p>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-6">
                {[1, 2, 3, 4, 5].map((index) => (
                  <Card key={index} className="bg-neutral-800 border-neutral-700">
                    <CardHeader>
                      <CardTitle className="text-base flex justify-between">
                        <span>Question {index}</span>
                        <span className={index % 2 === 0 ? "text-green-400" : "text-yellow-400"}>
                          {index % 2 === 0 ? "8/10" : "7/10"}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Your Answer:</h4>
                        <div className="bg-neutral-900 p-3 rounded-md text-sm">Student answer would appear here...</div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-2">Feedback:</h4>
                        <div className="bg-neutral-900 p-3 rounded-md text-sm">
                          {index % 2 === 0
                            ? "Excellent answer! Very comprehensive."
                            : "Good attempt, but could include more details."}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="flex justify-center">
                <Button onClick={resetQuiz} className="bg-purple-600 hover:bg-purple-700">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ScrollArea>
    </div>
  )
} 