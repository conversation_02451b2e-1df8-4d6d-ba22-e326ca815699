# Generated by Django 4.2.21 on 2025-07-13 12:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0012_chapter'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentLearningTime',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('predicted_time_seconds', models.PositiveIntegerField(help_text='Predicted learning time in seconds for age group 16-22')),
                ('topic_difficulty', models.IntegerField(choices=[(1, 'Very Easy'), (2, 'Easy'), (3, 'Medium'), (4, 'Hard'), (5, 'Very Hard')], help_text='Topic difficulty level from 1 (very easy) to 5 (very hard)')),
                ('content_length_words', models.PositiveIntegerField(help_text='Number of words in the document content')),
                ('concept_density', models.IntegerField(choices=[(1, 'Low - Basic concepts'), (2, 'Low-Medium - Some formulas'), (3, 'Medium - Moderate complexity'), (4, 'Medium-High - Formula heavy'), (5, 'High - Abstract/Problem-solving intensive')], help_text='Concept density level based on formulas, abstractions, and problem-solving requirements')),
                ('analysis_factors', models.JSONField(blank=True, default=dict, help_text='Additional factors considered by Gemini AI in JSON format')),
                ('gemini_reasoning', models.TextField(blank=True, help_text="Gemini AI's reasoning for the time prediction")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='learning_time', to='documents.document')),
            ],
            options={
                'verbose_name': 'Document Learning Time',
                'verbose_name_plural': 'Document Learning Times',
                'ordering': ['-created_at'],
            },
        ),
    ]
