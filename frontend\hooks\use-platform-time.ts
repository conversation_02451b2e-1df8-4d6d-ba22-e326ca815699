import { useEffect, useRef } from 'react';

interface UsePlatformTimeProps {
  documentId?: number;
  enabled?: boolean;
}

export function usePlatformTime({ documentId, enabled = true }: UsePlatformTimeProps = {}) {
  const sessionIdRef = useRef<number | null>(null);
  const startTimeRef = useRef<Date | null>(null);

  useEffect(() => {
    if (!enabled) return;

    const startSession = async () => {
      try {
        const token = localStorage.getItem('authToken');
        if (!token) return;

        const response = await fetch('/api/users/platform-time/start/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${token}`,
          },
          body: JSON.stringify({
            document_id: documentId,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          sessionIdRef.current = data.session_id;
          startTimeRef.current = new Date();
        }
      } catch (error) {
        console.error('Error starting platform session:', error);
      }
    };

    const endSession = async () => {
      if (!sessionIdRef.current) return;

      try {
        const token = localStorage.getItem('authToken');
        if (!token) return;

        await fetch('/api/users/platform-time/end/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${token}`,
          },
          body: JSON.stringify({
            session_id: sessionIdRef.current,
          }),
        });

        sessionIdRef.current = null;
        startTimeRef.current = null;
      } catch (error) {
        console.error('Error ending platform session:', error);
      }
    };

    // Start session when component mounts
    startSession();

    // End session when page unloads
    const handleBeforeUnload = () => {
      endSession();
    };

    // End session when page becomes hidden (user switches tabs)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        endSession();
      } else {
        startSession();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      endSession();
    };
  }, [documentId, enabled]);

  return {
    isTracking: sessionIdRef.current !== null,
  };
}
