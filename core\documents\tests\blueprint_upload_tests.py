import os
from io import BytesIO
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.conf import settings
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from documents.models import Document, Student
from rest_framework.authtoken.models import Token
from django.test import override_settings
from django.contrib.auth import get_user_model




import fitz  # for PDF
from docx import Document as DocxDocument



@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'unique-snowflake',
        }
    }
)

class UploadBlueprintTests(APITestCase):
    def setUp(self):

        self.client = APIClient()

        # Create a test student
        self.User = get_user_model()

        # Create test user
        self.user = self.User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
            is_email_verified=True
        )

        self.client.force_authenticate(user=self.user)


        self.document = Document.objects.create(
            user=self.user,
            title="Test Document",
            file=SimpleUploadedFile("dummy.txt", b"Test"),
        )
        self.url = reverse('document-blueprint', args=[self.document.id])

        # Create sample test files in memory
        self.test_files = {
            'pdf': self._create_pdf_file(),
            'docx': self._create_docx_file(),
            'txt': SimpleUploadedFile("sample.txt", b"This is a sample TXT file.", content_type='text/plain'),
        }

        #self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        #self.client.login(username='student1', password='password')  # Make sure you set a password for the student user



    def _create_pdf_file(self):
        buffer = BytesIO()
        doc = fitz.open()
        page = doc.new_page()
        page.insert_text((72, 72), "This is a sample PDF file.")
        doc.save(buffer)
        doc.close()
        buffer.seek(0)
        return SimpleUploadedFile("sample.pdf", buffer.read(), content_type="application/pdf")

    def _create_docx_file(self):
        buffer = BytesIO()
        doc = DocxDocument()
        doc.add_paragraph("This is a sample DOCX file.")
        doc.save(buffer)
        buffer.seek(0)
        return SimpleUploadedFile("sample.docx", buffer.read(), content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document")

    def test_upload_pdf_blueprint(self):
        response = self.client.post(self.url, {
            'file': self.test_files['pdf']
        }, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.document.refresh_from_db()
        self.assertTrue(self.document.blueprint)

    def test_upload_docx_blueprint(self):
        response = self.client.post(self.url, {
            'file': self.test_files['docx']
        }, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_upload_txt_blueprint(self):
        response = self.client.post(self.url, {
            'file': self.test_files['txt']
        }, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_upload_unsupported_file(self):
        unsupported_file = SimpleUploadedFile("test.xyz", b"dummy content", content_type='application/octet-stream')
        response = self.client.post(self.url, {
            'file': unsupported_file
        }, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn('Unsupported file type', response.data['error'])

    def test_missing_file(self):
        response = self.client.post(self.url, {}, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_get_blueprint_success(self):
        self.document.blueprint = "Some cleaned text."
        self.document.save()
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['blueprint'], "Some cleaned text.")

    def test_get_blueprint_not_found(self):
        # Create a URL with a non-existent document ID
        non_existent_url = reverse('document-blueprint', args=[9999])
        response = self.client.get(non_existent_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
