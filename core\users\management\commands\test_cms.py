"""
Django management command to test the CMS (Concept Mastery System) functionality.
Usage: python manage.py test_cms
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from documents.models import Document, Quiz, QuizAttempt, DocumentLearningTime
from users.models import CMSScore, DocumentTimeTracking
from users.cms_utils import CMSCalculator, RecommendationEngine
from decimal import Decimal
import json

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the CMS (Concept Mastery System) functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--demo',
            action='store_true',
            help='Run demonstration scenarios',
        )
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data for CMS testing',
        )

    def handle(self, *args, **options):
        if options['demo']:
            self.run_demo()
        elif options['create_test_data']:
            self.create_test_data()
        else:
            self.stdout.write(
                self.style.WARNING(
                    'Please specify --demo or --create-test-data'
                )
            )

    def run_demo(self):
        """Run CMS demonstration scenarios."""
        self.stdout.write(
            self.style.SUCCESS('=== CMS (Concept Mastery System) Demo ===\n')
        )
        
        scenarios = [
            {
                'name': 'Fast Learner - High Score',
                'quiz_score': 90,
                'time_spent': 1200,  # 20 minutes
                'average_time': 1800,  # 30 minutes
                'description': 'Student who learns quickly and scores well'
            },
            {
                'name': 'Thorough Learner - High Score',
                'quiz_score': 85,
                'time_spent': 3000,  # 50 minutes
                'average_time': 1800,  # 30 minutes
                'description': 'Student who takes time but understands well'
            },
            {
                'name': 'Struggling Student',
                'quiz_score': 35,
                'time_spent': 2700,  # 45 minutes
                'average_time': 1800,  # 30 minutes
                'description': 'Student having difficulty with the material'
            },
            {
                'name': 'Rushed Student',
                'quiz_score': 45,
                'time_spent': 900,   # 15 minutes
                'average_time': 1800,  # 30 minutes
                'description': 'Student who rushes through without proper understanding'
            },
            {
                'name': 'Average Performer',
                'quiz_score': 65,
                'time_spent': 1800,  # 30 minutes (exactly average)
                'average_time': 1800,  # 30 minutes
                'description': 'Student with average performance and time'
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            self.stdout.write(f"\n--- Scenario {i}: {scenario['name']} ---")
            self.stdout.write(f"Description: {scenario['description']}")
            self.stdout.write(f"Quiz Score: {scenario['quiz_score']}%")
            self.stdout.write(f"Time Spent: {scenario['time_spent']/60:.1f} minutes")
            self.stdout.write(f"Average Time: {scenario['average_time']/60:.1f} minutes")
            
            # Calculate time ratio and categories
            time_ratio = scenario['time_spent'] / scenario['average_time']
            time_category = CMSCalculator.categorize_time_efficiency(time_ratio)
            quiz_category = CMSCalculator.categorize_quiz_performance(scenario['quiz_score'])
            
            # Calculate components
            quiz_component = (scenario['quiz_score'] / 100) * 70
            time_points = CMSCalculator.calculate_time_component_points(time_category, quiz_category)
            cms_score = quiz_component + time_points
            cms_score = max(0, min(100, cms_score))
            user_zone = CMSCalculator.determine_user_zone(cms_score)
            
            self.stdout.write(f"Time Category: {time_category} (ratio: {time_ratio:.2f})")
            self.stdout.write(f"Quiz Category: {quiz_category}")
            self.stdout.write(f"Quiz Component: {quiz_component:.1f}/70 points")
            self.stdout.write(f"Time Efficiency Points: {time_points:+d}")
            self.stdout.write(
                self.style.SUCCESS(f"Final CMS Score: {cms_score:.1f}/100")
            )
            self.stdout.write(
                self.style.WARNING(f"User Zone: {user_zone.upper()}")
            )
            
            # Get recommendation
            recommendation = RecommendationEngine.get_recommendation(user_zone)
            self.stdout.write(
                f"Recommendation: {recommendation['icon']} {recommendation['message']}"
            )
            
            # Show scoring matrix explanation
            self.stdout.write("\nScoring Matrix Applied:")
            self.stdout.write(f"  - Quiz Score Component: {scenario['quiz_score']}% × 0.7 = {quiz_component:.1f} points")
            self.stdout.write(f"  - Time Efficiency: {time_category} time + {quiz_category} quiz = {time_points:+d} points")
            self.stdout.write(f"  - Total: {quiz_component:.1f} + {time_points} = {cms_score:.1f} points")

    def create_test_data(self):
        """Create test data for CMS testing."""
        self.stdout.write(
            self.style.SUCCESS('Creating test data for CMS system...')
        )
        
        # Create test user
        user, created = User.objects.get_or_create(
            username='cms_test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'CMS',
                'last_name': 'Test'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            self.stdout.write(f"Created test user: {user.username}")
        else:
            self.stdout.write(f"Using existing test user: {user.username}")
        
        # Create test document
        document, created = Document.objects.get_or_create(
            title='CMS Test Document',
            defaults={
                'user': user,
                'processing_status': 'completed'
            }
        )
        
        if created:
            self.stdout.write(f"Created test document: {document.title}")
        else:
            self.stdout.write(f"Using existing test document: {document.title}")
        
        # Create document learning time
        learning_time, created = DocumentLearningTime.objects.get_or_create(
            document=document,
            defaults={
                'predicted_time_seconds': 1800,  # 30 minutes
                'topic_difficulty': 3,
                'content_length_words': 500,
                'concept_density': 3,
                'gemini_reasoning': 'Test prediction for CMS system'
            }
        )
        
        if created:
            self.stdout.write(f"Created learning time prediction: {learning_time.predicted_time_seconds} seconds")
        
        # Create time tracking
        time_tracking, created = DocumentTimeTracking.objects.get_or_create(
            username=user.username,
            file_name=document.title,
            defaults={
                'total_time_seconds': 1500,  # 25 minutes
                'number_of_sessions': 1,
                'number_of_quizzes': 0
            }
        )
        
        if created:
            self.stdout.write(f"Created time tracking: {time_tracking.total_time_seconds} seconds")
        
        # Create quiz questions
        quiz1, created = Quiz.objects.get_or_create(
            document=document,
            question='What is the main purpose of the CMS system?',
            defaults={'answer': 'Adaptive learning assessment'}
        )
        
        quiz2, created = Quiz.objects.get_or_create(
            document=document,
            question='How is the CMS score calculated?',
            defaults={'answer': 'Quiz performance and time efficiency'}
        )
        
        self.stdout.write("Created quiz questions")
        
        # Create a test quiz attempt
        quiz_attempt = QuizAttempt.objects.create(
            document=document,
            user=user,
            score=Decimal('75.0')
        )
        
        self.stdout.write(f"Created quiz attempt with score: {quiz_attempt.score}%")
        
        # Calculate and create CMS score
        try:
            cms_calculation_data = CMSCalculator.calculate_cms_score(quiz_attempt)
            
            cms_score = CMSScore.objects.create(
                student=user,
                document=document,
                quiz_attempt=quiz_attempt,
                quiz_score_percentage=cms_calculation_data['quiz_score_percentage'],
                quiz_score_component=cms_calculation_data['quiz_score_component'],
                quiz_performance_category=cms_calculation_data['quiz_performance_category'],
                time_spent_seconds=cms_calculation_data['time_spent_seconds'],
                average_time_seconds=cms_calculation_data['average_time_seconds'],
                time_ratio=cms_calculation_data['time_ratio'],
                time_category=cms_calculation_data['time_category'],
                time_component_points=cms_calculation_data['time_component_points'],
                cms_score=cms_calculation_data['cms_score'],
                user_zone=cms_calculation_data['user_zone']
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Created CMS score: {cms_score.cms_score} ({cms_score.user_zone})"
                )
            )
            
            # Generate recommendation
            recommendation_data = RecommendationEngine.generate_detailed_recommendation(cms_calculation_data)
            
            self.stdout.write("\nGenerated Recommendation:")
            self.stdout.write(f"Zone: {recommendation_data['user_zone']}")
            self.stdout.write(f"Message: {recommendation_data['recommendation']['message']}")
            self.stdout.write(f"Analysis: {recommendation_data['analysis']['overall_assessment']}")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error creating CMS score: {str(e)}")
            )
        
        self.stdout.write(
            self.style.SUCCESS('\nTest data creation completed!')
        )
        self.stdout.write(
            'You can now view the CMS scores in the Django admin interface.'
        )
