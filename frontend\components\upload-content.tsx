"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, File, X, Loader2 } from "lucide-react"
import { motion } from "framer-motion"
import { useTheme } from "@/components/theme-provider"

export function UploadContent() {
  const [isDragging, setIsDragging] = useState(false)
  const [files, setFiles] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { theme } = useTheme()
  const [isProcessing, setIsProcessing] = useState(false)
  const [isUploading, setIsUploading] = useState(false)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles((prev) => [...prev, ...newFiles])

      setIsUploading(true)
      setTimeout(() => {
        setIsUploading(false)
      }, 1500)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      setFiles((prev) => [...prev, ...newFiles])

      setIsUploading(true)
      setTimeout(() => {
        setIsUploading(false)
      }, 1500)
    }
  }

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const handleProcessFiles = () => {
    if (files.length > 0) {
      setIsProcessing(true)

      const fileData = files.map((file) => ({
        name: file.name,
        size: `${(file.size / (1024 * 1024)).toFixed(2)} MB`,
        type: file.type || file.name.split('.').pop()?.toLowerCase(),
        lastModified: file.lastModified,
      }))

      localStorage.setItem("uploadedFiles", JSON.stringify(fileData))

      const fileUrls = files.map((file) => URL.createObjectURL(file))
      localStorage.setItem("filePreviewUrls", JSON.stringify(fileUrls))

      setTimeout(() => {
        fileUrls.forEach(url => URL.revokeObjectURL(url))
      }, 5 * 60 * 1000)

      setTimeout(() => {
        window.location.href = "/process?type=upload"
      }, 2000)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-semibold mb-4">Upload Files</h2>

      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors mb-4 ${
          isDragging ? "border-purple-500 bg-purple-500/10" : theme === "light" ? "border-black" : "border-neutral-700"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" multiple />

        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: isDragging ? 1.05 : 1 }}
          className="flex flex-col items-center"
        >
          <div
            className={`p-3 rounded-full mb-3 ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}
          >
            {isUploading ? (
              <Loader2 className={`h-6 w-6 animate-spin ${theme === "light" ? "text-black" : "text-purple-500"}`} />
            ) : (
              <Upload className={`h-6 w-6 ${theme === "light" ? "text-black" : "text-purple-500"}`} />
            )}
          </div>
          <p className="text-neutral-300 mb-2">Drag and drop files here</p>
          <p className="text-neutral-500 text-sm mb-4">or</p>
          <Button onClick={triggerFileInput} className="bg-purple-600 hover:bg-purple-700" disabled={isUploading}>
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              "Browse Files"
            )}
          </Button>
          <p className="text-neutral-500 text-xs mt-4">Supported formats: PDF, PPT, DOC, TXT, MP3, WAV</p>
        </motion.div>
      </div>

      {files.length > 0 && (
        <div className="flex-1 overflow-auto">
          <h3 className="text-sm font-medium mb-2">Selected Files</h3>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={index}
                className={`flex items-center justify-between p-2 rounded ${
                  theme === "light" ? "bg-white border border-black" : "bg-neutral-800"
                }`}
              >
                <div className="flex items-center">
                  <File className={`h-4 w-4 mr-2 ${theme === "light" ? "text-black" : "text-purple-500"}`} />
                  <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                </div>
                <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => removeFile(index)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>

          <div className="mt-4">
            <Button
              className="bg-purple-600 hover:bg-purple-700 w-full"
              disabled={files.length === 0 || isProcessing}
              onClick={handleProcessFiles}
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <div>Processing...</div>
                </div>
              ) : (
                <>
                  Process {files.length} {files.length === 1 ? "File" : "Files"}
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
