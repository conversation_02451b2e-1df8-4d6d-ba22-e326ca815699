'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Clock, 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  BookOpen, 
  Timer,
  Play,
  Pause,
  Square,
  RefreshCw
} from 'lucide-react';
import { timeTrackingApi } from '@/lib/api';
import { useTimeTrackingContext } from '@/hooks/use-time-tracking-context';
import { toast } from '@/hooks/use-toast';

interface DocumentTimeStats {
  document: number;
  document_title: string;
  total_study_time_seconds: number;
  total_study_time_formatted: string;
  total_sessions: number;
  view_count: number;
  reopened_at_least_once: boolean;
  first_access: string;
  last_access: string;
  average_session_duration_seconds: number;
  average_session_duration_formatted: string;
}

interface UserOverview {
  total_study_time_seconds: number;
  total_study_time_formatted: string;
  total_sessions: number;
  total_documents: number;
  active_sessions: any[];
  document_stats: DocumentTimeStats[];
}

export const TimeTrackingDashboard: React.FC = () => {
  const [overview, setOverview] = useState<UserOverview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    activeSessions,
    isGlobalLoading,
    globalError,
    pauseAllSessions,
    resumeAllSessions,
    endAllSessions,
    getUserOverview,
  } = useTimeTrackingContext();

  useEffect(() => {
    loadOverview();
  }, []);

  const loadOverview = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await getUserOverview();
      setOverview(data);
    } catch (error: any) {
      setError('Failed to load time tracking overview');
      console.error('Error loading overview:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePauseAll = async () => {
    try {
      await pauseAllSessions('Manual pause from dashboard');
      toast({
        title: 'Sessions Paused',
        description: 'All active sessions have been paused',
      });
      await loadOverview();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to pause some sessions',
        variant: 'destructive',
      });
    }
  };

  const handleResumeAll = async () => {
    try {
      await resumeAllSessions();
      toast({
        title: 'Sessions Resumed',
        description: 'All paused sessions have been resumed',
      });
      await loadOverview();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to resume some sessions',
        variant: 'destructive',
      });
    }
  };

  const handleEndAll = async () => {
    try {
      await endAllSessions();
      toast({
        title: 'Sessions Ended',
        description: 'All active sessions have been ended',
      });
      await loadOverview();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to end some sessions',
        variant: 'destructive',
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button onClick={loadOverview} className="mt-4" variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Time Tracking Dashboard</h1>
          <p className="text-muted-foreground">Monitor your study time and session activity</p>
        </div>
        <Button onClick={loadOverview} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Total Study Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_study_time_formatted || '0:00'}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Total Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_sessions || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Documents Studied
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.total_documents || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Timer className="h-4 w-4" />
              Active Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeSessions.size}</div>
          </CardContent>
        </Card>
      </div>

      {/* Active Sessions Management */}
      {activeSessions.size > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Active Sessions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button onClick={handlePauseAll} variant="outline" size="sm" disabled={isGlobalLoading}>
                <Pause className="h-4 w-4 mr-2" />
                Pause All
              </Button>
              <Button onClick={handleResumeAll} variant="outline" size="sm" disabled={isGlobalLoading}>
                <Play className="h-4 w-4 mr-2" />
                Resume All
              </Button>
              <Button onClick={handleEndAll} variant="destructive" size="sm" disabled={isGlobalLoading}>
                <Square className="h-4 w-4 mr-2" />
                End All
              </Button>
            </div>

            <div className="space-y-2">
              {Array.from(activeSessions.entries()).map(([documentId, session]) => (
                <div key={documentId} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Document {documentId}</p>
                    <p className="text-sm text-muted-foreground">
                      {session.total_time_formatted} • {session.status}
                    </p>
                  </div>
                  <Badge variant={session.status === 'active' ? 'default' : 'secondary'}>
                    {session.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Statistics */}
      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Document Statistics</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Study Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {overview?.document_stats?.map((doc) => (
                    <div key={doc.document} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">{doc.document_title || `Document ${doc.document}`}</h3>
                        {doc.reopened_at_least_once && (
                          <Badge variant="secondary" className="text-xs">
                            Reopened
                          </Badge>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Study Time:</span>
                          <div className="font-mono">{doc.total_study_time_formatted}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Sessions:</span>
                          <div>{doc.total_sessions}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Avg Session:</span>
                          <div className="font-mono">{doc.average_session_duration_formatted}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Views:</span>
                          <div>{doc.view_count}</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-muted-foreground">
                        <div>First Access: {formatDate(doc.first_access)}</div>
                        <div>Last Access: {formatDate(doc.last_access)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Study Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Trend analysis coming soon!</p>
                <p className="text-sm">We're working on detailed analytics and insights.</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {globalError && (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-red-600 text-sm">{globalError}</div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
