'use client'

import React from 'react'
import { useOptimizedTimer } from '@/hooks/use-optimized-timer'

interface OptimizedTimerProps {
  fileName?: string
  enabled?: boolean
  isProcessingComplete?: boolean
  onQuizStart?: () => void
  onQuizEnd?: () => void
  showDebugInfo?: boolean
}

export function OptimizedTimer({
  fileName,
  enabled = true,
  isProcessingComplete = false,
  onQuizStart,
  onQuizEnd,
  showDebugInfo = false
}: OptimizedTimerProps) {
  const {
    isTracking,
    totalTime,
    sessionStart,
    stopTimer,
    pauseTimer,
    resumeTimer,
    incrementQuizCount,
  } = useOptimizedTimer({
    fileName,
    enabled,
    isProcessingComplete,
    heartbeatInterval: 60000, // 1 minute heartbeat
    batchUpdateInterval: 300000 // 5 minute batch updates
  })

  // Handle quiz start
  const handleQuizStart = () => {
    pauseTimer()
    onQuizStart?.()
  }

  // Handle quiz end
  const handleQuizEnd = () => {
    resumeTimer()
    incrementQuizCount()
    onQuizEnd?.()
  }

  // Format time for display
  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  // Don't render anything if not enabled or no file name
  if (!enabled || !fileName) {
    return null
  }

  return (
    <div className="optimized-timer">
      {showDebugInfo && (
        <div className="timer-debug-info p-4 bg-gray-100 rounded-lg mb-4">
          <h3 className="text-lg font-semibold mb-2">Timer Debug Info</h3>
          <div className="space-y-1 text-sm">
            <p><strong>File:</strong> {fileName}</p>
            <p><strong>Status:</strong> {isTracking ? 'Active' : 'Inactive'}</p>
            <p><strong>Session Start:</strong> {sessionStart ? sessionStart.toLocaleTimeString() : 'N/A'}</p>
            <p><strong>Total Time:</strong> {formatTime(totalTime)}</p>
            <p><strong>Processing Complete:</strong> {isProcessingComplete ? 'Yes' : 'No'}</p>
          </div>
          
          <div className="mt-4 space-x-2">
            <button
              onClick={handleQuizStart}
              disabled={!isTracking}
              className="px-3 py-1 bg-yellow-500 text-white rounded disabled:bg-gray-300"
            >
              Start Quiz (Pause)
            </button>
            <button
              onClick={handleQuizEnd}
              disabled={isTracking}
              className="px-3 py-1 bg-green-500 text-white rounded disabled:bg-gray-300"
            >
              End Quiz (Resume)
            </button>
            <button
              onClick={stopTimer}
              disabled={!isTracking}
              className="px-3 py-1 bg-red-500 text-white rounded disabled:bg-gray-300"
            >
              Stop Timer
            </button>
          </div>
        </div>
      )}
      
      {/* Hidden timer component - runs in background */}
      <div className="hidden">
        Timer running for: {fileName}
      </div>
    </div>
  )
}

export default OptimizedTimer
