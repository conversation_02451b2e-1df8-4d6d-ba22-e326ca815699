"use client"

import React from "react"
import { motion, AnimatePresence } from "framer-motion"

interface LoadingOverlayProps {
  isVisible: boolean
  message?: string
}

export function LoadingOverlay({ isVisible, message = "Personalizing your learning journey. Just a moment..." }: LoadingOverlayProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center"
          style={{
            background: "rgba(255, 255, 255, 0)",
            backdropFilter: "blur(8px)",
            WebkitBackdropFilter: "blur(8px)", // Safari support
          }}
          onClick={(e) => e.preventDefault()} // Prevent any clicks
        >
          {/* Branding */}
          <div className="text-2xl font-bold text-purple-600 mb-8">
            Cognimosity
          </div>

          {/* Animated Loaders */}
          <div className="flex gap-5 mb-8">
            {/* Circle Loader */}
            <div className="relative w-12 h-12">
              <svg viewBox="0 0 80 80" className="w-full h-full">
                <circle
                  cx="40"
                  cy="40"
                  r="32"
                  fill="none"
                  stroke="#d3d3d3"
                  strokeWidth="10"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeDasharray="150 50 150 50"
                  strokeDashoffset="75"
                  style={{
                    animation: "pathCircle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                  }}
                />
              </svg>
              <div
                className="absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]"
                style={{
                  animation: "dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                }}
              />
            </div>

            {/* Triangle Loader */}
            <div className="relative w-12 h-12">
              <svg viewBox="0 0 86 80" className="w-full h-full">
                <polygon
                  points="43 8 79 72 7 72"
                  fill="none"
                  stroke="#d3d3d3"
                  strokeWidth="10"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeDasharray="145 76 145 76"
                  strokeDashoffset="0"
                  style={{
                    animation: "pathTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                  }}
                />
              </svg>
              <div
                className="absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[21px] transform -translate-x-[10px] -translate-y-[18px]"
                style={{
                  animation: "dotTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                }}
              />
            </div>

            {/* Square Loader */}
            <div className="relative w-12 h-12">
              <svg viewBox="0 0 80 80" className="w-full h-full">
                <rect
                  x="8"
                  y="8"
                  width="64"
                  height="64"
                  fill="none"
                  stroke="#d3d3d3"
                  strokeWidth="10"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeDasharray="192 64 192 64"
                  strokeDashoffset="0"
                  style={{
                    animation: "pathRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                  }}
                />
              </svg>
              <div
                className="absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]"
                style={{
                  animation: "dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"
                }}
              />
            </div>
          </div>

          {/* Message */}
          <div className="text-base text-gray-700 text-center max-w-md px-4">
            {message}
          </div>


        </motion.div>
      )}
    </AnimatePresence>
  )
}
