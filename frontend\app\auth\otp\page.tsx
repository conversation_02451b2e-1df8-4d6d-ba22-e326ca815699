"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { authApi } from "@/lib/api"
import { toast } from "sonner"

export default function OTPVerificationPage() {
  const router = useRouter()
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""))
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const [timer, setTimer] = useState(60)
  const [isResending, setIsResending] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)

  // Get email from localStorage
  const [email, setEmail] = useState("")

  useEffect(() => {
    // Get email from localStorage
    const storedEmail = localStorage.getItem("email")
    if (!storedEmail) {
      toast.error("No email found. Please register again.")
      router.push("/auth")
      return
    }
    setEmail(storedEmail)

    // Focus the first input on mount
    const firstInput = inputRefs.current[0]
    if (firstInput) {
      firstInput.focus()
    }

    // Start the timer
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [router])

  const handleChange = (index: number, value: string) => {
    // Only allow numbers
    if (!/^\d*$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.substring(0, 1)
    setOtp(newOtp)

    // Auto-focus next input
    if (value && index < 5 && inputRefs.current[index + 1]) {
      const nextInput = inputRefs.current[index + 1]
      if (nextInput) {
        nextInput.focus()
      }
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // Navigate with arrow keys
    if (e.key === "ArrowRight" && index < 5) {
      const nextInput = inputRefs.current[index + 1]
      if (nextInput) {
        nextInput.focus()
      }
    } else if (e.key === "ArrowLeft" && index > 0) {
      const prevInput = inputRefs.current[index - 1]
      if (prevInput) {
        prevInput.focus()
      }
    }

    // Handle backspace
    if (e.key === "Backspace") {
      if (otp[index]) {
        const newOtp = [...otp]
        newOtp[index] = ""
        setOtp(newOtp)
      } else if (index > 0) {
        const prevInput = inputRefs.current[index - 1]
        if (prevInput) {
          prevInput.focus()
        }
      }
    }
  }

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData("text/plain").trim()

    // Check if pasted content is a valid OTP
    if (!/^\d+$/.test(pastedData)) return

    const newOtp = [...otp]
    for (let i = 0; i < Math.min(pastedData.length, 6); i++) {
      newOtp[i] = pastedData[i]
    }
    setOtp(newOtp)

    // Focus the appropriate input
    if (pastedData.length < 6 && inputRefs.current[pastedData.length]) {
      inputRefs.current[pastedData.length].focus()
    }
  }

  const handleResendOTP = async () => {
    if (timer === 0) {
      setIsResending(true)
      try {
        const storedUsername = localStorage.getItem("username")
        if (!storedUsername) {
          toast.error("No username found. Please register again.")
          router.push("/auth")
          return
        }

        // Call your resend OTP API here
        await authApi.register({
          email,
          username: storedUsername,
          password: "", // You might need to store this temporarily
          confirm_password: "",
          first_name: "",
          last_name: "",
        })
        setTimer(60)
        toast.success("OTP resent successfully!")
      } catch (error: any) {
        toast.error(error.response?.data?.message || "Failed to resend OTP. Please try again.")
      } finally {
        setIsResending(false)
      }
    }
  }

  const handleVerify = async () => {
    const otpValue = otp.join("")

    if (otpValue.length !== 6) {
      toast.error("Please enter a valid OTP")
      return
    }

    setIsVerifying(true)

    try {
      const response = await authApi.verifyOTP({
        email,
        otp: otpValue,
      })

      // After successful verification, we need to sign in to get the token
      try {
        const storedUsername = localStorage.getItem("username")
        if (!storedUsername) {
          toast.error("No username found. Please register again.")
          router.push("/auth")
          return
        }

        // Redirect to login page with a success message
        toast.success("Email verified successfully! Please sign in with your credentials.")
        router.push("/auth")
      } catch (loginError: any) {
        toast.error("Account verified but failed to sign in automatically. Please sign in manually.")
        router.push("/auth")
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to verify OTP. Please try again.")
    } finally {
      setIsVerifying(false)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-8">
          <div className="flex items-center gap-2">
            <div className="relative h-10 w-10">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                alt="Cognimosity Logo"
                fill
                className="object-contain"
              />
            </div>
            <span className="text-2xl font-bold">Cognimosity</span>
          </div>
        </div>

        <Card className="border-border bg-card">
          <CardHeader>
            <CardTitle>OTP Verification</CardTitle>
            <CardDescription>
              We've sent a verification code to your email. Please enter the code below.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between mb-6">
              {otp.map((digit, index) => (
                <Input
                  key={index}
                  ref={(el) => {
                    inputRefs.current[index] = el
                  }}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={index === 0 ? handlePaste : undefined}
                  className="w-12 h-12 text-center text-lg"
                />
              ))}
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-2">Didn't receive the code?</p>
              <Button
                variant="link"
                className={`p-0 h-auto text-sm ${timer === 0 ? "text-purple-500" : "text-muted-foreground cursor-not-allowed"}`}
                onClick={handleResendOTP}
                disabled={timer > 0 || isResending}
              >
                {isResending ? "Resending..." : timer > 0 ? `Resend in ${formatTime(timer)}` : "Resend OTP"}
              </Button>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              className="w-full bg-purple-600 hover:bg-purple-700"
              onClick={handleVerify}
              disabled={otp.join("").length !== 6 || isVerifying}
            >
              {isVerifying ? "Verifying..." : "Verify"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
