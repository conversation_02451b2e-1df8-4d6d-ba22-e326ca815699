"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/roughjs";
exports.ids = ["vendor-chunks/roughjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/roughjs/bundled/rough.esm.js":
/*!***************************************************!*\
  !*** ./node_modules/roughjs/bundled/rough.esm.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ at)\n/* harmony export */ });\nfunction t(t,e,s){if(t&&t.length){const[n,o]=e,a=Math.PI/180*s,h=Math.cos(a),r=Math.sin(a);for(const e of t){const[t,s]=e;e[0]=(t-n)*h-(s-o)*r+n,e[1]=(t-n)*r+(s-o)*h+o}}}function e(t,e){return t[0]===e[0]&&t[1]===e[1]}function s(s,n,o,a=1){const h=o,r=Math.max(n,.1),i=s[0]&&s[0][0]&&\"number\"==typeof s[0][0]?[s]:s,c=[0,0];if(h)for(const e of i)t(e,c,h);const l=function(t,s,n){const o=[];for(const s of t){const t=[...s];e(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&o.push(t)}const a=[];s=Math.max(s,.1);const h=[];for(const t of o)for(let e=0;e<t.length-1;e++){const s=t[e],n=t[e+1];if(s[1]!==n[1]){const t=Math.min(s[1],n[1]);h.push({ymin:t,ymax:Math.max(s[1],n[1]),x:t===s[1]?s[0]:n[0],islope:(n[0]-s[0])/(n[1]-s[1])})}}if(h.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!h.length)return a;let r=[],i=h[0].ymin,c=0;for(;r.length||h.length;){if(h.length){let t=-1;for(let e=0;e<h.length&&!(h[e].ymin>i);e++)t=e;h.splice(0,t+1).forEach((t=>{r.push({s:i,edge:t})}))}if(r=r.filter((t=>!(t.edge.ymax<=i))),r.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==n||c%s==0)&&r.length>1)for(let t=0;t<r.length;t+=2){const e=t+1;if(e>=r.length)break;const s=r[t].edge,n=r[e].edge;a.push([[Math.round(s.x),i],[Math.round(n.x),i]])}i+=n,r.forEach((t=>{t.edge.x=t.edge.x+n*t.edge.islope})),c++}return a}(i,r,a);if(h){for(const e of i)t(e,c,-h);!function(e,s,n){const o=[];e.forEach((t=>o.push(...t))),t(o,s,n)}(l,c,-h)}return l}function n(t,e){var n;const o=e.hachureAngle+90;let a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));let h=1;return e.roughness>=1&&((null===(n=e.randomizer)||void 0===n?void 0:n.next())||Math.random())>.7&&(h=a),s(t,a,o,h||1)}class o{constructor(t){this.helper=t}fillPolygons(t,e){return this._fillPolygons(t,e)}_fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.renderLines(s,e)}}renderLines(t,e){const s=[];for(const n of t)s.push(...this.helper.doubleLineOps(n[0][0],n[0][1],n[1][0],n[1][1],e));return s}}function a(t){const e=t[0],s=t[1];return Math.sqrt(Math.pow(e[0]-s[0],2)+Math.pow(e[1]-s[1],2))}class h extends o{fillPolygons(t,e){let s=e.hachureGap;s<0&&(s=4*e.strokeWidth),s=Math.max(s,.1);const o=n(t,Object.assign({},e,{hachureGap:s})),h=Math.PI/180*e.hachureAngle,r=[],i=.5*s*Math.cos(h),c=.5*s*Math.sin(h);for(const[t,e]of o)a([t,e])&&r.push([[t[0]-i,t[1]+c],[...e]],[[t[0]+i,t[1]-c],[...e]]);return{type:\"fillSketch\",ops:this.renderLines(r,e)}}}class r extends o{fillPolygons(t,e){const s=this._fillPolygons(t,e),n=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),o=this._fillPolygons(t,n);return s.ops=s.ops.concat(o.ops),s}}class i{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(s,e)}dotsOnLines(t,e){const s=[];let n=e.hachureGap;n<0&&(n=4*e.strokeWidth),n=Math.max(n,.1);let o=e.fillWeight;o<0&&(o=e.strokeWidth/2);const h=n/4;for(const r of t){const t=a(r),i=t/n,c=Math.ceil(i)-1,l=t-c*n,u=(r[0][0]+r[1][0])/2-n/4,p=Math.min(r[0][1],r[1][1]);for(let t=0;t<c;t++){const a=p+l+t*n,r=u-h+2*Math.random()*h,i=a-h+2*Math.random()*h,c=this.helper.ellipse(r,i,o,o,e);s.push(...c.ops)}}return{type:\"fillSketch\",ops:s}}}class c{constructor(t){this.helper=t}fillPolygons(t,e){const s=n(t,e);return{type:\"fillSketch\",ops:this.dashedLine(s,e)}}dashedLine(t,e){const s=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,n=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,o=[];return t.forEach((t=>{const h=a(t),r=Math.floor(h/(s+n)),i=(h+n-r*(s+n))/2;let c=t[0],l=t[1];c[0]>l[0]&&(c=t[1],l=t[0]);const u=Math.atan((l[1]-c[1])/(l[0]-c[0]));for(let t=0;t<r;t++){const a=t*(s+n),h=a+s,r=[c[0]+a*Math.cos(u)+i*Math.cos(u),c[1]+a*Math.sin(u)+i*Math.sin(u)],l=[c[0]+h*Math.cos(u)+i*Math.cos(u),c[1]+h*Math.sin(u)+i*Math.sin(u)];o.push(...this.helper.doubleLineOps(r[0],r[1],l[0],l[1],e))}})),o}}class l{constructor(t){this.helper=t}fillPolygons(t,e){const s=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,o=e.zigzagOffset<0?s:e.zigzagOffset,a=n(t,e=Object.assign({},e,{hachureGap:s+o}));return{type:\"fillSketch\",ops:this.zigzagLines(a,o,e)}}zigzagLines(t,e,s){const n=[];return t.forEach((t=>{const o=a(t),h=Math.round(o/(2*e));let r=t[0],i=t[1];r[0]>i[0]&&(r=t[1],i=t[0]);const c=Math.atan((i[1]-r[1])/(i[0]-r[0]));for(let t=0;t<h;t++){const o=2*t*e,a=2*(t+1)*e,h=Math.sqrt(2*Math.pow(e,2)),i=[r[0]+o*Math.cos(c),r[1]+o*Math.sin(c)],l=[r[0]+a*Math.cos(c),r[1]+a*Math.sin(c)],u=[i[0]+h*Math.cos(c+Math.PI/4),i[1]+h*Math.sin(c+Math.PI/4)];n.push(...this.helper.doubleLineOps(i[0],i[1],u[0],u[1],s),...this.helper.doubleLineOps(u[0],u[1],l[0],l[1],s))}})),n}}const u={};class p{constructor(t){this.seed=t}next(){return this.seed?(2**31-1&(this.seed=Math.imul(48271,this.seed)))/2**31:Math.random()}}const f=0,d=1,g=2,M={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function k(t,e){return t.type===e}function b(t){const e=[],s=function(t){const e=new Array;for(;\"\"!==t;)if(t.match(/^([ \\t\\r\\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:f,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\\.[0-9]*)?|[-+]?\\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:d,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:g,text:\"\"},e}(t);let n=\"BOD\",o=0,a=s[o];for(;!k(a,g);){let h=0;const r=[];if(\"BOD\"===n){if(\"M\"!==a.text&&\"m\"!==a.text)return b(\"M0,0\"+t);o++,h=M[a.text],n=a.text}else k(a,d)?h=M[n]:(o++,h=M[a.text],n=a.text);if(!(o+h<s.length))throw new Error(\"Path data ended short\");for(let t=o;t<o+h;t++){const e=s[t];if(!k(e,d))throw new Error(\"Param not a number: \"+n+\",\"+e.text);r[r.length]=+e.text}if(\"number\"!=typeof M[n])throw new Error(\"Bad segment: \"+n);{const t={key:n,data:r};e.push(t),o+=h,a=s[o],\"M\"===n&&(n=\"L\"),\"m\"===n&&(n=\"l\")}}return e}function y(t){let e=0,s=0,n=0,o=0;const a=[];for(const{key:h,data:r}of t)switch(h){case\"M\":a.push({key:\"M\",data:[...r]}),[e,s]=r,[n,o]=r;break;case\"m\":e+=r[0],s+=r[1],a.push({key:\"M\",data:[e,s]}),n=e,o=s;break;case\"L\":a.push({key:\"L\",data:[...r]}),[e,s]=r;break;case\"l\":e+=r[0],s+=r[1],a.push({key:\"L\",data:[e,s]});break;case\"C\":a.push({key:\"C\",data:[...r]}),e=r[4],s=r[5];break;case\"c\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"C\",data:t}),e=t[4],s=t[5];break}case\"Q\":a.push({key:\"Q\",data:[...r]}),e=r[2],s=r[3];break;case\"q\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"Q\",data:t}),e=t[2],s=t[3];break}case\"A\":a.push({key:\"A\",data:[...r]}),e=r[5],s=r[6];break;case\"a\":e+=r[5],s+=r[6],a.push({key:\"A\",data:[r[0],r[1],r[2],r[3],r[4],e,s]});break;case\"H\":a.push({key:\"H\",data:[...r]}),e=r[0];break;case\"h\":e+=r[0],a.push({key:\"H\",data:[e]});break;case\"V\":a.push({key:\"V\",data:[...r]}),s=r[0];break;case\"v\":s+=r[0],a.push({key:\"V\",data:[s]});break;case\"S\":a.push({key:\"S\",data:[...r]}),e=r[2],s=r[3];break;case\"s\":{const t=r.map(((t,n)=>n%2?t+s:t+e));a.push({key:\"S\",data:t}),e=t[2],s=t[3];break}case\"T\":a.push({key:\"T\",data:[...r]}),e=r[0],s=r[1];break;case\"t\":e+=r[0],s+=r[1],a.push({key:\"T\",data:[e,s]});break;case\"Z\":case\"z\":a.push({key:\"Z\",data:[]}),e=n,s=o}return a}function m(t){const e=[];let s=\"\",n=0,o=0,a=0,h=0,r=0,i=0;for(const{key:c,data:l}of t){switch(c){case\"M\":e.push({key:\"M\",data:[...l]}),[n,o]=l,[a,h]=l;break;case\"C\":e.push({key:\"C\",data:[...l]}),n=l[4],o=l[5],r=l[2],i=l[3];break;case\"L\":e.push({key:\"L\",data:[...l]}),[n,o]=l;break;case\"H\":n=l[0],e.push({key:\"L\",data:[n,o]});break;case\"V\":o=l[0],e.push({key:\"L\",data:[n,o]});break;case\"S\":{let t=0,a=0;\"C\"===s||\"S\"===s?(t=n+(n-r),a=o+(o-i)):(t=n,a=o),e.push({key:\"C\",data:[t,a,...l]}),r=l[0],i=l[1],n=l[2],o=l[3];break}case\"T\":{const[t,a]=l;let h=0,c=0;\"Q\"===s||\"T\"===s?(h=n+(n-r),c=o+(o-i)):(h=n,c=o);const u=n+2*(h-n)/3,p=o+2*(c-o)/3,f=t+2*(h-t)/3,d=a+2*(c-a)/3;e.push({key:\"C\",data:[u,p,f,d,t,a]}),r=h,i=c,n=t,o=a;break}case\"Q\":{const[t,s,a,h]=l,c=n+2*(t-n)/3,u=o+2*(s-o)/3,p=a+2*(t-a)/3,f=h+2*(s-h)/3;e.push({key:\"C\",data:[c,u,p,f,a,h]}),r=t,i=s,n=a,o=h;break}case\"A\":{const t=Math.abs(l[0]),s=Math.abs(l[1]),a=l[2],h=l[3],r=l[4],i=l[5],c=l[6];if(0===t||0===s)e.push({key:\"C\",data:[n,o,i,c,i,c]}),n=i,o=c;else if(n!==i||o!==c){x(n,o,i,c,t,s,a,h,r).forEach((function(t){e.push({key:\"C\",data:t})})),n=i,o=c}break}case\"Z\":e.push({key:\"Z\",data:[]}),n=a,o=h}s=c}return e}function w(t,e,s){return[t*Math.cos(s)-e*Math.sin(s),t*Math.sin(s)+e*Math.cos(s)]}function x(t,e,s,n,o,a,h,r,i,c){const l=(u=h,Math.PI*u/180);var u;let p=[],f=0,d=0,g=0,M=0;if(c)[f,d,g,M]=c;else{[t,e]=w(t,e,-l),[s,n]=w(s,n,-l);const h=(t-s)/2,c=(e-n)/2;let u=h*h/(o*o)+c*c/(a*a);u>1&&(u=Math.sqrt(u),o*=u,a*=u);const p=o*o,k=a*a,b=p*k-p*c*c-k*h*h,y=p*c*c+k*h*h,m=(r===i?-1:1)*Math.sqrt(Math.abs(b/y));g=m*o*c/a+(t+s)/2,M=m*-a*h/o+(e+n)/2,f=Math.asin(parseFloat(((e-M)/a).toFixed(9))),d=Math.asin(parseFloat(((n-M)/a).toFixed(9))),t<g&&(f=Math.PI-f),s<g&&(d=Math.PI-d),f<0&&(f=2*Math.PI+f),d<0&&(d=2*Math.PI+d),i&&f>d&&(f-=2*Math.PI),!i&&d>f&&(d-=2*Math.PI)}let k=d-f;if(Math.abs(k)>120*Math.PI/180){const t=d,e=s,r=n;d=i&&d>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,p=x(s=g+o*Math.cos(d),n=M+a*Math.sin(d),e,r,o,a,h,0,i,[d,t,g,M])}k=d-f;const b=Math.cos(f),y=Math.sin(f),m=Math.cos(d),P=Math.sin(d),v=Math.tan(k/4),S=4/3*o*v,O=4/3*a*v,L=[t,e],T=[t+S*y,e-O*b],D=[s+S*P,n-O*m],A=[s,n];if(T[0]=2*L[0]-T[0],T[1]=2*L[1]-T[1],c)return[T,D,A].concat(p);{p=[T,D,A].concat(p);const t=[];for(let e=0;e<p.length;e+=3){const s=w(p[e][0],p[e][1],l),n=w(p[e+1][0],p[e+1][1],l),o=w(p[e+2][0],p[e+2][1],l);t.push([s[0],s[1],n[0],n[1],o[0],o[1]])}return t}}const P={randOffset:function(t,e){return G(t,e)},randOffsetWithRange:function(t,e,s){return E(t,e,s)},ellipse:function(t,e,s,n,o){const a=T(s,n,o);return D(t,e,o,a).opset},doubleLineOps:function(t,e,s,n,o){return $(t,e,s,n,o,!0)}};function v(t,e,s,n,o){return{type:\"path\",ops:$(t,e,s,n,o)}}function S(t,e,s){const n=(t||[]).length;if(n>2){const o=[];for(let e=0;e<n-1;e++)o.push(...$(t[e][0],t[e][1],t[e+1][0],t[e+1][1],s));return e&&o.push(...$(t[n-1][0],t[n-1][1],t[0][0],t[0][1],s)),{type:\"path\",ops:o}}return 2===n?v(t[0][0],t[0][1],t[1][0],t[1][1],s):{type:\"path\",ops:[]}}function O(t,e,s,n,o){return function(t,e){return S(t,!0,e)}([[t,e],[t+s,e],[t+s,e+n],[t,e+n]],o)}function L(t,e){if(t.length){const s=\"number\"==typeof t[0][0]?[t]:t,n=j(s[0],1*(1+.2*e.roughness),e),o=e.disableMultiStroke?[]:j(s[0],1.5*(1+.22*e.roughness),z(e));for(let t=1;t<s.length;t++){const a=s[t];if(a.length){const t=j(a,1*(1+.2*e.roughness),e),s=e.disableMultiStroke?[]:j(a,1.5*(1+.22*e.roughness),z(e));for(const e of t)\"move\"!==e.op&&n.push(e);for(const t of s)\"move\"!==t.op&&o.push(t)}}return{type:\"path\",ops:n.concat(o)}}return{type:\"path\",ops:[]}}function T(t,e,s){const n=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),o=Math.ceil(Math.max(s.curveStepCount,s.curveStepCount/Math.sqrt(200)*n)),a=2*Math.PI/o;let h=Math.abs(t/2),r=Math.abs(e/2);const i=1-s.curveFitting;return h+=G(h*i,s),r+=G(r*i,s),{increment:a,rx:h,ry:r}}function D(t,e,s,n){const[o,a]=F(n.increment,t,e,n.rx,n.ry,1,n.increment*E(.1,E(.4,1,s),s),s);let h=q(o,null,s);if(!s.disableMultiStroke&&0!==s.roughness){const[o]=F(n.increment,t,e,n.rx,n.ry,1.5,0,s),a=q(o,null,s);h=h.concat(a)}return{estimatedPoints:a,opset:{type:\"path\",ops:h}}}function A(t,e,s,n,o,a,h,r,i){const c=t,l=e;let u=Math.abs(s/2),p=Math.abs(n/2);u+=G(.01*u,i),p+=G(.01*p,i);let f=o,d=a;for(;f<0;)f+=2*Math.PI,d+=2*Math.PI;d-f>2*Math.PI&&(f=0,d=2*Math.PI);const g=2*Math.PI/i.curveStepCount,M=Math.min(g/2,(d-f)/2),k=V(M,c,l,u,p,f,d,1,i);if(!i.disableMultiStroke){const t=V(M,c,l,u,p,f,d,1.5,i);k.push(...t)}return h&&(r?k.push(...$(c,l,c+u*Math.cos(f),l+p*Math.sin(f),i),...$(c,l,c+u*Math.cos(d),l+p*Math.sin(d),i)):k.push({op:\"lineTo\",data:[c,l]},{op:\"lineTo\",data:[c+u*Math.cos(f),l+p*Math.sin(f)]})),{type:\"path\",ops:k}}function _(t,e){const s=m(y(b(t))),n=[];let o=[0,0],a=[0,0];for(const{key:t,data:h}of s)switch(t){case\"M\":a=[h[0],h[1]],o=[h[0],h[1]];break;case\"L\":n.push(...$(a[0],a[1],h[0],h[1],e)),a=[h[0],h[1]];break;case\"C\":{const[t,s,o,r,i,c]=h;n.push(...Z(t,s,o,r,i,c,a,e)),a=[i,c];break}case\"Z\":n.push(...$(a[0],a[1],o[0],o[1],e)),a=[o[0],o[1]]}return{type:\"path\",ops:n}}function I(t,e){const s=[];for(const n of t)if(n.length){const t=e.maxRandomnessOffset||0,o=n.length;if(o>2){s.push({op:\"move\",data:[n[0][0]+G(t,e),n[0][1]+G(t,e)]});for(let a=1;a<o;a++)s.push({op:\"lineTo\",data:[n[a][0]+G(t,e),n[a][1]+G(t,e)]})}}return{type:\"fillPath\",ops:s}}function C(t,e){return function(t,e){let s=t.fillStyle||\"hachure\";if(!u[s])switch(s){case\"zigzag\":u[s]||(u[s]=new h(e));break;case\"cross-hatch\":u[s]||(u[s]=new r(e));break;case\"dots\":u[s]||(u[s]=new i(e));break;case\"dashed\":u[s]||(u[s]=new c(e));break;case\"zigzag-line\":u[s]||(u[s]=new l(e));break;default:s=\"hachure\",u[s]||(u[s]=new o(e))}return u[s]}(e,P).fillPolygons(t,e)}function z(t){const e=Object.assign({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function W(t){return t.randomizer||(t.randomizer=new p(t.seed||0)),t.randomizer.next()}function E(t,e,s,n=1){return s.roughness*n*(W(s)*(e-t)+t)}function G(t,e,s=1){return E(-t,t,e,s)}function $(t,e,s,n,o,a=!1){const h=a?o.disableMultiStrokeFill:o.disableMultiStroke,r=R(t,e,s,n,o,!0,!1);if(h)return r;const i=R(t,e,s,n,o,!0,!0);return r.concat(i)}function R(t,e,s,n,o,a,h){const r=Math.pow(t-s,2)+Math.pow(e-n,2),i=Math.sqrt(r);let c=1;c=i<200?1:i>500?.4:-.0016668*i+1.233334;let l=o.maxRandomnessOffset||0;l*l*100>r&&(l=i/10);const u=l/2,p=.2+.2*W(o);let f=o.bowing*o.maxRandomnessOffset*(n-e)/200,d=o.bowing*o.maxRandomnessOffset*(t-s)/200;f=G(f,o,c),d=G(d,o,c);const g=[],M=()=>G(u,o,c),k=()=>G(l,o,c),b=o.preserveVertices;return a&&(h?g.push({op:\"move\",data:[t+(b?0:M()),e+(b?0:M())]}):g.push({op:\"move\",data:[t+(b?0:G(l,o,c)),e+(b?0:G(l,o,c))]})),h?g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+M(),d+e+(n-e)*p+M(),f+t+2*(s-t)*p+M(),d+e+2*(n-e)*p+M(),s+(b?0:M()),n+(b?0:M())]}):g.push({op:\"bcurveTo\",data:[f+t+(s-t)*p+k(),d+e+(n-e)*p+k(),f+t+2*(s-t)*p+k(),d+e+2*(n-e)*p+k(),s+(b?0:k()),n+(b?0:k())]}),g}function j(t,e,s){if(!t.length)return[];const n=[];n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]),n.push([t[0][0]+G(e,s),t[0][1]+G(e,s)]);for(let o=1;o<t.length;o++)n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]),o===t.length-1&&n.push([t[o][0]+G(e,s),t[o][1]+G(e,s)]);return q(n,null,s)}function q(t,e,s){const n=t.length,o=[];if(n>3){const a=[],h=1-s.curveTightness;o.push({op:\"move\",data:[t[1][0],t[1][1]]});for(let e=1;e+2<n;e++){const s=t[e];a[0]=[s[0],s[1]],a[1]=[s[0]+(h*t[e+1][0]-h*t[e-1][0])/6,s[1]+(h*t[e+1][1]-h*t[e-1][1])/6],a[2]=[t[e+1][0]+(h*t[e][0]-h*t[e+2][0])/6,t[e+1][1]+(h*t[e][1]-h*t[e+2][1])/6],a[3]=[t[e+1][0],t[e+1][1]],o.push({op:\"bcurveTo\",data:[a[1][0],a[1][1],a[2][0],a[2][1],a[3][0],a[3][1]]})}if(e&&2===e.length){const t=s.maxRandomnessOffset;o.push({op:\"lineTo\",data:[e[0]+G(t,s),e[1]+G(t,s)]})}}else 3===n?(o.push({op:\"move\",data:[t[1][0],t[1][1]]}),o.push({op:\"bcurveTo\",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===n&&o.push(...R(t[0][0],t[0][1],t[1][0],t[1][1],s,!0,!0));return o}function F(t,e,s,n,o,a,h,r){const i=[],c=[];if(0===r.roughness){t/=4,c.push([e+n*Math.cos(-t),s+o*Math.sin(-t)]);for(let a=0;a<=2*Math.PI;a+=t){const t=[e+n*Math.cos(a),s+o*Math.sin(a)];i.push(t),c.push(t)}c.push([e+n*Math.cos(0),s+o*Math.sin(0)]),c.push([e+n*Math.cos(t),s+o*Math.sin(t)])}else{const l=G(.5,r)-Math.PI/2;c.push([G(a,r)+e+.9*n*Math.cos(l-t),G(a,r)+s+.9*o*Math.sin(l-t)]);const u=2*Math.PI+l-.01;for(let h=l;h<u;h+=t){const t=[G(a,r)+e+n*Math.cos(h),G(a,r)+s+o*Math.sin(h)];i.push(t),c.push(t)}c.push([G(a,r)+e+n*Math.cos(l+2*Math.PI+.5*h),G(a,r)+s+o*Math.sin(l+2*Math.PI+.5*h)]),c.push([G(a,r)+e+.98*n*Math.cos(l+h),G(a,r)+s+.98*o*Math.sin(l+h)]),c.push([G(a,r)+e+.9*n*Math.cos(l+.5*h),G(a,r)+s+.9*o*Math.sin(l+.5*h)])}return[c,i]}function V(t,e,s,n,o,a,h,r,i){const c=a+G(.1,i),l=[];l.push([G(r,i)+e+.9*n*Math.cos(c-t),G(r,i)+s+.9*o*Math.sin(c-t)]);for(let a=c;a<=h;a+=t)l.push([G(r,i)+e+n*Math.cos(a),G(r,i)+s+o*Math.sin(a)]);return l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),l.push([e+n*Math.cos(h),s+o*Math.sin(h)]),q(l,null,i)}function Z(t,e,s,n,o,a,h,r){const i=[],c=[r.maxRandomnessOffset||1,(r.maxRandomnessOffset||1)+.3];let l=[0,0];const u=r.disableMultiStroke?1:2,p=r.preserveVertices;for(let f=0;f<u;f++)0===f?i.push({op:\"move\",data:[h[0],h[1]]}):i.push({op:\"move\",data:[h[0]+(p?0:G(c[0],r)),h[1]+(p?0:G(c[0],r))]}),l=p?[o,a]:[o+G(c[f],r),a+G(c[f],r)],i.push({op:\"bcurveTo\",data:[t+G(c[f],r),e+G(c[f],r),s+G(c[f],r),n+G(c[f],r),l[0],l[1]]});return i}function Q(t){return[...t]}function H(t,e=0){const s=t.length;if(s<3)throw new Error(\"A curve must have at least three points.\");const n=[];if(3===s)n.push(Q(t[0]),Q(t[1]),Q(t[2]),Q(t[2]));else{const s=[];s.push(t[0],t[0]);for(let e=1;e<t.length;e++)s.push(t[e]),e===t.length-1&&s.push(t[e]);const o=[],a=1-e;n.push(Q(s[0]));for(let t=1;t+2<s.length;t++){const e=s[t];o[0]=[e[0],e[1]],o[1]=[e[0]+(a*s[t+1][0]-a*s[t-1][0])/6,e[1]+(a*s[t+1][1]-a*s[t-1][1])/6],o[2]=[s[t+1][0]+(a*s[t][0]-a*s[t+2][0])/6,s[t+1][1]+(a*s[t][1]-a*s[t+2][1])/6],o[3]=[s[t+1][0],s[t+1][1]],n.push(o[1],o[2],o[3])}}return n}function N(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function B(t,e,s){const n=N(e,s);if(0===n)return N(t,e);let o=((t[0]-e[0])*(s[0]-e[0])+(t[1]-e[1])*(s[1]-e[1]))/n;return o=Math.max(0,Math.min(1,o)),N(t,J(e,s,o))}function J(t,e,s){return[t[0]+(e[0]-t[0])*s,t[1]+(e[1]-t[1])*s]}function K(t,e,s,n){const o=n||[];if(function(t,e){const s=t[e+0],n=t[e+1],o=t[e+2],a=t[e+3];let h=3*n[0]-2*s[0]-a[0];h*=h;let r=3*n[1]-2*s[1]-a[1];r*=r;let i=3*o[0]-2*a[0]-s[0];i*=i;let c=3*o[1]-2*a[1]-s[1];return c*=c,h<i&&(h=i),r<c&&(r=c),h+r}(t,e)<s){const s=t[e+0];if(o.length){(a=o[o.length-1],h=s,Math.sqrt(N(a,h)))>1&&o.push(s)}else o.push(s);o.push(t[e+3])}else{const n=.5,a=t[e+0],h=t[e+1],r=t[e+2],i=t[e+3],c=J(a,h,n),l=J(h,r,n),u=J(r,i,n),p=J(c,l,n),f=J(l,u,n),d=J(p,f,n);K([a,c,p,d],0,s,o),K([d,f,u,i],0,s,o)}var a,h;return o}function U(t,e){return X(t,0,t.length,e)}function X(t,e,s,n,o){const a=o||[],h=t[e],r=t[s-1];let i=0,c=1;for(let n=e+1;n<s-1;++n){const e=B(t[n],h,r);e>i&&(i=e,c=n)}return Math.sqrt(i)>n?(X(t,e,c+1,n,a),X(t,c,s,n,a)):(a.length||a.push(h),a.push(r)),a}function Y(t,e=.15,s){const n=[],o=(t.length-1)/3;for(let s=0;s<o;s++){K(t,3*s,e,n)}return s&&s>0?X(n,0,n.length,s):n}const tt=\"none\";class et{constructor(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:\"#000\",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:\"hachure\",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}static newSeed(){return Math.floor(Math.random()*2**31)}_o(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions}_d(t,e,s){return{shape:t,sets:e||[],options:s||this.defaultOptions}}line(t,e,s,n,o){const a=this._o(o);return this._d(\"line\",[v(t,e,s,n,a)],a)}rectangle(t,e,s,n,o){const a=this._o(o),h=[],r=O(t,e,s,n,a);if(a.fill){const o=[[t,e],[t+s,e],[t+s,e+n],[t,e+n]];\"solid\"===a.fillStyle?h.push(I([o],a)):h.push(C([o],a))}return a.stroke!==tt&&h.push(r),this._d(\"rectangle\",h,a)}ellipse(t,e,s,n,o){const a=this._o(o),h=[],r=T(s,n,a),i=D(t,e,a,r);if(a.fill)if(\"solid\"===a.fillStyle){const s=D(t,e,a,r).opset;s.type=\"fillPath\",h.push(s)}else h.push(C([i.estimatedPoints],a));return a.stroke!==tt&&h.push(i.opset),this._d(\"ellipse\",h,a)}circle(t,e,s,n){const o=this.ellipse(t,e,s,s,n);return o.shape=\"circle\",o}linearPath(t,e){const s=this._o(e);return this._d(\"linearPath\",[S(t,!1,s)],s)}arc(t,e,s,n,o,a,h=!1,r){const i=this._o(r),c=[],l=A(t,e,s,n,o,a,h,!0,i);if(h&&i.fill)if(\"solid\"===i.fillStyle){const h=Object.assign({},i);h.disableMultiStroke=!0;const r=A(t,e,s,n,o,a,!0,!1,h);r.type=\"fillPath\",c.push(r)}else c.push(function(t,e,s,n,o,a,h){const r=t,i=e;let c=Math.abs(s/2),l=Math.abs(n/2);c+=G(.01*c,h),l+=G(.01*l,h);let u=o,p=a;for(;u<0;)u+=2*Math.PI,p+=2*Math.PI;p-u>2*Math.PI&&(u=0,p=2*Math.PI);const f=(p-u)/h.curveStepCount,d=[];for(let t=u;t<=p;t+=f)d.push([r+c*Math.cos(t),i+l*Math.sin(t)]);return d.push([r+c*Math.cos(p),i+l*Math.sin(p)]),d.push([r,i]),C([d],h)}(t,e,s,n,o,a,i));return i.stroke!==tt&&c.push(l),this._d(\"arc\",c,i)}curve(t,e){const s=this._o(e),n=[],o=L(t,s);if(s.fill&&s.fill!==tt)if(\"solid\"===s.fillStyle){const e=L(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else{const e=[],o=t;if(o.length){const t=\"number\"==typeof o[0][0]?[o]:o;for(const n of t)n.length<3?e.push(...n):3===n.length?e.push(...Y(H([n[0],n[0],n[1],n[2]]),10,(1+s.roughness)/2)):e.push(...Y(H(n),10,(1+s.roughness)/2))}e.length&&n.push(C([e],s))}return s.stroke!==tt&&n.push(o),this._d(\"curve\",n,s)}polygon(t,e){const s=this._o(e),n=[],o=S(t,!0,s);return s.fill&&(\"solid\"===s.fillStyle?n.push(I([t],s)):n.push(C([t],s))),s.stroke!==tt&&n.push(o),this._d(\"polygon\",n,s)}path(t,e){const s=this._o(e),n=[];if(!t)return this._d(\"path\",n,s);t=(t||\"\").replace(/\\n/g,\" \").replace(/(-\\s)/g,\"-\").replace(\"/(ss)/g\",\" \");const o=s.fill&&\"transparent\"!==s.fill&&s.fill!==tt,a=s.stroke!==tt,h=!!(s.simplification&&s.simplification<1),r=function(t,e,s){const n=m(y(b(t))),o=[];let a=[],h=[0,0],r=[];const i=()=>{r.length>=4&&a.push(...Y(r,e)),r=[]},c=()=>{i(),a.length&&(o.push(a),a=[])};for(const{key:t,data:e}of n)switch(t){case\"M\":c(),h=[e[0],e[1]],a.push(h);break;case\"L\":i(),a.push([e[0],e[1]]);break;case\"C\":if(!r.length){const t=a.length?a[a.length-1]:h;r.push([t[0],t[1]])}r.push([e[0],e[1]]),r.push([e[2],e[3]]),r.push([e[4],e[5]]);break;case\"Z\":i(),a.push([h[0],h[1]])}if(c(),!s)return o;const l=[];for(const t of o){const e=U(t,s);e.length&&l.push(e)}return l}(t,1,h?4-4*(s.simplification||1):(1+s.roughness)/2),i=_(t,s);if(o)if(\"solid\"===s.fillStyle)if(1===r.length){const e=_(t,Object.assign(Object.assign({},s),{disableMultiStroke:!0,roughness:s.roughness?s.roughness+s.fillShapeRoughnessGain:0}));n.push({type:\"fillPath\",ops:this._mergedShape(e.ops)})}else n.push(I(r,s));else n.push(C(r,s));return a&&(h?r.forEach((t=>{n.push(S(t,!1,s))})):n.push(i)),this._d(\"path\",n,s)}opsToPath(t,e){let s=\"\";for(const n of t.ops){const t=\"number\"==typeof e&&e>=0?n.data.map((t=>+t.toFixed(e))):n.data;switch(n.op){case\"move\":s+=`M${t[0]} ${t[1]} `;break;case\"bcurveTo\":s+=`C${t[0]} ${t[1]}, ${t[2]} ${t[3]}, ${t[4]} ${t[5]} `;break;case\"lineTo\":s+=`L${t[0]} ${t[1]} `}}return s.trim()}toPaths(t){const e=t.sets||[],s=t.options||this.defaultOptions,n=[];for(const t of e){let e=null;switch(t.type){case\"path\":e={d:this.opsToPath(t),stroke:s.stroke,strokeWidth:s.strokeWidth,fill:tt};break;case\"fillPath\":e={d:this.opsToPath(t),stroke:tt,strokeWidth:0,fill:s.fill||tt};break;case\"fillSketch\":e=this.fillSketch(t,s)}e&&n.push(e)}return n}fillSketch(t,e){let s=e.fillWeight;return s<0&&(s=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||tt,strokeWidth:s,fill:tt}}_mergedShape(t){return t.filter(((t,e)=>0===e||\"move\"!==t.op))}}class st{constructor(t,e){this.canvas=t,this.ctx=this.canvas.getContext(\"2d\"),this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.ctx,o=t.options.fixedDecimalPlaceDigits;for(const a of e)switch(a.type){case\"path\":n.save(),n.strokeStyle=\"none\"===s.stroke?\"transparent\":s.stroke,n.lineWidth=s.strokeWidth,s.strokeLineDash&&n.setLineDash(s.strokeLineDash),s.strokeLineDashOffset&&(n.lineDashOffset=s.strokeLineDashOffset),this._drawToContext(n,a,o),n.restore();break;case\"fillPath\":{n.save(),n.fillStyle=s.fill||\"\";const e=\"curve\"===t.shape||\"polygon\"===t.shape||\"path\"===t.shape?\"evenodd\":\"nonzero\";this._drawToContext(n,a,o,e),n.restore();break}case\"fillSketch\":this.fillSketch(n,a,s)}}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2),t.save(),s.fillLineDash&&t.setLineDash(s.fillLineDash),s.fillLineDashOffset&&(t.lineDashOffset=s.fillLineDashOffset),t.strokeStyle=s.fill||\"\",t.lineWidth=n,this._drawToContext(t,e,s.fixedDecimalPlaceDigits),t.restore()}_drawToContext(t,e,s,n=\"nonzero\"){t.beginPath();for(const n of e.ops){const e=\"number\"==typeof s&&s>=0?n.data.map((t=>+t.toFixed(s))):n.data;switch(n.op){case\"move\":t.moveTo(e[0],e[1]);break;case\"bcurveTo\":t.bezierCurveTo(e[0],e[1],e[2],e[3],e[4],e[5]);break;case\"lineTo\":t.lineTo(e[0],e[1])}}\"fillPath\"===e.type?t.fill(n):t.stroke()}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a),a}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a),a}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a),a}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o),o}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s),s}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s),s}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i),i}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s),s}path(t,e){const s=this.gen.path(t,e);return this.draw(s),s}}const nt=\"http://www.w3.org/2000/svg\";class ot{constructor(t,e){this.svg=t,this.gen=new et(e)}draw(t){const e=t.sets||[],s=t.options||this.getDefaultOptions(),n=this.svg.ownerDocument||window.document,o=n.createElementNS(nt,\"g\"),a=t.options.fixedDecimalPlaceDigits;for(const h of e){let e=null;switch(h.type){case\"path\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",s.stroke),e.setAttribute(\"stroke-width\",s.strokeWidth+\"\"),e.setAttribute(\"fill\",\"none\"),s.strokeLineDash&&e.setAttribute(\"stroke-dasharray\",s.strokeLineDash.join(\" \").trim()),s.strokeLineDashOffset&&e.setAttribute(\"stroke-dashoffset\",`${s.strokeLineDashOffset}`);break;case\"fillPath\":e=n.createElementNS(nt,\"path\"),e.setAttribute(\"d\",this.opsToPath(h,a)),e.setAttribute(\"stroke\",\"none\"),e.setAttribute(\"stroke-width\",\"0\"),e.setAttribute(\"fill\",s.fill||\"\"),\"curve\"!==t.shape&&\"polygon\"!==t.shape||e.setAttribute(\"fill-rule\",\"evenodd\");break;case\"fillSketch\":e=this.fillSketch(n,h,s)}e&&o.appendChild(e)}return o}fillSketch(t,e,s){let n=s.fillWeight;n<0&&(n=s.strokeWidth/2);const o=t.createElementNS(nt,\"path\");return o.setAttribute(\"d\",this.opsToPath(e,s.fixedDecimalPlaceDigits)),o.setAttribute(\"stroke\",s.fill||\"\"),o.setAttribute(\"stroke-width\",n+\"\"),o.setAttribute(\"fill\",\"none\"),s.fillLineDash&&o.setAttribute(\"stroke-dasharray\",s.fillLineDash.join(\" \").trim()),s.fillLineDashOffset&&o.setAttribute(\"stroke-dashoffset\",`${s.fillLineDashOffset}`),o}get generator(){return this.gen}getDefaultOptions(){return this.gen.defaultOptions}opsToPath(t,e){return this.gen.opsToPath(t,e)}line(t,e,s,n,o){const a=this.gen.line(t,e,s,n,o);return this.draw(a)}rectangle(t,e,s,n,o){const a=this.gen.rectangle(t,e,s,n,o);return this.draw(a)}ellipse(t,e,s,n,o){const a=this.gen.ellipse(t,e,s,n,o);return this.draw(a)}circle(t,e,s,n){const o=this.gen.circle(t,e,s,n);return this.draw(o)}linearPath(t,e){const s=this.gen.linearPath(t,e);return this.draw(s)}polygon(t,e){const s=this.gen.polygon(t,e);return this.draw(s)}arc(t,e,s,n,o,a,h=!1,r){const i=this.gen.arc(t,e,s,n,o,a,h,r);return this.draw(i)}curve(t,e){const s=this.gen.curve(t,e);return this.draw(s)}path(t,e){const s=this.gen.path(t,e);return this.draw(s)}}var at={canvas:(t,e)=>new st(t,e),svg:(t,e)=>new ot(t,e),generator:t=>new et(t),newSeed:()=>et.newSeed()};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/roughjs/bundled/rough.esm.js\n");

/***/ })

};
;