# Cogni API

A decoupled architecture with Django and FastAPI servers for LLM inference, document processing, user authentication, usage tracking, and payment integration.

## Architecture

The system is designed with a decoupled architecture:

1. **Django Server**:
   - Handles user authentication
   - Manages database operations
   - Provides the main REST API
   - Schedules background tasks with Celery

2. **FastAPI Server**:
   - Handles LLM inference
   - Processes documents and generates embeddings
   - Analyzes blueprints and identifies topics
   - Communicates with Django server via REST API

## Features

- User authentication and authorization
- Email verification
- Usage tracking and limits
- File upload and management
- Document processing and embedding generation
- Blueprint analysis and topic identification
- Chat history and session management
- Payment processing with Razorpay
- Integration with OpenAI and Google's Gemini
- Background task processing with Celery
- Rate limiting and security features

## Prerequisites

- Python 3.8+
- Redis server
- PostgreSQL (optional, SQLite by default)
- Virtual environment (recommended)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/cogni_api.git
cd cogni_api
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies for Django server:
```bash
pip install -r requirements.txt
```

4. Install dependencies for FastAPI server:
```bash
pip install fastapi uvicorn httpx python-dotenv openai google-generativeai sentence-transformers nltk PyPDF2
```

5. Create a `.env` file in the root directory with the following variables:
```env
# Django settings
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1
DB_NAME=db.sqlite3
MEDIA_ROOT=media

# Email settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# API keys
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret

# FastAPI Integration
FASTAPI_URL=http://localhost:8001

# Celery settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

6. Create a `.env` file in the `llm_api` directory with the following variables:
```env
# FastAPI server configuration
PORT=8001
HOST=0.0.0.0

# Django server URL
DJANGO_SERVER_URL=http://localhost:8000

# API keys for LLM services
OPENAI_API_KEY=your-openai-api-key
GOOGLE_API_KEY=your-google-api-key
```

7. Run migrations:
```bash
python manage.py makemigrations
python manage.py migrate
```

8. Create a superuser:
```bash
python manage.py createsuperuser
```

9. Start Redis server:
```bash
redis-server
```

10. Start Celery worker:
```bash
celery -A core worker -l info
```

11. Start Celery beat (for scheduled tasks):
```bash
celery -A core beat -l info
```

12. Run the Django development server:
```bash
python manage.py runserver
```

13. Run the FastAPI server:
```bash
cd llm_api
# Method 1: Using uvicorn directly
uvicorn main:app --reload --port 8001

# Method 2: Using Python to run the file
python -m uvicorn main:app --reload --port 8001
```

## API Documentation

### Django API
The Django API documentation is available at `/api/docs/` when running the development server.

### FastAPI API
The FastAPI documentation is available at `http://localhost:8001/docs` when running the FastAPI server.

### Authentication

All API endpoints require authentication using a token. To get a token:

1. Register a new user at `/api/users/register/`
2. Verify your email using the link sent to your email
3. Login at `/api/users/login/` to get your token
4. Use the token in the Authorization header: `Authorization: Token <your_token>`

### Rate Limits

- Anonymous users: 60 requests per minute
- Authenticated users: 1000 requests per hour

## File Upload

- Maximum file size: 10MB
- Allowed file types: PDF, DOC, DOCX, TXT
- Rate limit: 5 files per day for free users, 20 for paid users

## Chat Usage

- Free users: 5 chats per day
- Paid users: 100 chats per day

## Testing

Run tests with:
```bash
pytest
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.