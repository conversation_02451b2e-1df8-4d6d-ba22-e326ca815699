"use client"

import { useState, useEffect } from 'react'
import { Clock } from 'lucide-react'

interface SessionTimeDisplayProps {
  isActive: boolean
  isPaused: boolean
  sessionStartTime?: Date | null
  className?: string
}

export function SessionTimeDisplay({ 
  isActive, 
  isPaused, 
  sessionStartTime,
  className = "" 
}: SessionTimeDisplayProps) {
  const [currentTime, setCurrentTime] = useState<string>("00:00:00")

  useEffect(() => {
    if (!isActive || !sessionStartTime) {
      setCurrentTime("00:00:00")
      return
    }

    const updateTime = () => {
      if (isPaused) return // Don't update time when paused
      
      const now = new Date()
      const startTime = new Date(sessionStartTime)
      const diffMs = now.getTime() - startTime.getTime()
      const diffSeconds = Math.floor(diffMs / 1000)
      
      const hours = Math.floor(diffSeconds / 3600)
      const minutes = Math.floor((diffSeconds % 3600) / 60)
      const seconds = diffSeconds % 60
      
      const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      setCurrentTime(timeString)
    }

    // Update immediately
    updateTime()

    // Update every second
    const interval = setInterval(updateTime, 1000)

    return () => clearInterval(interval)
  }, [isActive, isPaused, sessionStartTime])

  if (!isActive) {
    return null // Don't show anything when not tracking
  }

  return (
    <div className={`flex items-center gap-2 text-sm ${className}`}>
      <Clock className="w-4 h-4" />
      <span className="font-mono">
        {currentTime}
      </span>
      {isPaused && (
        <span className="text-yellow-500 text-xs">(Paused)</span>
      )}
    </div>
  )
}
