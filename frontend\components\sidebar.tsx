"use client"
import { <PERSON>, <PERSON>, CuboidIcon as <PERSON>ube, X, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>humbsUp, LogOut, Sun, Moon } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"
import { useTheme } from "@/components/theme-provider"
import { useRouter } from "next/navigation"
import { navigationEvents } from "@/lib/navigation-events"

interface SidebarProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  isLoggedIn?: boolean
  username?: string
  onLogout?: () => void
}

export function Sidebar({ isOpen, setIsOpen, isLoggedIn = false, username = "", onLogout }: SidebarProps) {
  const { theme, setTheme } = useTheme()
  const router = useRouter()

  // Determine text color based on theme
  const textColor = theme === "dark" ? "text-neutral-300" : "text-neutral-800"
  const iconColor = theme === "dark" ? "text-neutral-400" : "text-neutral-600"

  const goToHome = () => {
    navigationEvents.triggerNavigation()
    router.push("/")
    setIsOpen(false)
  }

  const handleSignIn = () => {
    navigationEvents.triggerNavigation()
    router.push("/auth")
    setIsOpen(false)
  }

  const handleLogout = () => {
    if (onLogout) {
      onLogout()
    }
    setIsOpen(false)
  }

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.5 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black z-40 md:hidden"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={{ x: "-100%" }}
        animate={{ x: isOpen ? 0 : "-100%" }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`fixed top-0 left-0 h-full w-64 border-r flex flex-col z-50 ${
          theme === "light" ? "border-black bg-white" : "border-border bg-background"
        }`}
      >
        <div className="p-4 flex items-center justify-between">
          <button className="flex items-center gap-2 hover:text-purple-500" onClick={goToHome}>
            <div className="relative h-6 w-6">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png"
                alt="Cognimosity Logo"
                fill
                className="object-contain"
              />
            </div>
            <span className="font-semibold">Cognimosity</span>
          </button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setIsOpen(false)}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Theme Toggle */}
        <div className="px-3 py-2">
          <Button
            variant="ghost"
            className={`w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`}
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          >
            {theme === "dark" ? (
              <>
                <Sun className={`h-4 w-4 mr-2 ${iconColor}`} />
                Light Mode
              </>
            ) : (
              <>
                <Moon className={`h-4 w-4 mr-2 ${iconColor}`} />
                Dark Mode
              </>
            )}
          </Button>
        </div>

        {/* History Section */}
        <div className="px-3 py-2">
          <div className={`w-full px-4 py-2 ${textColor} font-medium`}>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>History</span>
            </div>
          </div>
          <div className="pl-6 space-y-1">
            <div className="text-sm text-neutral-500 py-2">No recent history</div>
          </div>
        </div>

        {/* Spaces Section */}
        <div className="px-3 py-2">
          <div className={`w-full px-4 py-2 ${textColor} font-medium`}>
            <div className="flex items-center">
              <Cube className="h-4 w-4 mr-2" />
              <span>Spaces</span>
            </div>
          </div>
          <div className="pl-6 space-y-1">
            <Button
              variant="ghost"
              className={`w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`}
            >
              <Cube className={`h-4 w-4 mr-2 ${iconColor}`} />
              My Space
            </Button>
            <Button
              variant="outline"
              className={`w-full justify-start gap-2 border-dashed border-neutral-700 text-sm ${textColor} hover:border-purple-500 hover:text-purple-500`}
            >
              <Plus className="h-4 w-4" />
              Add space
            </Button>
          </div>
        </div>

        {/* Help & Tools Section */}
        <div className="px-3 py-2">
          <div className={`w-full px-4 py-2 ${textColor} font-medium`}>
            <div className="flex items-center">
              <BookOpen className="h-4 w-4 mr-2" />
              <span>Help & Tools</span>
            </div>
          </div>
          <div className="pl-6 space-y-1">
            <Button
              variant="ghost"
              className={`w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`}
            >
              <ThumbsUp className={`h-4 w-4 mr-2 ${iconColor}`} />
              Feedback
            </Button>
            <Button
              variant="ghost"
              className={`w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`}
            >
              <BookOpen className={`h-4 w-4 mr-2 ${iconColor}`} />
              Quick Guide
            </Button>
            <Button
              variant="ghost"
              className={`w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`}
            >
              <Users className={`h-4 w-4 mr-2 ${iconColor}`} />
              Invite & Earn
            </Button>
          </div>
        </div>

        <div className="mt-auto p-3 space-y-3">
          <Button
            variant="outline"
            className={`w-full justify-center text-purple-500 hover:bg-purple-500/10 ${
              theme === "light" ? "border-purple-500" : "border-purple-500"
            }`}
          >
            Upgrade
          </Button>

          <div
            className={`rounded-md p-2 text-center ${
              theme === "light" ? "bg-white border border-black" : "bg-neutral-900"
            }`}
          >
            {isLoggedIn ? (
              <Button
                variant="ghost"
                className={`w-full justify-center text-sm ${textColor} hover:text-purple-500`}
                onClick={handleLogout}
              >
                <LogOut className={`h-4 w-4 mr-2 ${iconColor}`} />
                Log out ({username})
              </Button>
            ) : (
              <Button
                variant="ghost"
                className={`w-full justify-center text-sm ${textColor} hover:text-purple-500`}
                onClick={handleSignIn}
              >
                <Users className={`h-4 w-4 mr-2 ${iconColor}`} />
                Sign in / Sign up
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </>
  )
}
