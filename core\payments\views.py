import razorpay
import json
import logging
from django.conf import settings
from django.utils import timezone
from rest_framework import status, viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from .models import Payment, Subscription, SubscriptionPlan
from .serializers import (
    PaymentSerializer, PaymentCreateSerializer, PaymentVerifySerializer,
    SubscriptionSerializer, SubscriptionPlanSerializer
)
import hmac
import hashlib

# Initialize Razorpay client
client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))

# Configure logging
logger = logging.getLogger(__name__)

class SubscriptionPlanViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = SubscriptionPlan.objects.filter(is_active=True)
    serializer_class = SubscriptionPlanSerializer

class SubscriptionViewSet(viewsets.ModelViewSet):
    serializer_class = SubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Subscription.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        subscription = self.get_object()
        subscription.status = 'cancelled'
        subscription.auto_renew = False
        subscription.save()
        return Response({'status': 'Subscription cancelled'})

class PaymentViewSet(viewsets.ModelViewSet):
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Payment.objects.filter(user=self.request.user)

    def create(self, request):
        serializer = PaymentCreateSerializer(data=request.data)
        if serializer.is_valid():
            amount = int(serializer.validated_data['amount'] * 100)  # Convert to paise
            plan = serializer.validated_data.get('plan_id')

            try:
                # Create Razorpay order
                order_data = {
                    'amount': amount,
                    'currency': 'INR',
                    'payment_capture': 1
                }
                order = client.order.create(data=order_data)

                # Create payment record
                payment = Payment.objects.create(
                    user=request.user,
                    amount=serializer.validated_data['amount'],
                    razorpay_order_id=order['id'],
                    status='pending'
                )

                # If this is a subscription payment, create subscription
                if plan:
                    subscription = Subscription.objects.create(
                        user=request.user,
                        plan=plan,
                        start_date=timezone.now(),
                        end_date=timezone.now() + timezone.timedelta(days=plan.duration_days)
                    )
                    payment.subscription = subscription
                    payment.save()

                return Response({
                    'order_id': order['id'],
                    'amount': amount,
                    'key': settings.RAZORPAY_KEY_ID
                }, status=status.HTTP_201_CREATED)

            except Exception as e:
                logger.error(f"Error creating payment: {str(e)}")
                return Response(
                    {'error': 'Error creating payment'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def verify(self, request):
        serializer = PaymentVerifySerializer(data=request.data)
        if serializer.is_valid():
            try:
                payment = Payment.objects.get(
                    razorpay_order_id=serializer.validated_data['razorpay_order_id'],
                    user=request.user
                )

                # Verify the payment signature
                params_dict = {
                    'razorpay_order_id': serializer.validated_data['razorpay_order_id'],
                    'razorpay_payment_id': serializer.validated_data['razorpay_payment_id'],
                    'razorpay_signature': serializer.validated_data['razorpay_signature']
                }

                client.utility.verify_payment_signature(params_dict)

                # Update payment record
                payment.razorpay_payment_id = serializer.validated_data['razorpay_payment_id']
                payment.razorpay_signature = serializer.validated_data['razorpay_signature']
                payment.status = 'completed'
                payment.save()

                # Generate receipt
                payment.generate_receipt()

                return Response({'status': 'Payment successful'})

            except Payment.DoesNotExist:
                return Response(
                    {'error': 'Payment not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
            except Exception as e:
                logger.error(f"Error verifying payment: {str(e)}")
                return Response(
                    {'error': 'Invalid payment signature'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RazorpayWebhookView(APIView):
    def post(self, request):
        try:
            # Verify webhook signature
            received_signature = request.headers.get('X-Razorpay-Signature')
            webhook_secret = settings.RAZORPAY_WEBHOOK_SECRET
            payload = request.body

            # Create HMAC SHA256 signature
            expected_signature = hmac.new(
                webhook_secret.encode(),
                payload,
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(received_signature, expected_signature):
                return Response(
                    {'error': 'Invalid signature'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Process webhook event
            event = json.loads(payload)
            event_type = event.get('event')

            if event_type == 'payment.captured':
                payment_id = event['payload']['payment']['entity']['id']
                order_id = event['payload']['payment']['entity']['order_id']

                try:
                    payment = Payment.objects.get(
                        razorpay_order_id=order_id,
                        razorpay_payment_id=payment_id
                    )
                    payment.status = 'completed'
                    payment.save()

                    # If this is a subscription payment, activate the subscription
                    if payment.subscription:
                        payment.subscription.status = 'active'
                        payment.subscription.save()

                except Payment.DoesNotExist:
                    logger.error(f"Payment not found: {payment_id}")
                    return Response(
                        {'error': 'Payment not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )

            return Response({'status': 'success'})

        except Exception as e:
            logger.error(f"Webhook error: {str(e)}")
            return Response(
                {'error': 'Webhook processing failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 