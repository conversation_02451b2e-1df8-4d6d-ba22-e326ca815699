# Generated by Django 4.2.21 on 2025-07-18 07:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('documents', '0013_documentlearningtime'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuizAttempt',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField(auto_now_add=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('score', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_attempts', to='documents.document')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_attempts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='QuizAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_answer', models.TextField()),
                ('is_correct', models.BooleanField(default=False)),
                ('feedback', models.TextField(blank=True, help_text='AI-generated feedback on the answer', null=True)),
                ('quiz_attempt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='documents.quizattempt')),
                ('quiz_question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_answers', to='documents.quiz')),
            ],
            options={
                'ordering': ['quiz_question_id'],
            },
        ),
        migrations.AddIndex(
            model_name='quizattempt',
            index=models.Index(fields=['user', 'document'], name='documents_q_user_id_283e66_idx'),
        ),
        migrations.AddIndex(
            model_name='quizattempt',
            index=models.Index(fields=['start_time'], name='documents_q_start_t_5299d8_idx'),
        ),
        migrations.AddIndex(
            model_name='quizanswer',
            index=models.Index(fields=['quiz_attempt', 'quiz_question'], name='documents_q_quiz_at_7256e7_idx'),
        ),
        migrations.AddIndex(
            model_name='quizanswer',
            index=models.Index(fields=['is_correct'], name='documents_q_is_corr_4c07f2_idx'),
        ),
    ]
