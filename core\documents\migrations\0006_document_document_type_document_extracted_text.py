# Generated by Django 4.2.20 on 2025-05-07 07:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0005_document_error_message_document_processing_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="document",
            name="document_type",
            field=models.CharField(
                choices=[
                    ("general", "General"),
                    ("blueprint", "Blueprint"),
                    ("pattern", "Pattern"),
                ],
                default="general",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="document",
            name="extracted_text",
            field=models.TextField(
                blank=True,
                help_text="Extracted text content from the document",
                null=True,
            ),
        ),
    ]
