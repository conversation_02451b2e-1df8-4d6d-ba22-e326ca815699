'use client';

import React, { useState } from 'react';
import { Clock, Play, Pause, Square, BarChart3, Timer } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useTimeTracking, TimeTrackingSession, TimeTrackingStats } from '@/hooks/use-time-tracking';

interface TimeTrackingWidgetProps {
  documentId: number;
  documentTitle?: string;
  className?: string;
  compact?: boolean;
  showStats?: boolean;
  onQuizStart?: () => void;
  onQuizEnd?: () => void;
}

export const TimeTrackingWidget: React.FC<TimeTrackingWidgetProps> = ({
  documentId,
  documentTitle,
  className = '',
  compact = false,
  showStats = true,
  onQuizStart,
  onQuizEnd,
}) => {
  const [showStatsPanel, setShowStatsPanel] = useState(false);

  const {
    session,
    stats,
    isTracking,
    isPaused,
    isLoading,
    error,
    startTracking,
    pauseTracking,
    resumeTracking,
    stopTracking,
    getDocumentStats,
    isActive,
    totalTime,
  } = useTimeTracking({
    documentId,
    autoStart: true,
    onSessionStart: (session: TimeTrackingSession) => {
      console.log('Time tracking started:', session);
    },
    onSessionEnd: (session: TimeTrackingSession, stats: TimeTrackingStats) => {
      console.log('Time tracking ended:', session, stats);
    },
    onSessionPause: (session: TimeTrackingSession) => {
      console.log('Time tracking paused:', session);
      onQuizStart?.();
    },
    onSessionResume: (session: TimeTrackingSession) => {
      console.log('Time tracking resumed:', session);
      onQuizEnd?.();
    },
    onError: (error) => {
      console.error('Time tracking error:', error);
    },
  });

  const handleStartStop = async () => {
    try {
      if (isTracking) {
        await stopTracking();
      } else {
        await startTracking();
      }
    } catch (error) {
      console.error('Error toggling tracking:', error);
    }
  };

  const handlePauseResume = async () => {
    try {
      if (isPaused) {
        await resumeTracking();
      } else {
        await pauseTracking('Manual pause');
      }
    } catch (error) {
      console.error('Error toggling pause:', error);
    }
  };

  const getStatusColor = () => {
    if (!isTracking) return 'secondary';
    if (isPaused) return 'warning';
    return 'default';
  };

  const getStatusText = () => {
    if (!isTracking) return 'Not tracking';
    if (isPaused) return 'Paused';
    return 'Active';
  };

  if (compact) {
    return (
      <TooltipProvider>
        <div className={`flex items-center gap-2 ${className}`}>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 text-sm">
                <Timer className="h-4 w-4" />
                <span className="font-mono">{totalTime}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Study time: {totalTime}</p>
              <p>Status: {getStatusText()}</p>
            </TooltipContent>
          </Tooltip>
          
          <Badge variant={getStatusColor()} className="text-xs">
            {getStatusText()}
          </Badge>
          
          {isTracking && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handlePauseResume}
              disabled={isLoading}
              className="h-6 w-6 p-0"
            >
              {isPaused ? <Play className="h-3 w-3" /> : <Pause className="h-3 w-3" />}
            </Button>
          )}
        </div>
      </TooltipProvider>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-lg">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            <span>Study Timer</span>
          </div>
          {showStats && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowStatsPanel(!showStatsPanel)}
              className="h-8 w-8 p-0"
            >
              <BarChart3 className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
        {documentTitle && (
          <p className="text-sm text-muted-foreground truncate">{documentTitle}</p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Timer Display */}
        <div className="text-center">
          <div className="text-3xl font-mono font-bold">{totalTime}</div>
          <Badge variant={getStatusColor()} className="mt-2">
            {getStatusText()}
          </Badge>
        </div>

        {/* Control Buttons */}
        <div className="flex gap-2 justify-center">
          <Button
            onClick={handleStartStop}
            disabled={isLoading}
            variant={isTracking ? "destructive" : "default"}
            size="sm"
          >
            {isTracking ? (
              <>
                <Square className="h-4 w-4 mr-2" />
                Stop
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Start
              </>
            )}
          </Button>
          
          {isTracking && (
            <Button
              onClick={handlePauseResume}
              disabled={isLoading}
              variant="outline"
              size="sm"
            >
              {isPaused ? (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Resume
                </>
              ) : (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Pause
                </>
              )}
            </Button>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
            {error}
          </div>
        )}

        {/* Stats Panel */}
        {showStatsPanel && stats && (
          <div className="border-t pt-4 space-y-2">
            <h4 className="font-semibold text-sm">Session Statistics</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-muted-foreground">Total Study Time:</span>
                <div className="font-mono">{stats.total_study_time_formatted}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Sessions:</span>
                <div>{stats.total_sessions}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Avg Session:</span>
                <div className="font-mono">{stats.average_session_duration_formatted}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Views:</span>
                <div>{stats.view_count}</div>
              </div>
            </div>
            {stats.reopened_at_least_once && (
              <Badge variant="secondary" className="text-xs">
                Reopened at least once
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Export a simple timer display component for minimal UI
export const SimpleTimer: React.FC<{ documentId: number; className?: string }> = ({
  documentId,
  className = '',
}) => {
  const { totalTime, isActive } = useTimeTracking({
    documentId,
    autoStart: true,
  });

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Timer className={`h-4 w-4 ${isActive ? 'text-green-600' : 'text-gray-400'}`} />
      <span className="font-mono text-sm">{totalTime}</span>
    </div>
  );
};
