from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from users.models import DocumentTimeTracking
import json

User = get_user_model()

class OptimizedTimerTestCase(TransactionTestCase):
    """Test cases for the optimized timer system"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
        
        # Clear cache before each test
        cache.clear()
        
        self.file_name = 'test_document.pdf'

    def tearDown(self):
        # Clear cache after each test
        cache.clear()

    def test_optimized_timer_start(self):
        """Test starting a timer with caching"""
        response = self.client.post('/users/timer/start/', {
            'file_name': self.file_name
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('session_start', data)
        self.assertIn('tracking_id', data)
        
        # Check that tracking record was created
        tracking = DocumentTimeTracking.objects.get(
            username=self.user.username,
            file_name=self.file_name
        )
        self.assertEqual(tracking.total_time_seconds, 0)
        self.assertEqual(tracking.number_of_sessions, 0)
        
        # Check that session is cached
        session_cache_key = f"timer_session_{self.user.username}_{self.file_name}"
        cached_session = cache.get(session_cache_key)
        self.assertIsNotNone(cached_session)
        self.assertTrue(cached_session['is_active'])

    def test_optimized_timer_stop(self):
        """Test stopping a timer with atomic updates"""
        # Start timer first
        start_response = self.client.post('/users/timer/start/', {
            'file_name': self.file_name
        })
        self.assertEqual(start_response.status_code, 200)

        # Stop timer with session time
        response = self.client.post('/users/timer/stop/', {
            'file_name': self.file_name,
            'session_time_seconds': 120
        })

        if response.status_code != 200:
            print(f"Error response: {response.content}")
            print(f"Status code: {response.status_code}")

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['total_time_seconds'], 120)
        self.assertEqual(data['number_of_sessions'], 1)

        # Check database was updated
        tracking = DocumentTimeTracking.objects.get(
            username=self.user.username,
            file_name=self.file_name
        )
        self.assertEqual(tracking.total_time_seconds, 120)
        self.assertEqual(tracking.number_of_sessions, 1)

        # Check that session cache was cleared
        session_cache_key = f"timer_session_{self.user.username}_{self.file_name}"
        cached_session = cache.get(session_cache_key)
        self.assertIsNone(cached_session)

    def test_heartbeat_functionality(self):
        """Test heartbeat endpoint"""
        # Start timer first
        self.client.post('/users/timer/start/', {
            'file_name': self.file_name
        })
        
        # Send heartbeat
        response = self.client.post('/users/timer/heartbeat/', {
            'file_name': self.file_name
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertTrue(data['session_active'])
        
        # Test heartbeat without active session
        cache.clear()
        response = self.client.post('/users/timer/heartbeat/', {
            'file_name': 'nonexistent.pdf'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertFalse(data['session_active'])

    def test_batch_update_sessions(self):
        """Test batch updating multiple sessions"""
        # Create multiple tracking records
        file1 = 'doc1.pdf'
        file2 = 'doc2.pdf'
        
        DocumentTimeTracking.get_or_create_tracking(self.user.username, file1)
        DocumentTimeTracking.get_or_create_tracking(self.user.username, file2)
        
        # Batch update
        sessions = [
            {'file_name': file1, 'time_seconds': 60},
            {'file_name': file2, 'time_seconds': 90},
            {'file_name': file1, 'time_seconds': 30}  # Additional time for file1
        ]
        
        response = self.client.post('/users/timer/batch-update/', {
            'sessions': sessions
        }, format='json')

        if response.status_code != 200:
            print(f"Batch update error: {response.content}")
            print(f"Status code: {response.status_code}")

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['updated_sessions'], 3)
        
        # Check database updates
        tracking1 = DocumentTimeTracking.objects.get(
            username=self.user.username,
            file_name=file1
        )
        tracking2 = DocumentTimeTracking.objects.get(
            username=self.user.username,
            file_name=file2
        )
        
        self.assertEqual(tracking1.total_time_seconds, 90)  # 60 + 30
        self.assertEqual(tracking1.number_of_sessions, 1)   # Grouped into one session
        self.assertEqual(tracking2.total_time_seconds, 90)
        self.assertEqual(tracking2.number_of_sessions, 1)

    def test_atomic_updates_race_condition(self):
        """Test that atomic updates prevent race conditions"""
        tracking = DocumentTimeTracking.get_or_create_tracking(
            self.user.username, 
            self.file_name
        )
        
        # Simulate concurrent updates
        tracking.add_session_time(60)
        tracking.add_session_time(30)
        
        # Refresh from database
        tracking.refresh_from_db()
        
        self.assertEqual(tracking.total_time_seconds, 90)
        self.assertEqual(tracking.number_of_sessions, 2)

    def test_caching_efficiency(self):
        """Test that caching reduces database queries"""
        # First call should create cache entry
        tracking1 = DocumentTimeTracking.get_or_create_tracking(
            self.user.username, 
            self.file_name
        )
        
        # Second call should use cache
        tracking2 = DocumentTimeTracking.get_or_create_tracking(
            self.user.username, 
            self.file_name
        )
        
        self.assertEqual(tracking1.id, tracking2.id)
        
        # Check cache exists
        cache_key = f"doc_tracking_{self.user.username}_{self.file_name}"
        cached_data = cache.get(cache_key)
        self.assertIsNotNone(cached_data)
        self.assertEqual(cached_data['id'], tracking1.id)

    def test_quiz_count_increment(self):
        """Test quiz count increment functionality"""
        # Create tracking record
        DocumentTimeTracking.get_or_create_tracking(self.user.username, self.file_name)
        
        # Increment quiz count
        response = self.client.post('/users/timer/quiz/', {
            'file_name': self.file_name
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['number_of_quizzes'], 1)
        
        # Check database
        tracking = DocumentTimeTracking.objects.get(
            username=self.user.username,
            file_name=self.file_name
        )
        self.assertEqual(tracking.number_of_quizzes, 1)

    def test_error_handling(self):
        """Test error handling for various scenarios"""
        # Test missing file_name
        response = self.client.post('/users/timer/start/', {})
        self.assertEqual(response.status_code, 400)
        
        # Test stopping non-existent timer
        response = self.client.post('/users/timer/stop/', {
            'file_name': 'nonexistent.pdf',
            'session_time_seconds': 60
        })
        self.assertEqual(response.status_code, 404)
        
        # Test batch update with empty sessions
        response = self.client.post('/users/timer/batch-update/', {
            'sessions': []
        })
        self.assertEqual(response.status_code, 400)

    def test_stats_endpoint(self):
        """Test the stats endpoint functionality"""
        # Create some test data
        tracking = DocumentTimeTracking.get_or_create_tracking(
            self.user.username, 
            self.file_name
        )
        tracking.add_session_time(120)
        tracking.increment_quiz_count()
        
        # Test specific file stats
        response = self.client.get('/users/timer/stats/', {
            'file_name': self.file_name
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['total_time_seconds'], 120)
        self.assertEqual(data['number_of_sessions'], 1)
        self.assertEqual(data['number_of_quizzes'], 1)
        
        # Test all files stats
        response = self.client.get('/users/timer/stats/')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('files', data)
        self.assertIn('summary', data)
        self.assertEqual(data['summary']['total_files'], 1)
        self.assertEqual(data['summary']['total_time_seconds'], 120)
