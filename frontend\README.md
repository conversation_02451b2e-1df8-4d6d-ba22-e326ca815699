# Cognimosity Frontend

A Next.js-based frontend application for the Cognimosity learning platform.

## Features

- **Modern UI**: Built with Next.js 15, React 18, and Tailwind CSS
- **Component Library**: Radix UI components with custom styling
- **Authentication**: User registration, login, and OTP verification
- **Document Management**: Upload, view, and process learning materials
- **Interactive Learning**: Chat interface, quizzes, flashcards, and more
- **Performance Tracking**: Student performance analytics and dashboards
- **Responsive Design**: Mobile-first approach with dark/light theme support

## Tech Stack

- **Framework**: Next.js 15.2.4
- **UI Library**: Radix UI + Tailwind CSS
- **State Management**: React hooks
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Charts**: Recharts
- **Animations**: Framer Motion

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up environment variables:
```bash
cp .env.example .env.local
```

3. Update the API URL in `.env.local`:
```
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

### Development

Start the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### Build

Create a production build:
```bash
npm run build
```

Start the production server:
```bash
npm start
```

## Project Structure

```
frontend/
├── app/                    # Next.js app directory
│   ├── auth/              # Authentication pages
│   ├── performance/       # Performance dashboard
│   ├── process/           # Learning interface
│   └── subscription/      # Subscription management
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   └── *.tsx             # Feature components
├── lib/                  # Utilities and API
├── public/               # Static assets
└── styles/               # Global styles
```

## API Integration

The frontend communicates with the Django/FastAPI backend through:

- **Authentication API**: User management and sessions
- **Document API**: File upload and processing
- **Chat API**: AI-powered conversations
- **Performance API**: Analytics and tracking
- **Learning APIs**: Quizzes, flashcards, blueprints

## Recent Fixes

- ✅ Fixed mermaid dependency issues
- ✅ Resolved TypeScript errors in ChatInterface
- ✅ Removed duplicate CSS files
- ✅ Added proper environment configuration
- ✅ Fixed component prop interfaces
- ✅ Cleaned up unused imports and variables
- ✅ Updated package.json with consistent versioning

## Contributing

1. Follow the existing code style
2. Use TypeScript for type safety
3. Test components thoroughly
4. Update documentation as needed
