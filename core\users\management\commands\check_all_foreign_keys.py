from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Check all foreign key constraints that reference documents'

    def handle(self, *args, **options):
        self.stdout.write("🔍 Checking ALL foreign key constraints that reference documents...")
        self.stdout.write("=" * 70)
        
        with connection.cursor() as cursor:
            # Get all tables
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name;
            """)
            all_tables = [table[0] for table in cursor.fetchall()]
            
            # Check each table for foreign keys to documents_document
            document_referencing_tables = []
            
            for table in all_tables:
                try:
                    cursor.execute(f"PRAGMA foreign_key_list({table});")
                    fks = cursor.fetchall()
                    
                    for fk in fks:
                        # fk structure: [id, seq, table, from, to, on_update, on_delete, match]
                        if fk[2] == 'documents_document':  # References documents_document
                            document_referencing_tables.append({
                                'table': table,
                                'column': fk[3],
                                'references': f"{fk[2]}.{fk[4]}",
                                'on_delete': fk[6]
                            })
                except Exception as e:
                    # Skip tables that can't be queried
                    continue
            
            if document_referencing_tables:
                self.stdout.write("📋 Tables with foreign keys to documents_document:")
                for ref in document_referencing_tables:
                    status = "✅" if ref['on_delete'] == 'CASCADE' else "❌"
                    self.stdout.write(f"   {status} {ref['table']}.{ref['column']} -> {ref['references']} (on_delete: {ref['on_delete']})")
                
                # Check for problematic constraints
                problematic = [ref for ref in document_referencing_tables if ref['on_delete'] != 'CASCADE']
                if problematic:
                    self.stdout.write(f"\n⚠️  Found {len(problematic)} tables with non-CASCADE constraints:")
                    for ref in problematic:
                        self.stdout.write(f"   - {ref['table']}.{ref['column']} (on_delete: {ref['on_delete']})")
                    
                    self.stdout.write("\n💡 These tables may cause foreign key constraint errors when deleting documents.")
                    self.stdout.write("   Consider updating their constraints to CASCADE or manually handling deletion.")
                else:
                    self.stdout.write("\n✅ All foreign key constraints use CASCADE deletion!")
            else:
                self.stdout.write("📋 No tables found with foreign keys to documents_document")
            
            # Also check for any tables that might have document_id columns without proper foreign keys
            self.stdout.write(f"\n🔍 Checking for tables with document_id columns...")
            
            tables_with_document_id = []
            for table in all_tables:
                try:
                    cursor.execute(f"PRAGMA table_info({table});")
                    columns = cursor.fetchall()
                    
                    for col in columns:
                        if 'document_id' in col[1].lower():
                            # Check if this column has a foreign key
                            cursor.execute(f"PRAGMA foreign_key_list({table});")
                            fks = cursor.fetchall()
                            has_fk = any(fk[3] == col[1] for fk in fks)
                            
                            tables_with_document_id.append({
                                'table': table,
                                'column': col[1],
                                'has_foreign_key': has_fk
                            })
                except Exception:
                    continue
            
            if tables_with_document_id:
                self.stdout.write("📋 Tables with document_id columns:")
                for item in tables_with_document_id:
                    status = "✅" if item['has_foreign_key'] else "⚠️ "
                    fk_status = "with FK" if item['has_foreign_key'] else "NO FK"
                    self.stdout.write(f"   {status} {item['table']}.{item['column']} ({fk_status})")
                
                # Check for columns without foreign keys
                no_fk = [item for item in tables_with_document_id if not item['has_foreign_key']]
                if no_fk:
                    self.stdout.write(f"\n⚠️  Found {len(no_fk)} document_id columns without foreign keys:")
                    for item in no_fk:
                        self.stdout.write(f"   - {item['table']}.{item['column']}")
                    self.stdout.write("   These might cause orphaned records but won't cause FK constraint errors.")
        
        self.stdout.write("\n🎉 Foreign key analysis completed!")
