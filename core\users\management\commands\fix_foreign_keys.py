from django.core.management.base import BaseCommand
from django.db import connection, transaction
from users.models import PlatformTimeSession, PlatformTimeStats, PlatformTimeEvent, StudentPerformance
from documents.models import (
    DocumentEmbedding, Flashcard, Flowchart, Quiz, BlueprintTopics
)

class Command(BaseCommand):
    help = 'Fix foreign key constraints to use CASCADE deletion'

    def add_arguments(self, parser):
        parser.add_argument(
            '--apply',
            action='store_true',
            help='Actually apply the fixes (default is dry run)',
        )

    def handle(self, *args, **options):
        apply_fixes = options['apply']
        
        if not apply_fixes:
            self.stdout.write("🔍 DRY RUN MODE - No changes will be made")
            self.stdout.write("   Use --apply flag to actually fix the constraints")
        
        self.stdout.write("🔧 Fixing foreign key constraints for CASCADE deletion...")
        self.stdout.write("=" * 60)
        
        with connection.cursor() as cursor:
            # Check current constraints
            self.stdout.write("📋 Current foreign key constraints:")
            
            # Check PlatformTimeSession constraints
            cursor.execute("PRAGMA foreign_key_list(users_platformtimesession);")
            session_fks = cursor.fetchall()
            for fk in session_fks:
                self.stdout.write(f"   - users_platformtimesession.{fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[5]})")
            
            # Check PlatformTimeStats constraints  
            cursor.execute("PRAGMA foreign_key_list(users_platformtimestats);")
            stats_fks = cursor.fetchall()
            for fk in stats_fks:
                self.stdout.write(f"   - users_platformtimestats.{fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[5]})")
            
            # Check PlatformTimeEvent constraints
            cursor.execute("PRAGMA foreign_key_list(users_platformtimeevent);")
            event_fks = cursor.fetchall()
            for fk in event_fks:
                self.stdout.write(f"   - users_platformtimeevent.{fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[5]})")
        
        if not apply_fixes:
            self.stdout.write("\n💡 To fix these constraints, run:")
            self.stdout.write("   python manage.py fix_foreign_keys --apply")
            return
        
        # Apply the fixes
        self.stdout.write("\n🔨 Applying fixes...")
        
        try:
            with transaction.atomic():
                # Backup existing data
                self.stdout.write("📦 Backing up existing data...")

                # Get all existing data
                sessions_data = list(PlatformTimeSession.objects.all().values())
                stats_data = list(PlatformTimeStats.objects.all().values())
                events_data = list(PlatformTimeEvent.objects.all().values())
                performance_data = list(StudentPerformance.objects.all().values())

                # Documents app data
                embeddings_data = list(DocumentEmbedding.objects.all().values())
                flashcards_data = list(Flashcard.objects.all().values())
                flowcharts_data = list(Flowchart.objects.all().values())
                quizzes_data = list(Quiz.objects.all().values())
                blueprint_topics_data = list(BlueprintTopics.objects.all().values())

                # Initialize variables for tables without Django models
                documenttimer_data = []
                summaryinteraction_data = []
                summarytracking_data = []
                timersession_data = []

                self.stdout.write(f"   - {len(sessions_data)} session records")
                self.stdout.write(f"   - {len(stats_data)} stats records")
                self.stdout.write(f"   - {len(events_data)} event records")
                self.stdout.write(f"   - {len(performance_data)} performance records")
                self.stdout.write(f"   - {len(embeddings_data)} embedding records")
                self.stdout.write(f"   - {len(flashcards_data)} flashcard records")
                self.stdout.write(f"   - {len(flowcharts_data)} flowchart records")
                self.stdout.write(f"   - {len(quizzes_data)} quiz records")
                self.stdout.write(f"   - {len(blueprint_topics_data)} blueprint topic records")
                
                # Disable SQL debugging to avoid formatting issues
                from django.conf import settings
                old_debug = settings.DEBUG
                settings.DEBUG = False

                with connection.cursor() as cursor:
                    # Backup tables without Django models (raw SQL)
                    cursor.execute("SELECT * FROM documents_documenttimer;")
                    documenttimer_data = cursor.fetchall()
                    cursor.execute("SELECT * FROM documents_summaryinteraction;")
                    summaryinteraction_data = cursor.fetchall()
                    cursor.execute("SELECT * FROM documents_summarytracking;")
                    summarytracking_data = cursor.fetchall()
                    cursor.execute("SELECT * FROM documents_timersession;")
                    timersession_data = cursor.fetchall()

                    # Disable foreign key checks temporarily
                    cursor.execute("PRAGMA foreign_keys = OFF;")
                    
                    # Drop and recreate tables with proper constraints
                    self.stdout.write("🗑️  Dropping existing tables...")

                    # Drop tables in correct order (events first, then sessions, then stats)
                    cursor.execute("DROP TABLE IF EXISTS users_platformtimeevent;")
                    cursor.execute("DROP TABLE IF EXISTS users_platformtimesession;")
                    cursor.execute("DROP TABLE IF EXISTS users_platformtimestats;")
                    cursor.execute("DROP TABLE IF EXISTS users_studentperformance;")

                    # Drop documents app tables
                    cursor.execute("DROP TABLE IF EXISTS documents_documentembedding;")
                    cursor.execute("DROP TABLE IF EXISTS documents_flashcard;")
                    cursor.execute("DROP TABLE IF EXISTS documents_flowchart;")
                    cursor.execute("DROP TABLE IF EXISTS documents_quiz;")
                    cursor.execute("DROP TABLE IF EXISTS documents_blueprinttopics;")
                    cursor.execute("DROP TABLE IF EXISTS documents_documenttimer;")
                    cursor.execute("DROP TABLE IF EXISTS documents_summaryinteraction;")
                    cursor.execute("DROP TABLE IF EXISTS documents_summarytracking;")
                    cursor.execute("DROP TABLE IF EXISTS documents_timersession;")
                    
                    # Recreate tables with proper CASCADE constraints
                    self.stdout.write("🏗️  Recreating tables with CASCADE constraints...")
                    
                    # Create PlatformTimeSession table
                    cursor.execute("""
                        CREATE TABLE users_platformtimesession (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            session_start DATETIME NOT NULL,
                            session_end DATETIME,
                            total_time_seconds INTEGER NOT NULL DEFAULT 0,
                            is_active BOOLEAN NOT NULL DEFAULT 1,
                            is_paused BOOLEAN NOT NULL DEFAULT 0,
                            pause_reason VARCHAR(50),
                            last_activity DATETIME NOT NULL,
                            created_at DATETIME NOT NULL,
                            student_id BIGINT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (student_id) REFERENCES users_student(id) ON DELETE CASCADE,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)
                    
                    # Create PlatformTimeStats table
                    cursor.execute("""
                        CREATE TABLE users_platformtimestats (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            total_time_seconds INTEGER NOT NULL DEFAULT 0,
                            total_sessions INTEGER NOT NULL DEFAULT 0,
                            total_pauses INTEGER NOT NULL DEFAULT 0,
                            quiz_pauses INTEGER NOT NULL DEFAULT 0,
                            navigation_pauses INTEGER NOT NULL DEFAULT 0,
                            view_count INTEGER NOT NULL DEFAULT 0,
                            first_access DATETIME,
                            last_access DATETIME,
                            created_at DATETIME NOT NULL,
                            updated_at DATETIME NOT NULL,
                            student_id BIGINT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (student_id) REFERENCES users_student(id) ON DELETE CASCADE,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE,
                            UNIQUE (student_id, document_id)
                        );
                    """)
                    
                    # Create PlatformTimeEvent table
                    cursor.execute("""
                        CREATE TABLE users_platformtimeevent (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            event_type VARCHAR(20) NOT NULL,
                            reason VARCHAR(50),
                            timestamp DATETIME NOT NULL,
                            created_at DATETIME NOT NULL,
                            session_id BIGINT NOT NULL,
                            FOREIGN KEY (session_id) REFERENCES users_platformtimesession(id) ON DELETE CASCADE
                        );
                    """)

                    # Create StudentPerformance table
                    cursor.execute("""
                        CREATE TABLE users_studentperformance (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            quiz_score DECIMAL(5,2) NOT NULL DEFAULT 0.0,
                            time_taken INTEGER NOT NULL,
                            remarks TEXT,
                            created_at DATETIME NOT NULL,
                            updated_at DATETIME NOT NULL,
                            student_id BIGINT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (student_id) REFERENCES users_student(id) ON DELETE CASCADE,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create DocumentEmbedding table
                    cursor.execute("""
                        CREATE TABLE documents_documentembedding (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            text_chunk TEXT NOT NULL,
                            embedding TEXT NOT NULL,
                            chunk_number INTEGER NOT NULL,
                            created_at DATETIME NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create Flashcard table
                    cursor.execute("""
                        CREATE TABLE documents_flashcard (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            front TEXT NOT NULL,
                            back TEXT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create Flowchart table
                    cursor.execute("""
                        CREATE TABLE documents_flowchart (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            mermaid_code TEXT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create Quiz table
                    cursor.execute("""
                        CREATE TABLE documents_quiz (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            question TEXT NOT NULL,
                            answer TEXT NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create BlueprintTopics table
                    cursor.execute("""
                        CREATE TABLE documents_blueprinttopics (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            title VARCHAR(255) NOT NULL,
                            weightage DECIMAL(5,2) NOT NULL,
                            created_at DATETIME NOT NULL,
                            document_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE
                        );
                    """)

                    # Create DocumentTimer table
                    cursor.execute("""
                        CREATE TABLE documents_documenttimer (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            session_start_time DATETIME NOT NULL,
                            session_end_time DATETIME,
                            pause_time DATETIME,
                            resume_time DATETIME,
                            total_time_minutes DECIMAL NOT NULL,
                            is_active BOOLEAN NOT NULL,
                            ended_by_quiz BOOLEAN NOT NULL,
                            created_at DATETIME NOT NULL,
                            updated_at DATETIME NOT NULL,
                            document_id BIGINT NOT NULL,
                            user_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES users_student(id) ON DELETE CASCADE
                        );
                    """)

                    # Create SummaryInteraction table
                    cursor.execute("""
                        CREATE TABLE documents_summaryinteraction (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            event_type VARCHAR(50) NOT NULL,
                            event_time DATETIME NOT NULL,
                            additional_data TEXT,
                            created_at DATETIME NOT NULL,
                            document_id BIGINT NOT NULL,
                            user_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES users_student(id) ON DELETE CASCADE
                        );
                    """)

                    # Create SummaryTracking table
                    cursor.execute("""
                        CREATE TABLE documents_summarytracking (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            summary_generated_at DATETIME NOT NULL,
                            reached_end BOOLEAN NOT NULL,
                            reached_end_at DATETIME,
                            stayed_avg_time BOOLEAN NOT NULL,
                            stayed_avg_time_at DATETIME,
                            reopened_once BOOLEAN NOT NULL,
                            reopened_at DATETIME,
                            first_view_time DATETIME,
                            total_view_time_minutes DECIMAL NOT NULL,
                            predicted_avg_time_minutes DECIMAL,
                            view_count INTEGER NOT NULL,
                            document_id BIGINT NOT NULL,
                            user_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES users_student(id) ON DELETE CASCADE
                        );
                    """)

                    # Create TimerSession table
                    cursor.execute("""
                        CREATE TABLE documents_timersession (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            session_number INTEGER NOT NULL,
                            start_time DATETIME NOT NULL,
                            end_time DATETIME,
                            pause_time DATETIME,
                            resume_time DATETIME,
                            total_minutes DECIMAL NOT NULL,
                            ended_by_quiz BOOLEAN NOT NULL,
                            created_at DATETIME NOT NULL,
                            document_id BIGINT NOT NULL,
                            user_id BIGINT NOT NULL,
                            FOREIGN KEY (document_id) REFERENCES documents_document(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES users_student(id) ON DELETE CASCADE
                        );
                    """)
                    
                    # Create indexes
                    self.stdout.write("📊 Creating indexes...")
                    cursor.execute("CREATE INDEX users_platf_student_7e9574_idx ON users_platformtimesession(student_id, document_id);")
                    cursor.execute("CREATE INDEX users_platf_session_2f6e60_idx ON users_platformtimesession(session_start);")
                    cursor.execute("CREATE INDEX users_platf_last_ac_d6da23_idx ON users_platformtimesession(last_activity);")

                    cursor.execute("CREATE INDEX users_platf_student_a71a97_idx ON users_platformtimestats(student_id, document_id);")
                    cursor.execute("CREATE INDEX users_platf_total_t_1d632c_idx ON users_platformtimestats(total_time_seconds);")
                    cursor.execute("CREATE INDEX users_platf_last_ac_b3d5a8_idx ON users_platformtimestats(last_access);")

                    cursor.execute("CREATE INDEX users_platf_session_5d4389_idx ON users_platformtimeevent(session_id, event_type);")
                    cursor.execute("CREATE INDEX users_platf_timesta_dd8565_idx ON users_platformtimeevent(timestamp);")

                    # StudentPerformance indexes
                    cursor.execute("CREATE INDEX users_studentperformance_student_id_39e22420 ON users_studentperformance(student_id);")
                    cursor.execute("CREATE INDEX users_studentperformance_document_id_39e22420 ON users_studentperformance(document_id);")

                    # DocumentEmbedding indexes
                    cursor.execute("CREATE INDEX documents_d_documen_895782_idx ON documents_documentembedding(document_id, chunk_number);")

                    # Other document tables indexes
                    cursor.execute("CREATE INDEX documents_flashcard_document_id_7668ca94 ON documents_flashcard(document_id);")
                    cursor.execute("CREATE INDEX documents_flowchart_document_id_e920a492 ON documents_flowchart(document_id);")
                    cursor.execute("CREATE INDEX documents_quiz_document_id_b8de38d1 ON documents_quiz(document_id);")
                    cursor.execute("CREATE INDEX documents_blueprinttopics_document_id_idx ON documents_blueprinttopics(document_id);")
                    cursor.execute("CREATE INDEX documents_documenttimer_document_id_idx ON documents_documenttimer(document_id);")
                    cursor.execute("CREATE INDEX documents_documenttimer_user_id_idx ON documents_documenttimer(user_id);")
                    cursor.execute("CREATE INDEX documents_summaryinteraction_document_id_idx ON documents_summaryinteraction(document_id);")
                    cursor.execute("CREATE INDEX documents_summaryinteraction_user_id_idx ON documents_summaryinteraction(user_id);")
                    cursor.execute("CREATE INDEX documents_summarytracking_document_id_idx ON documents_summarytracking(document_id);")
                    cursor.execute("CREATE INDEX documents_summarytracking_user_id_idx ON documents_summarytracking(user_id);")
                    cursor.execute("CREATE INDEX documents_timersession_document_id_idx ON documents_timersession(document_id);")
                    cursor.execute("CREATE INDEX documents_timersession_user_id_idx ON documents_timersession(user_id);")
                    
                    # Re-enable foreign key checks
                    cursor.execute("PRAGMA foreign_keys = ON;")

                    # Restore data if any existed
                    if (sessions_data or stats_data or events_data or performance_data or
                        embeddings_data or flashcards_data or flowcharts_data or quizzes_data or
                        blueprint_topics_data or documenttimer_data or summaryinteraction_data or
                        summarytracking_data or timersession_data):
                        self.stdout.write("📥 Restoring data...")
                        
                        # Restore sessions
                        for session in sessions_data:
                            cursor.execute("""
                                INSERT INTO users_platformtimesession 
                                (id, session_start, session_end, total_time_seconds, is_active, is_paused, 
                                 pause_reason, last_activity, created_at, student_id, document_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                session['id'], session['session_start'], session['session_end'],
                                session['total_time_seconds'], session['is_active'], session['is_paused'],
                                session['pause_reason'], session['last_activity'], session['created_at'],
                                session['student_id'], session['document_id']
                            ))
                        
                        # Restore stats
                        for stat in stats_data:
                            cursor.execute("""
                                INSERT INTO users_platformtimestats
                                (id, total_time_seconds, total_sessions, total_pauses, quiz_pauses,
                                 navigation_pauses, view_count, first_access, last_access, created_at,
                                 updated_at, student_id, document_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                stat['id'], stat['total_time_seconds'], stat['total_sessions'],
                                stat['total_pauses'], stat['quiz_pauses'], stat['navigation_pauses'],
                                stat['view_count'], stat['first_access'], stat['last_access'],
                                stat['created_at'], stat['updated_at'], stat['student_id'], stat['document_id']
                            ))
                        
                        # Restore events
                        for event in events_data:
                            cursor.execute("""
                                INSERT INTO users_platformtimeevent
                                (id, event_type, reason, timestamp, created_at, session_id)
                                VALUES (?, ?, ?, ?, ?, ?)
                            """, (
                                event['id'], event['event_type'], event['reason'],
                                event['timestamp'], event['created_at'], event['session_id']
                            ))

                        # Restore performance data
                        for perf in performance_data:
                            cursor.execute("""
                                INSERT INTO users_studentperformance
                                (id, quiz_score, time_taken, remarks, created_at, updated_at, student_id, document_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                perf['id'], perf['quiz_score'], perf['time_taken'], perf['remarks'],
                                perf['created_at'], perf['updated_at'], perf['student_id'], perf['document_id']
                            ))

                        # Restore embeddings
                        for emb in embeddings_data:
                            # Convert embedding list to JSON string if needed
                            embedding_value = emb['embedding']
                            if isinstance(embedding_value, list):
                                import json
                                embedding_value = json.dumps(embedding_value)

                            cursor.execute("""
                                INSERT INTO documents_documentembedding
                                (id, text_chunk, embedding, chunk_number, created_at, document_id)
                                VALUES (?, ?, ?, ?, ?, ?)
                            """, (
                                emb['id'], emb['text_chunk'], embedding_value, emb['chunk_number'],
                                emb['created_at'], emb['document_id']
                            ))

                        # Restore flashcards
                        for card in flashcards_data:
                            try:
                                cursor.execute("""
                                    INSERT INTO documents_flashcard
                                    (id, front, back, document_id)
                                    VALUES (?, ?, ?, ?)
                                """, (
                                    card['id'], card['front'], card['back'], card['document_id']
                                ))
                            except Exception as e:
                                self.stdout.write(f"Error restoring flashcard {card}: {e}")
                                raise

                        # Restore flowcharts
                        for flow in flowcharts_data:
                            cursor.execute("""
                                INSERT INTO documents_flowchart
                                (id, mermaid_code, document_id)
                                VALUES (?, ?, ?)
                            """, (
                                flow['id'], flow['mermaid_code'], flow['document_id']
                            ))

                        # Restore quizzes
                        for quiz in quizzes_data:
                            cursor.execute("""
                                INSERT INTO documents_quiz
                                (id, question, answer, document_id)
                                VALUES (?, ?, ?, ?)
                            """, (
                                quiz['id'], quiz['question'], quiz['answer'], quiz['document_id']
                            ))

                        # Restore blueprint topics
                        for topic in blueprint_topics_data:
                            cursor.execute("""
                                INSERT INTO documents_blueprinttopics
                                (id, title, weightage, created_at, document_id)
                                VALUES (?, ?, ?, ?, ?)
                            """, (
                                topic['id'], topic['title'], topic['weightage'],
                                topic['created_at'], topic['document_id']
                            ))

                        # Restore document timer data
                        for timer in documenttimer_data:
                            cursor.execute("""
                                INSERT INTO documents_documenttimer
                                (id, session_start_time, session_end_time, pause_time, resume_time,
                                 total_time_minutes, is_active, ended_by_quiz, created_at, updated_at,
                                 document_id, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, timer)

                        # Restore summary interaction data
                        for interaction in summaryinteraction_data:
                            cursor.execute("""
                                INSERT INTO documents_summaryinteraction
                                (id, event_type, event_time, additional_data, created_at, document_id, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            """, interaction)

                        # Restore summary tracking data
                        for tracking in summarytracking_data:
                            cursor.execute("""
                                INSERT INTO documents_summarytracking
                                (id, summary_generated_at, reached_end, reached_end_at, stayed_avg_time,
                                 stayed_avg_time_at, reopened_once, reopened_at, first_view_time,
                                 total_view_time_minutes, predicted_avg_time_minutes, view_count,
                                 document_id, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, tracking)

                        # Restore timer session data
                        for session in timersession_data:
                            cursor.execute("""
                                INSERT INTO documents_timersession
                                (id, session_number, start_time, end_time, pause_time, resume_time,
                                 total_minutes, ended_by_quiz, created_at, document_id, user_id)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, session)

                # Restore DEBUG setting
                settings.DEBUG = old_debug

                self.stdout.write("✅ Foreign key constraints fixed successfully!")
                self.stdout.write("   Documents can now be deleted without constraint errors")
                
        except Exception as e:
            self.stdout.write(f"❌ Error fixing constraints: {e}")
            raise
