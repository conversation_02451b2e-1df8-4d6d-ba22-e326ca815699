from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache

class ChatSession(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    title = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.title or 'Untitled'}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self._update_cache()

    def _update_cache(self):
        """Update cache with session data"""
        cache_key = f"chat_session_{self.id}"
        cache.set(cache_key, {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'is_active': self.is_active,
            'message_count': self.messages.count()
        }, timeout=settings.CACHE_TIMEOUT)

    @classmethod
    def get_or_create_session(cls, user, title=None):
        """Get or create a chat session with caching"""
        cache_key = f"user_active_session_{user.id}"
        session_id = cache.get(cache_key)
        
        if session_id:
            try:
                session = cls.objects.get(id=session_id, is_active=True)
                return session
            except cls.DoesNotExist:
                pass
        
        session = cls.objects.create(user=user, title=title)
        cache.set(cache_key, session.id, timeout=settings.CACHE_TIMEOUT)
        return session

class ChatMessage(models.Model):
    session = models.ForeignKey(ChatSession, related_name='messages', on_delete=models.CASCADE)
    role = models.CharField(max_length=10, choices=[
        ('user', 'User'),
        ('assistant', 'Assistant'),
        ('system', 'System')
    ])
    content = models.TextField()
    model = models.CharField(max_length=50, choices=[
        ('openai', 'OpenAI'),
        ('gemini', 'Gemini')
    ])
    created_at = models.DateTimeField(auto_now_add=True)
    tokens = models.IntegerField(default=0)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['session', 'created_at']),
            models.Index(fields=['role']),
        ]

    def __str__(self):
        return f"{self.session.user.email} - {self.role} - {self.content[:50]}..."

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self._update_cache()

    def _update_cache(self):
        """Update cache with message data"""
        cache_key = f"chat_message_{self.id}"
        cache.set(cache_key, {
            'id': self.id,
            'session_id': self.session_id,
            'role': self.role,
            'content': self.content,
            'model': self.model,
            'tokens': self.tokens
        }, timeout=settings.CACHE_TIMEOUT)

    @classmethod
    def get_session_messages(cls, session_id, limit=50):
        """Get messages for a session with caching"""
        cache_key = f"session_messages_{session_id}_{limit}"
        messages = cache.get(cache_key)
        
        if messages is None:
            messages = list(cls.objects.filter(session_id=session_id)
                          .order_by('-created_at')[:limit]
                          .values('role', 'content', 'model', 'created_at'))
            cache.set(cache_key, messages, timeout=settings.CACHE_TIMEOUT)
        
        return messages
