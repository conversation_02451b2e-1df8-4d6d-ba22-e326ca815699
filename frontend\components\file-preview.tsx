"use client"

import React, { useState, useEffect } from "react"
import { documentApi } from "@/lib/api"
import { useTheme } from "@/components/theme-provider"
import { FileText, Image, Video, Music, FileSpreadsheet, Presentation, Archive, File } from "lucide-react"

interface FilePreviewProps {
  documentId: number
  fileName: string
  fileType: string
  className?: string
}

export function FilePreview({ documentId, fileName, fileType, className = "" }: FilePreviewProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { theme } = useTheme()

  useEffect(() => {
    loadFilePreview()
  }, [documentId])

  const loadFilePreview = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // For blob-based preview (images, PDFs, etc.)
      if (canPreviewAsBlob(fileType)) {
        const blob = await documentApi.getDocumentFileBlob(documentId)
        const url = URL.createObjectURL(blob)
        setPreviewUrl(url)
      } else {
        // For direct URL preview - but office docs need special handling
        const url = documentApi.getDocumentFileUrl(documentId)
        setPreviewUrl(url)
      }
    } catch (err) {
      console.error('Error loading file preview:', err)
      setError('Failed to load file preview. The file may not be accessible or the server may be down.')
    } finally {
      setIsLoading(false)
    }
  }

  const canPreviewAsBlob = (fileType: string): boolean => {
    const blobTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'pdf']
    return blobTypes.includes(fileType.toLowerCase())
  }

  const getFileIcon = (fileType: string) => {
    const type = fileType.toLowerCase()
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
      return <Image className="h-8 w-8 text-blue-500" />
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(type)) {
      return <Video className="h-8 w-8 text-red-500" />
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(type)) {
      return <Music className="h-8 w-8 text-green-500" />
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(type)) {
      return <FileText className="h-8 w-8 text-orange-500" />
    } else if (['xls', 'xlsx', 'csv'].includes(type)) {
      return <FileSpreadsheet className="h-8 w-8 text-green-600" />
    } else if (['ppt', 'pptx'].includes(type)) {
      return <Presentation className="h-8 w-8 text-red-600" />
    } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(type)) {
      return <Archive className="h-8 w-8 text-purple-500" />
    } else {
      return <File className="h-8 w-8 text-gray-500" />
    }
  }

  const renderPreview = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading preview...</p>
        </div>
      )
    }

    if (error || !previewUrl) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-8">
          {getFileIcon(fileType)}
          <p className="text-lg font-medium mt-4">{fileName}</p>
          <p className="text-sm text-muted-foreground mt-2">
            File type: {fileType.toUpperCase()}
          </p>
          {error && (
            <p className="text-sm text-destructive mt-2">{error}</p>
          )}
          <p className="text-xs text-muted-foreground mt-4 text-center">
            {error ? 'Preview failed to load' : 'Preview not available for this file type'}
          </p>
          {error && (
            <div className="mt-6 flex gap-3">
              <button
                onClick={loadFilePreview}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Retry
              </button>
              <button
                onClick={() => window.open(documentApi.getDocumentFileUrl(documentId), '_blank')}
                className="px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors"
              >
                Download File
              </button>
            </div>
          )}
        </div>
      )
    }

    const type = fileType.toLowerCase()

    // Image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) {
      return (
        <div className="flex items-center justify-center h-full p-4">
          <img
            src={previewUrl}
            alt={fileName}
            className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
            onError={() => setError('Failed to load image')}
          />
        </div>
      )
    }

    // SVG files
    if (type === 'svg') {
      return (
        <div className="flex items-center justify-center h-full p-4">
          <div 
            className="max-w-full max-h-full"
            dangerouslySetInnerHTML={{ __html: previewUrl }}
          />
        </div>
      )
    }

    // PDF files
    if (type === 'pdf') {
      return (
        <div className="h-full w-full">
          <iframe
            src={previewUrl}
            title={fileName}
            className="w-full h-full border-0 rounded-lg"
            onError={() => setError('Failed to load PDF')}
          />
        </div>
      )
    }

    // Video files
    if (['mp4', 'webm', 'ogg'].includes(type)) {
      return (
        <div className="flex items-center justify-center h-full p-4">
          <video
            controls
            className="max-w-full max-h-full rounded-lg shadow-lg"
            onError={() => setError('Failed to load video')}
          >
            <source src={previewUrl} type={`video/${type}`} />
            Your browser does not support the video tag.
          </video>
        </div>
      )
    }

    // Audio files
    if (['mp3', 'wav', 'ogg', 'flac', 'aac'].includes(type)) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-8">
          <Music className="h-16 w-16 text-green-500 mb-4" />
          <p className="text-lg font-medium mb-4">{fileName}</p>
          <audio
            controls
            className="w-full max-w-md"
            onError={() => setError('Failed to load audio')}
          >
            <source src={previewUrl} type={`audio/${type}`} />
            Your browser does not support the audio tag.
          </audio>
        </div>
      )
    }

    // Text files
    if (['txt', 'csv', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c'].includes(type)) {
      return (
        <div className="h-full p-4">
          <iframe
            src={previewUrl}
            title={fileName}
            className="w-full h-full border-0 rounded-lg bg-white"
            onError={() => setError('Failed to load text file')}
          />
        </div>
      )
    }

    // PowerPoint files - special handling
    if (['ppt', 'pptx'].includes(type)) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-8">
          <Presentation className="h-24 w-24 text-red-600 mb-6" />
          <p className="text-xl font-medium mb-2">{fileName}</p>
          <p className="text-sm text-muted-foreground mb-6 text-center max-w-md">
            PowerPoint presentations cannot be previewed directly in the browser.
            You can download the file to view it in PowerPoint or a compatible application.
          </p>
          <div className="flex gap-3">
            <button
              onClick={() => window.open(previewUrl, '_blank')}
              className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              Download File
            </button>
            <button
              onClick={() => {
                const link = document.createElement('a')
                link.href = previewUrl
                link.download = fileName
                link.click()
              }}
              className="px-6 py-3 border border-border rounded-lg hover:bg-muted transition-colors"
            >
              Save As...
            </button>
          </div>
          <p className="text-xs text-muted-foreground mt-4">
            File type: {fileType.toUpperCase()}
          </p>
        </div>
      )
    }

    // Other Office documents (Word, Excel)
    if (['doc', 'docx', 'xls', 'xlsx'].includes(type)) {
      return (
        <div className="flex flex-col items-center justify-center h-full p-8">
          {type.includes('doc') ? (
            <FileText className="h-24 w-24 text-blue-600 mb-6" />
          ) : (
            <FileSpreadsheet className="h-24 w-24 text-green-600 mb-6" />
          )}
          <p className="text-xl font-medium mb-2">{fileName}</p>
          <p className="text-sm text-muted-foreground mb-6 text-center max-w-md">
            Office documents cannot be previewed directly in the browser.
            You can download the file to view it in the appropriate application.
          </p>
          <div className="flex gap-3">
            <button
              onClick={() => window.open(previewUrl, '_blank')}
              className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              Download File
            </button>
            <button
              onClick={() => {
                const link = document.createElement('a')
                link.href = previewUrl
                link.download = fileName
                link.click()
              }}
              className="px-6 py-3 border border-border rounded-lg hover:bg-muted transition-colors"
            >
              Save As...
            </button>
          </div>
          <p className="text-xs text-muted-foreground mt-4">
            File type: {fileType.toUpperCase()}
          </p>
        </div>
      )
    }

    // Default fallback
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        {getFileIcon(fileType)}
        <p className="text-lg font-medium mt-4">{fileName}</p>
        <p className="text-sm text-muted-foreground mt-2">
          File type: {fileType.toUpperCase()}
        </p>
        <p className="text-xs text-muted-foreground mt-4 text-center">
          Preview not available for this file type
        </p>
        <button
          onClick={() => window.open(previewUrl, '_blank')}
          className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Download File
        </button>
      </div>
    )
  }

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [previewUrl])

  return (
    <div className={`h-full w-full ${theme === "light" ? "bg-white border border-gray-200" : "bg-neutral-800 border border-neutral-700"} rounded-lg overflow-hidden ${className}`}>
      {renderPreview()}
    </div>
  )
}
