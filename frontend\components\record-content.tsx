"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Mic, Square, Play, Pause, Save, Loader2 } from "lucide-react"
import { motion } from "framer-motion"
import { useTheme } from "@/components/theme-provider"

export function RecordContent() {
  const [isRecording, setIsRecording] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [recordingTime, setRecordingTime] = useState(0)
  const [audioURL, setAudioURL] = useState<string | null>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const { theme } = useTheme()
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (audioURL) {
        URL.revokeObjectURL(audioURL)
      }
    }
  }, [audioURL])

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

      const mediaRecorder = new MediaRecorder(stream)
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data)
        }
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: "audio/wav" })
        const url = URL.createObjectURL(audioBlob)
        setAudioURL(url)
      }

      mediaRecorder.start()
      setIsRecording(true)
      setIsPaused(false)

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    } catch (error) {
      console.error("Error accessing microphone:", error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)

      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }

      // Stop all audio tracks
      mediaRecorderRef.current.stream.getTracks().forEach((track) => track.stop())
    }
  }

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording && !isPaused) {
      mediaRecorderRef.current.pause()
      setIsPaused(true)

      // Pause timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }

  const resumeRecording = () => {
    if (mediaRecorderRef.current && isRecording && isPaused) {
      mediaRecorderRef.current.resume()
      setIsPaused(false)

      // Resume timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1)
      }, 1000)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const processRecording = () => {
    if (audioURL) {
      setIsProcessing(true)
      // Store recording info in localStorage to simulate persistence
      localStorage.setItem(
        "recordedAudio",
        JSON.stringify({
          duration: formatTime(recordingTime),
          timestamp: new Date().toISOString(),
          audioURL: audioURL, // In a real app, you'd upload this to a server
        }),
      )

      // Simulate preprocessing time
      setTimeout(() => {
        // Navigate to the process page
        window.location.href = "/process?type=record"
      }, 2000)
    }
  }

  return (
    <div className="h-full flex flex-col items-center justify-center">
      <h2 className="text-xl font-semibold mb-8">Record Audio</h2>

      <motion.div
        initial={{ scale: 1 }}
        animate={{
          scale: isRecording && !isPaused ? [1, 1.1, 1] : 1,
          transition: {
            repeat: isRecording && !isPaused ? Number.POSITIVE_INFINITY : 0,
            duration: 1.5,
          },
        }}
        className="relative mb-6"
      >
        <div
          className={`p-12 rounded-full ${
            isRecording ? "bg-red-500/20" : theme === "light" ? "bg-white border border-black" : "bg-neutral-800"
          }`}
        >
          <Mic
            className={`h-12 w-12 ${
              isRecording ? "text-red-500" : theme === "light" ? "text-black" : "text-purple-500"
            }`}
          />
        </div>
        {isRecording && !isPaused && (
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-red-500"
            initial={{ scale: 1, opacity: 1 }}
            animate={{
              scale: [1, 1.2, 1.2, 1],
              opacity: [1, 0.8, 0.2, 0],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeOut",
            }}
          />
        )}
      </motion.div>

      <div className="text-3xl font-mono mb-8">{formatTime(recordingTime)}</div>

      <div className="flex gap-6 mb-8">
        {!isRecording ? (
          <Button onClick={startRecording} className="bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0">
            <Mic className="h-6 w-6" />
          </Button>
        ) : (
          <>
            <Button onClick={stopRecording} className="bg-red-600 hover:bg-red-700 rounded-full h-14 w-14 p-0">
              <Square className="h-6 w-6" />
            </Button>

            {isPaused ? (
              <Button
                onClick={resumeRecording}
                className="bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0"
              >
                <Play className="h-6 w-6" />
              </Button>
            ) : (
              <Button onClick={pauseRecording} className="bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0">
                <Pause className="h-6 w-6" />
              </Button>
            )}
          </>
        )}
      </div>

      {audioURL && (
        <div className="flex flex-col items-center gap-4">
          <audio ref={audioRef} src={audioURL} controls className="w-full max-w-md" />
          <Button
            className="bg-purple-600 hover:bg-purple-700"
            onClick={processRecording}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <div className="flex items-center">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                <div>Processing...</div>
              </div>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Process Recording
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
