import io
import os
import PyPDF2
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from sentence_transformers import SentenceTransformer
from typing import List, Tuple
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Set up NLTK data path to include a local directory
nltk_data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'nltk_data')
os.makedirs(nltk_data_dir, exist_ok=True)
nltk.data.path.insert(0, nltk_data_dir)  # Add our directory as the first place to look

# Download required NLTK data to our local directory
try:
    nltk.download('punkt', download_dir=nltk_data_dir, quiet=True)
    nltk.download('punkt_tab', download_dir=nltk_data_dir, quiet=True)
    # Verify that punkt is properly installed
    from nltk.tokenize import PunktSentenceTokenizer
    tokenizer = PunktSentenceTokenizer()
    logging.info(f"NLTK punkt tokenizer loaded successfully from {nltk_data_dir}")
except Exception as e:
    logging.error(f"Error loading NLTK punkt: {str(e)}")
    # Try alternative download method
    try:
        import subprocess
        import sys
        logging.info("Attempting alternative NLTK download method...")
        subprocess.check_call([sys.executable, '-m', 'nltk.downloader', '-d', nltk_data_dir, 'punkt'])
        logging.info("Alternative download method completed")
    except Exception as alt_e:
        logging.error(f"Alternative download method failed: {str(alt_e)}")
        logging.warning("Continuing with fallback tokenization methods")

# Initialize the sentence transformer model
model = SentenceTransformer('all-MiniLM-L6-v2')


async def preprocess_pdf(file_content: bytes) -> str:
    """
    Extract and clean text from PDF or text file.

    Parameters:
    - file_content: Raw bytes of the file (PDF or text)

    Returns:
    - Cleaned and formatted text from the file
    """
    # Check if the file is a PDF by looking for the PDF signature
    is_pdf = file_content.startswith(b'%PDF')

    if is_pdf:
        try:
            pdf_file = io.BytesIO(file_content)
            reader = PyPDF2.PdfReader(pdf_file)

            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
        except Exception as e:
            logging.error(f"Error processing PDF: {str(e)}")
            raise ValueError(f"Error processing PDF: {str(e)}")
    else:
        # Handle as plain text file
        try:
            # Try to decode as UTF-8
            text = file_content.decode('utf-8')
        except UnicodeDecodeError:
            # If UTF-8 fails, try with latin-1 which should handle any byte sequence
            text = file_content.decode('latin-1')
        logging.info("Processed file as plain text")

    # Basic cleaning
    text = text.replace('\n\n', ' [PARA] ').replace('\n', ' ')
    text = ' '.join(text.split())

    # Split into paragraphs
    paragraphs = text.split('[PARA]')
    paragraphs = [p.strip() for p in paragraphs if p.strip()]

    return '\n\n'.join(paragraphs)


def create_embeddings(text: str, chunk_size: int = 500) -> Tuple[List[str], List[List[float]]]:
    """
    Create embeddings for chunks of text.

    Parameters:
    - text: The text to create embeddings for
    - chunk_size: Maximum number of words per chunk

    Returns:
    - A tuple containing:
      - List of text chunks
      - List of embeddings for each chunk
    """
    try:
        # Split text into sentences
        try:
            sentences = sent_tokenize(text)
            logging.info(f"Text tokenized into {len(sentences)} sentences")
        except Exception as e:
            logging.error(f"Error in sentence tokenization: {str(e)}")
            # Fallback: split by periods if sentence tokenization fails
            sentences = [s.strip() + '.' for s in text.split('.') if s.strip()]
            logging.info(f"Fallback tokenization: split text into {len(sentences)} segments")

        chunks = []
        current_chunk = []
        current_length = 0

        for sentence in sentences:
            try:
                # Try to tokenize words
                sentence_length = len(word_tokenize(sentence))
            except Exception as e:
                logging.error(f"Error in word tokenization: {str(e)}")
                # Fallback: estimate length by splitting on spaces
                sentence_length = len(sentence.split())

            if current_length + sentence_length > chunk_size:
                if current_chunk:
                    chunks.append(' '.join(current_chunk))
                current_chunk = [sentence]
                current_length = sentence_length
            else:
                current_chunk.append(sentence)
                current_length += sentence_length

        if current_chunk:
            chunks.append(' '.join(current_chunk))

        # If no chunks were created (e.g., very short text), create at least one
        if not chunks and text.strip():
            chunks = [text.strip()]

        logging.info(f"Created {len(chunks)} text chunks for embedding")

        # Generate embeddings
        embeddings = model.encode(chunks)
        return chunks, embeddings.tolist()

    except Exception as e:
        logging.error(f"Error in create_embeddings: {str(e)}")
        # If all else fails, return the entire text as a single chunk with a zero embedding
        # This allows the process to continue even if embedding fails
        if text.strip():
            fallback_chunk = [text.strip()]
            # Create a zero embedding of the same dimension as the model
            model_dimension = 384  # Default dimension for 'all-MiniLM-L6-v2'
            fallback_embedding = [[0.0] * model_dimension]
            logging.warning("Using fallback embedding due to error")
            return fallback_chunk, fallback_embedding
        else:
            # Empty text case
            return [], []