from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Check table structure for tables without Django models'

    def handle(self, *args, **options):
        print("Checking table structure...")
        print("=" * 60)
        
        with connection.cursor() as cursor:
            # Tables without Django models
            tables = [
                'documents_documenttimer',
                'documents_summaryinteraction',
                'documents_summarytracking', 
                'documents_timersession'
            ]
            
            for table in tables:
                try:
                    print(f"\n{table}:")
                    cursor.execute(f"PRAGMA table_info({table});")
                    columns = cursor.fetchall()
                    for col in columns:
                        print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'PRIMARY KEY' if col[5] else ''}")
                    
                    # Check foreign keys
                    cursor.execute(f"PRAGMA foreign_key_list({table});")
                    fks = cursor.fetchall()
                    if fks:
                        print("  Foreign Keys:")
                        for fk in fks:
                            print(f"    {fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[6]})")
                        
                except Exception as e:
                    print(f"{table}: Error - {e}")
        
        print("\nTable structure check completed!")
