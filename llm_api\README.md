# LLM Inference API

A FastAPI-based service that provides a unified interface for multiple LLM providers (OpenAI and Gemini).

## Setup

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Create a `.env` file in the root directory with your API keys:
```
OPENAI_API_KEY=your-openai-api-key-here
GOOGLE_API_KEY=your-google-api-key-here
```

3. Run the FastAPI server:
```bash
python main.py
```

The API will be available at `http://localhost:8001`.

## API Documentation

Once the server is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8001/docs`
- ReDoc: `http://localhost:8001/redoc`

## API Endpoint

### POST /infer

Make inference requests to the LLM models.

**Request Body:**
```json
{
    "model": "openai",  // or "gemini"
    "instruction": "What is the capital of France?",
    "context": "You are a helpful assistant."  // optional
}
```

**Response:**
```json
{
    "response": "The capital of France is Paris.",
    "model": "openai"
}
```

## Supported Models

- OpenAI (GPT-3.5-turbo)
- Google Gemini (gemini-pro)

## Error Handling

The API will return appropriate HTTP status codes and error messages for:
- Invalid model selection (400)
- API key issues (500)
- Other API-related errors (500)