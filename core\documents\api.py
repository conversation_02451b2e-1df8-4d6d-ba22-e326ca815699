from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
import logging
import os
import requests

from .models import Document, DocumentEmbedding, BlueprintTopics
from .serializers import DocumentSerializer, DocumentEmbeddingSerializer, BlueprintTopicsSerializer

logger = logging.getLogger(__name__)

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_embeddings(request, document_id):
    """
    Store document embeddings received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the chunks from the request
        chunks = request.data.get('chunks', [])

        if not chunks:
            return Response(
                {"error": "No chunks provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing embeddings for this document
        DocumentEmbedding.objects.filter(document=document).delete()

        # Create embedding objects for each chunk
        for chunk in chunks:
            DocumentEmbedding.objects.create(
                document=document,
                text_chunk=chunk['text'],
                embedding=chunk['embedding'],
                chunk_number=chunk['chunk_number']
            )

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "message": "Embeddings stored successfully",
            "document_id": document_id,
            "num_chunks": len(chunks)
        })

    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        return Response(
            {"error": f"Error storing embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def store_topics(request, document_id):
    """
    Store blueprint topics received from FastAPI server
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topics from the request
        topics = request.data.get('topics', [])

        if not topics:
            return Response(
                {"error": "No topics provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # First delete any existing topics for this document
        BlueprintTopics.objects.filter(document=document).delete()

        # Create topic objects for each topic
        created_topics = []
        for topic in topics:
            blueprint_topic = BlueprintTopics.objects.create(
                document=document,
                title=topic['title'],
                weightage=topic['weightage']
            )

            # Find relevant embeddings for this topic
            embeddings = DocumentEmbedding.objects.filter(document=document)

            if embeddings.exists():
                # Use semantic search to find relevant embeddings
                from sentence_transformers import SentenceTransformer
                import numpy as np

                # Initialize the sentence transformer model
                model = SentenceTransformer('all-MiniLM-L6-v2')

                # Generate embedding for the topic
                topic_embedding = model.encode([topic['title']])[0]

                # Calculate similarity scores
                similarities = []
                for idx, embedding_obj in enumerate(embeddings):
                    embedding_vector = np.array(embedding_obj.embedding)
                    similarity = np.dot(topic_embedding, embedding_vector) / (
                        np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
                    )
                    similarities.append((similarity, idx, embedding_obj))

                # Sort by similarity (highest first)
                similarities.sort(reverse=True)

                # Get top 5 or 30% of embeddings, whichever is greater
                num_to_select = max(5, int(len(embeddings) * 0.3))
                relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

                # Add embeddings to the topic
                blueprint_topic.content.add(*relevant_embeddings)

            created_topics.append(blueprint_topic)

        # Update document status to completed
        document.processing_status = 'completed'
        document.error_message = None  # Clear any previous error
        document.save()

        return Response({
            "status_code": status.HTTP_200_OK,
            "message": "Topics stored successfully",
            "document_id": document_id,
            "num_topics": len(created_topics)
        })

    except Exception as e:
        logger.error(f"Error storing topics: {str(e)}")
        return Response(
            {"error": f"Error storing topics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_document_status(request, document_id):
    """
    Update document status
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the status from the request
        status_value = request.data.get('status')
        error_message = request.data.get('error_message')

        if not status_value:
            return Response(
                {"error": "No status provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update document status
        document.processing_status = status_value
        if error_message:
            document.error_message = error_message
        document.save()

        return Response({
            "message": "Document status updated successfully",
            "document_id": document_id,
            "status": status_value
        })

    except Exception as e:
        logger.error(f"Error updating document status: {str(e)}")
        return Response(
            {"error": f"Error updating document status: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def find_relevant_embeddings(request, document_id):
    """
    Find embeddings relevant to a topic
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get the topic from the request
        topic = request.data.get('topic')

        if not topic:
            return Response(
                {"error": "No topic provided"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document)

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use semantic search to find relevant embeddings
        from sentence_transformers import SentenceTransformer
        import numpy as np

        # Initialize the sentence transformer model
        model = SentenceTransformer('all-MiniLM-L6-v2')

        # Generate embedding for the topic
        topic_embedding = model.encode([topic])[0]

        # Calculate similarity scores
        similarities = []
        for idx, embedding_obj in enumerate(embeddings):
            embedding_vector = np.array(embedding_obj.embedding)
            similarity = np.dot(topic_embedding, embedding_vector) / (
                np.linalg.norm(topic_embedding) * np.linalg.norm(embedding_vector)
            )
            similarities.append((similarity, idx, embedding_obj))

        # Sort by similarity (highest first)
        similarities.sort(reverse=True)

        # Get top 5 or 30% of embeddings, whichever is greater
        num_to_select = max(5, int(len(embeddings) * 0.3))
        relevant_embeddings = [emb for _, _, emb in similarities[:num_to_select]]

        return Response({
            "embedding_ids": [emb.id for emb in relevant_embeddings]
        })

    except Exception as e:
        logger.error(f"Error finding relevant embeddings: {str(e)}")
        return Response(
            {"error": f"Error finding relevant embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_document_embeddings(request, document_id):
    """
    Get all embeddings for a document
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get all embeddings for this document
        embeddings = DocumentEmbedding.objects.filter(document=document).order_by('chunk_number')

        if not embeddings.exists():
            return Response(
                {"error": "Document has no embeddings"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Serialize the embeddings
        serializer = DocumentEmbeddingSerializer(embeddings, many=True)

        return Response(serializer.data)

    except Exception as e:
        logger.error(f"Error retrieving document embeddings: {str(e)}")
        return Response(
            {"error": f"Error retrieving document embeddings: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )




@api_view(["POST"])
@permission_classes([IsAuthenticated])
def process_blueprint_direct(request, document_id):
    """
    Direct endpoint for processing blueprints.
    Compatible with frontend API calls to /process-blueprint/{documentId}/
    """
    try:
        # Get the document
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get blueprint text and LLM model from request
        blueprint_text = request.data.get('blueprint_text')
        llm_model = request.data.get('llm_model', 'openai')

        # If no blueprint text provided, use the document's blueprint
        if not blueprint_text:
            if not document.blueprint:
                return Response(
                    {"error": "No blueprint text provided and document has no blueprint"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            blueprint_text = document.blueprint
        else:
            # Update document blueprint if new text provided
            document.blueprint = blueprint_text
            document.save()

        # Validate the LLM model
        if llm_model not in ['openai', 'gemini']:
            return Response(
                {"error": "Invalid LLM model. Use 'openai' or 'gemini'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the auth token
        auth_token = None
        if hasattr(request, 'auth') and request.auth:
            if hasattr(request.auth, 'token'):
                auth_token = request.auth.token
            elif hasattr(request.auth, 'key'):
                auth_token = request.auth.key
            else:
                auth_token = str(request.auth)

        if not auth_token:
            auth_token = f"user_{request.user.id}"

        # Set document status to processing
        document.processing_status = 'processing'
        document.save()

        # Import the direct processing function
        from .views import direct_process_blueprint

        # Try to use Celery task first, fall back to direct processing
        try:
            from .tasks import process_blueprint_task
            process_blueprint_task.delay(document.id, auth_token, llm_model)
            processing_method = "background task"
        except Exception as e:
            logger.warning(f"Celery task failed, falling back to direct processing: {str(e)}")
            # Fall back to direct processing
            result = direct_process_blueprint(document, auth_token, llm_model)
            processing_method = "direct request"

            # If there was an error, return it
            if 'error' in result:
                return Response({
                    "message": f"Blueprint processing failed: {result['error']}",
                    "document_id": document.id,
                    "status": "failed",
                    "error": result['error'],
                    "llm_model": llm_model
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'message': f'Blueprint processing started via {processing_method} using {llm_model}.',
            'document_id': document.id,
            'status': 'processing',
            'llm_model': llm_model
        }, status=status.HTTP_202_ACCEPTED)

    except Exception as e:
        logger.error(f"Error processing blueprint for document {document_id}: {str(e)}")
        return Response(
            {"error": f"Error processing blueprint: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def get_or_generate_summary(request, document_id):
    """
    GET: Retrieve existing summary for a document
    POST: Generate new summary for a document
    """
    document = get_object_or_404(Document, id=document_id, user=request.user)

    if request.method == "GET":
        # Try to get existing summary from database
        try:
            from .models import Summary
            summary = Summary.objects.get(document=document)
            return Response({
                "summary": summary.content,
                "created_at": summary.created_at,
                "summary_type": getattr(summary, 'summary_type', 'comprehensive')
            })
        except ImportError:
            # Summary model doesn't exist yet
            return Response({"error": "No summary found for this document"}, status=status.HTTP_404_NOT_FOUND)
        except Exception:
            # Summary doesn't exist or other error
            return Response({"error": "No summary found for this document"}, status=status.HTTP_404_NOT_FOUND)

    elif request.method == "POST":
        # Generate new summary - this will be handled by FastAPI
        # For now, return a placeholder response
        return Response({
            "message": "Summary generation should be handled by FastAPI endpoint",
            "fastapi_endpoint": f"/generate-summary/{document_id}"
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


