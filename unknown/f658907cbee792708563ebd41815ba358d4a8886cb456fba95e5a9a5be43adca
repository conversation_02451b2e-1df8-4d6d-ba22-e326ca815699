/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/follow-redirects";
exports.ids = ["vendor-chunks/follow-redirects"];
exports.modules = {

/***/ "(ssr)/./node_modules/follow-redirects/debug.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/debug.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var debug;\n\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")(\"follow-redirects\");\n    }\n    catch (error) { /* */ }\n    if (typeof debug !== \"function\") {\n      debug = function () { /* */ };\n    }\n  }\n  debug.apply(null, arguments);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9sbG93LXJlZGlyZWN0cy9kZWJ1Zy5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbUJBQU8sQ0FBQyxzREFBTztBQUM3QjtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqb3ZhblxcT25lRHJpdmVcXERlc2t0b3BcXGNvZ25pbW9zaXR5X1VJXFxsYXRlc3QgcmVwb1xcY29nbmlfYXBpXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxmb2xsb3ctcmVkaXJlY3RzXFxkZWJ1Zy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZGVidWc7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKCkge1xuICBpZiAoIWRlYnVnKSB7XG4gICAgdHJ5IHtcbiAgICAgIC8qIGVzbGludCBnbG9iYWwtcmVxdWlyZTogb2ZmICovXG4gICAgICBkZWJ1ZyA9IHJlcXVpcmUoXCJkZWJ1Z1wiKShcImZvbGxvdy1yZWRpcmVjdHNcIik7XG4gICAgfVxuICAgIGNhdGNoIChlcnJvcikgeyAvKiAqLyB9XG4gICAgaWYgKHR5cGVvZiBkZWJ1ZyAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICBkZWJ1ZyA9IGZ1bmN0aW9uICgpIHsgLyogKi8gfTtcbiAgICB9XG4gIH1cbiAgZGVidWcuYXBwbHkobnVsbCwgYXJndW1lbnRzKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/follow-redirects/debug.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/follow-redirects/index.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var url = __webpack_require__(/*! url */ \"url\");\nvar URL = url.URL;\nvar http = __webpack_require__(/*! http */ \"http\");\nvar https = __webpack_require__(/*! https */ \"https\");\nvar Writable = (__webpack_require__(/*! stream */ \"stream\").Writable);\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar debug = __webpack_require__(/*! ./debug */ \"(ssr)/./node_modules/follow-redirects/debug.js\");\n\n// Preventive platform detection\n// istanbul ignore next\n(function detectUnsupportedEnvironment() {\n  var looksLikeNode = typeof process !== \"undefined\";\n  var looksLikeBrowser = typeof window !== \"undefined\" && typeof document !== \"undefined\";\n  var looksLikeV8 = isFunction(Error.captureStackTrace);\n  if (!looksLikeNode && (looksLikeBrowser || !looksLikeV8)) {\n    console.warn(\"The follow-redirects package should be excluded from browser builds.\");\n  }\n}());\n\n// Whether to use the native URL object or the legacy url module\nvar useNativeURL = false;\ntry {\n  assert(new URL(\"\"));\n}\ncatch (error) {\n  useNativeURL = error.code === \"ERR_INVALID_URL\";\n}\n\n// URL fields to preserve in copy operations\nvar preservedUrlFields = [\n  \"auth\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"path\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"query\",\n  \"search\",\n  \"hash\",\n];\n\n// Create handlers that pass events from native requests\nvar events = [\"abort\", \"aborted\", \"connect\", \"error\", \"socket\", \"timeout\"];\nvar eventHandlers = Object.create(null);\nevents.forEach(function (event) {\n  eventHandlers[event] = function (arg1, arg2, arg3) {\n    this._redirectable.emit(event, arg1, arg2, arg3);\n  };\n});\n\n// Error types with codes\nvar InvalidUrlError = createErrorType(\n  \"ERR_INVALID_URL\",\n  \"Invalid URL\",\n  TypeError\n);\nvar RedirectionError = createErrorType(\n  \"ERR_FR_REDIRECTION_FAILURE\",\n  \"Redirected request failed\"\n);\nvar TooManyRedirectsError = createErrorType(\n  \"ERR_FR_TOO_MANY_REDIRECTS\",\n  \"Maximum number of redirects exceeded\",\n  RedirectionError\n);\nvar MaxBodyLengthExceededError = createErrorType(\n  \"ERR_FR_MAX_BODY_LENGTH_EXCEEDED\",\n  \"Request body larger than maxBodyLength limit\"\n);\nvar WriteAfterEndError = createErrorType(\n  \"ERR_STREAM_WRITE_AFTER_END\",\n  \"write after end\"\n);\n\n// istanbul ignore next\nvar destroy = Writable.prototype.destroy || noop;\n\n// An HTTP(S) request that can be redirected\nfunction RedirectableRequest(options, responseCallback) {\n  // Initialize the request\n  Writable.call(this);\n  this._sanitizeOptions(options);\n  this._options = options;\n  this._ended = false;\n  this._ending = false;\n  this._redirectCount = 0;\n  this._redirects = [];\n  this._requestBodyLength = 0;\n  this._requestBodyBuffers = [];\n\n  // Attach a callback if passed\n  if (responseCallback) {\n    this.on(\"response\", responseCallback);\n  }\n\n  // React to responses of native requests\n  var self = this;\n  this._onNativeResponse = function (response) {\n    try {\n      self._processResponse(response);\n    }\n    catch (cause) {\n      self.emit(\"error\", cause instanceof RedirectionError ?\n        cause : new RedirectionError({ cause: cause }));\n    }\n  };\n\n  // Perform the first request\n  this._performRequest();\n}\nRedirectableRequest.prototype = Object.create(Writable.prototype);\n\nRedirectableRequest.prototype.abort = function () {\n  destroyRequest(this._currentRequest);\n  this._currentRequest.abort();\n  this.emit(\"abort\");\n};\n\nRedirectableRequest.prototype.destroy = function (error) {\n  destroyRequest(this._currentRequest, error);\n  destroy.call(this, error);\n  return this;\n};\n\n// Writes buffered data to the current native request\nRedirectableRequest.prototype.write = function (data, encoding, callback) {\n  // Writing is not allowed if end has been called\n  if (this._ending) {\n    throw new WriteAfterEndError();\n  }\n\n  // Validate input and shift parameters if necessary\n  if (!isString(data) && !isBuffer(data)) {\n    throw new TypeError(\"data should be a string, Buffer or Uint8Array\");\n  }\n  if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Ignore empty buffers, since writing them doesn't invoke the callback\n  // https://github.com/nodejs/node/issues/22066\n  if (data.length === 0) {\n    if (callback) {\n      callback();\n    }\n    return;\n  }\n  // Only write when we don't exceed the maximum body length\n  if (this._requestBodyLength + data.length <= this._options.maxBodyLength) {\n    this._requestBodyLength += data.length;\n    this._requestBodyBuffers.push({ data: data, encoding: encoding });\n    this._currentRequest.write(data, encoding, callback);\n  }\n  // Error when we exceed the maximum body length\n  else {\n    this.emit(\"error\", new MaxBodyLengthExceededError());\n    this.abort();\n  }\n};\n\n// Ends the current native request\nRedirectableRequest.prototype.end = function (data, encoding, callback) {\n  // Shift parameters if necessary\n  if (isFunction(data)) {\n    callback = data;\n    data = encoding = null;\n  }\n  else if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Write data if needed and end\n  if (!data) {\n    this._ended = this._ending = true;\n    this._currentRequest.end(null, null, callback);\n  }\n  else {\n    var self = this;\n    var currentRequest = this._currentRequest;\n    this.write(data, encoding, function () {\n      self._ended = true;\n      currentRequest.end(null, null, callback);\n    });\n    this._ending = true;\n  }\n};\n\n// Sets a header value on the current native request\nRedirectableRequest.prototype.setHeader = function (name, value) {\n  this._options.headers[name] = value;\n  this._currentRequest.setHeader(name, value);\n};\n\n// Clears a header value on the current native request\nRedirectableRequest.prototype.removeHeader = function (name) {\n  delete this._options.headers[name];\n  this._currentRequest.removeHeader(name);\n};\n\n// Global timeout for all underlying requests\nRedirectableRequest.prototype.setTimeout = function (msecs, callback) {\n  var self = this;\n\n  // Destroys the socket on timeout\n  function destroyOnTimeout(socket) {\n    socket.setTimeout(msecs);\n    socket.removeListener(\"timeout\", socket.destroy);\n    socket.addListener(\"timeout\", socket.destroy);\n  }\n\n  // Sets up a timer to trigger a timeout event\n  function startTimer(socket) {\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n    }\n    self._timeout = setTimeout(function () {\n      self.emit(\"timeout\");\n      clearTimer();\n    }, msecs);\n    destroyOnTimeout(socket);\n  }\n\n  // Stops a timeout from triggering\n  function clearTimer() {\n    // Clear the timeout\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n      self._timeout = null;\n    }\n\n    // Clean up all attached listeners\n    self.removeListener(\"abort\", clearTimer);\n    self.removeListener(\"error\", clearTimer);\n    self.removeListener(\"response\", clearTimer);\n    self.removeListener(\"close\", clearTimer);\n    if (callback) {\n      self.removeListener(\"timeout\", callback);\n    }\n    if (!self.socket) {\n      self._currentRequest.removeListener(\"socket\", startTimer);\n    }\n  }\n\n  // Attach callback if passed\n  if (callback) {\n    this.on(\"timeout\", callback);\n  }\n\n  // Start the timer if or when the socket is opened\n  if (this.socket) {\n    startTimer(this.socket);\n  }\n  else {\n    this._currentRequest.once(\"socket\", startTimer);\n  }\n\n  // Clean up on events\n  this.on(\"socket\", destroyOnTimeout);\n  this.on(\"abort\", clearTimer);\n  this.on(\"error\", clearTimer);\n  this.on(\"response\", clearTimer);\n  this.on(\"close\", clearTimer);\n\n  return this;\n};\n\n// Proxy all other public ClientRequest methods\n[\n  \"flushHeaders\", \"getHeader\",\n  \"setNoDelay\", \"setSocketKeepAlive\",\n].forEach(function (method) {\n  RedirectableRequest.prototype[method] = function (a, b) {\n    return this._currentRequest[method](a, b);\n  };\n});\n\n// Proxy all public ClientRequest properties\n[\"aborted\", \"connection\", \"socket\"].forEach(function (property) {\n  Object.defineProperty(RedirectableRequest.prototype, property, {\n    get: function () { return this._currentRequest[property]; },\n  });\n});\n\nRedirectableRequest.prototype._sanitizeOptions = function (options) {\n  // Ensure headers are always present\n  if (!options.headers) {\n    options.headers = {};\n  }\n\n  // Since http.request treats host as an alias of hostname,\n  // but the url module interprets host as hostname plus port,\n  // eliminate the host property to avoid confusion.\n  if (options.host) {\n    // Use hostname if set, because it has precedence\n    if (!options.hostname) {\n      options.hostname = options.host;\n    }\n    delete options.host;\n  }\n\n  // Complete the URL object when necessary\n  if (!options.pathname && options.path) {\n    var searchPos = options.path.indexOf(\"?\");\n    if (searchPos < 0) {\n      options.pathname = options.path;\n    }\n    else {\n      options.pathname = options.path.substring(0, searchPos);\n      options.search = options.path.substring(searchPos);\n    }\n  }\n};\n\n\n// Executes the next native request (initial or redirect)\nRedirectableRequest.prototype._performRequest = function () {\n  // Load the native protocol\n  var protocol = this._options.protocol;\n  var nativeProtocol = this._options.nativeProtocols[protocol];\n  if (!nativeProtocol) {\n    throw new TypeError(\"Unsupported protocol \" + protocol);\n  }\n\n  // If specified, use the agent corresponding to the protocol\n  // (HTTP and HTTPS use different types of agents)\n  if (this._options.agents) {\n    var scheme = protocol.slice(0, -1);\n    this._options.agent = this._options.agents[scheme];\n  }\n\n  // Create the native request and set up its event handlers\n  var request = this._currentRequest =\n        nativeProtocol.request(this._options, this._onNativeResponse);\n  request._redirectable = this;\n  for (var event of events) {\n    request.on(event, eventHandlers[event]);\n  }\n\n  // RFC7230§5.3.1: When making a request directly to an origin server, […]\n  // a client MUST send only the absolute path […] as the request-target.\n  this._currentUrl = /^\\//.test(this._options.path) ?\n    url.format(this._options) :\n    // When making a request to a proxy, […]\n    // a client MUST send the target URI in absolute-form […].\n    this._options.path;\n\n  // End a redirected request\n  // (The first request must be ended explicitly with RedirectableRequest#end)\n  if (this._isRedirect) {\n    // Write the request entity and end\n    var i = 0;\n    var self = this;\n    var buffers = this._requestBodyBuffers;\n    (function writeNext(error) {\n      // Only write if this request has not been redirected yet\n      // istanbul ignore else\n      if (request === self._currentRequest) {\n        // Report any write errors\n        // istanbul ignore if\n        if (error) {\n          self.emit(\"error\", error);\n        }\n        // Write the next buffer if there are still left\n        else if (i < buffers.length) {\n          var buffer = buffers[i++];\n          // istanbul ignore else\n          if (!request.finished) {\n            request.write(buffer.data, buffer.encoding, writeNext);\n          }\n        }\n        // End the request if `end` has been called on us\n        else if (self._ended) {\n          request.end();\n        }\n      }\n    }());\n  }\n};\n\n// Processes a response from the current native request\nRedirectableRequest.prototype._processResponse = function (response) {\n  // Store the redirected response\n  var statusCode = response.statusCode;\n  if (this._options.trackRedirects) {\n    this._redirects.push({\n      url: this._currentUrl,\n      headers: response.headers,\n      statusCode: statusCode,\n    });\n  }\n\n  // RFC7231§6.4: The 3xx (Redirection) class of status code indicates\n  // that further action needs to be taken by the user agent in order to\n  // fulfill the request. If a Location header field is provided,\n  // the user agent MAY automatically redirect its request to the URI\n  // referenced by the Location field value,\n  // even if the specific status code is not understood.\n\n  // If the response is not a redirect; return it as-is\n  var location = response.headers.location;\n  if (!location || this._options.followRedirects === false ||\n      statusCode < 300 || statusCode >= 400) {\n    response.responseUrl = this._currentUrl;\n    response.redirects = this._redirects;\n    this.emit(\"response\", response);\n\n    // Clean up\n    this._requestBodyBuffers = [];\n    return;\n  }\n\n  // The response is a redirect, so abort the current request\n  destroyRequest(this._currentRequest);\n  // Discard the remainder of the response to avoid waiting for data\n  response.destroy();\n\n  // RFC7231§6.4: A client SHOULD detect and intervene\n  // in cyclical redirections (i.e., \"infinite\" redirection loops).\n  if (++this._redirectCount > this._options.maxRedirects) {\n    throw new TooManyRedirectsError();\n  }\n\n  // Store the request headers if applicable\n  var requestHeaders;\n  var beforeRedirect = this._options.beforeRedirect;\n  if (beforeRedirect) {\n    requestHeaders = Object.assign({\n      // The Host header was set by nativeProtocol.request\n      Host: response.req.getHeader(\"host\"),\n    }, this._options.headers);\n  }\n\n  // RFC7231§6.4: Automatic redirection needs to done with\n  // care for methods not known to be safe, […]\n  // RFC7231§6.4.2–3: For historical reasons, a user agent MAY change\n  // the request method from POST to GET for the subsequent request.\n  var method = this._options.method;\n  if ((statusCode === 301 || statusCode === 302) && this._options.method === \"POST\" ||\n      // RFC7231§6.4.4: The 303 (See Other) status code indicates that\n      // the server is redirecting the user agent to a different resource […]\n      // A user agent can perform a retrieval request targeting that URI\n      // (a GET or HEAD request if using HTTP) […]\n      (statusCode === 303) && !/^(?:GET|HEAD)$/.test(this._options.method)) {\n    this._options.method = \"GET\";\n    // Drop a possible entity and headers related to it\n    this._requestBodyBuffers = [];\n    removeMatchingHeaders(/^content-/i, this._options.headers);\n  }\n\n  // Drop the Host header, as the redirect might lead to a different host\n  var currentHostHeader = removeMatchingHeaders(/^host$/i, this._options.headers);\n\n  // If the redirect is relative, carry over the host of the last request\n  var currentUrlParts = parseUrl(this._currentUrl);\n  var currentHost = currentHostHeader || currentUrlParts.host;\n  var currentUrl = /^\\w+:/.test(location) ? this._currentUrl :\n    url.format(Object.assign(currentUrlParts, { host: currentHost }));\n\n  // Create the redirected request\n  var redirectUrl = resolveUrl(location, currentUrl);\n  debug(\"redirecting to\", redirectUrl.href);\n  this._isRedirect = true;\n  spreadUrlObject(redirectUrl, this._options);\n\n  // Drop confidential headers when redirecting to a less secure protocol\n  // or to a different domain that is not a superdomain\n  if (redirectUrl.protocol !== currentUrlParts.protocol &&\n     redirectUrl.protocol !== \"https:\" ||\n     redirectUrl.host !== currentHost &&\n     !isSubdomain(redirectUrl.host, currentHost)) {\n    removeMatchingHeaders(/^(?:(?:proxy-)?authorization|cookie)$/i, this._options.headers);\n  }\n\n  // Evaluate the beforeRedirect callback\n  if (isFunction(beforeRedirect)) {\n    var responseDetails = {\n      headers: response.headers,\n      statusCode: statusCode,\n    };\n    var requestDetails = {\n      url: currentUrl,\n      method: method,\n      headers: requestHeaders,\n    };\n    beforeRedirect(this._options, responseDetails, requestDetails);\n    this._sanitizeOptions(this._options);\n  }\n\n  // Perform the redirected request\n  this._performRequest();\n};\n\n// Wraps the key/value object of protocols with redirect functionality\nfunction wrap(protocols) {\n  // Default settings\n  var exports = {\n    maxRedirects: 21,\n    maxBodyLength: 10 * 1024 * 1024,\n  };\n\n  // Wrap each protocol\n  var nativeProtocols = {};\n  Object.keys(protocols).forEach(function (scheme) {\n    var protocol = scheme + \":\";\n    var nativeProtocol = nativeProtocols[protocol] = protocols[scheme];\n    var wrappedProtocol = exports[scheme] = Object.create(nativeProtocol);\n\n    // Executes a request, following redirects\n    function request(input, options, callback) {\n      // Parse parameters, ensuring that input is an object\n      if (isURL(input)) {\n        input = spreadUrlObject(input);\n      }\n      else if (isString(input)) {\n        input = spreadUrlObject(parseUrl(input));\n      }\n      else {\n        callback = options;\n        options = validateUrl(input);\n        input = { protocol: protocol };\n      }\n      if (isFunction(options)) {\n        callback = options;\n        options = null;\n      }\n\n      // Set defaults\n      options = Object.assign({\n        maxRedirects: exports.maxRedirects,\n        maxBodyLength: exports.maxBodyLength,\n      }, input, options);\n      options.nativeProtocols = nativeProtocols;\n      if (!isString(options.host) && !isString(options.hostname)) {\n        options.hostname = \"::1\";\n      }\n\n      assert.equal(options.protocol, protocol, \"protocol mismatch\");\n      debug(\"options\", options);\n      return new RedirectableRequest(options, callback);\n    }\n\n    // Executes a GET request, following redirects\n    function get(input, options, callback) {\n      var wrappedRequest = wrappedProtocol.request(input, options, callback);\n      wrappedRequest.end();\n      return wrappedRequest;\n    }\n\n    // Expose the properties on the wrapped protocol\n    Object.defineProperties(wrappedProtocol, {\n      request: { value: request, configurable: true, enumerable: true, writable: true },\n      get: { value: get, configurable: true, enumerable: true, writable: true },\n    });\n  });\n  return exports;\n}\n\nfunction noop() { /* empty */ }\n\nfunction parseUrl(input) {\n  var parsed;\n  // istanbul ignore else\n  if (useNativeURL) {\n    parsed = new URL(input);\n  }\n  else {\n    // Ensure the URL is valid and absolute\n    parsed = validateUrl(url.parse(input));\n    if (!isString(parsed.protocol)) {\n      throw new InvalidUrlError({ input });\n    }\n  }\n  return parsed;\n}\n\nfunction resolveUrl(relative, base) {\n  // istanbul ignore next\n  return useNativeURL ? new URL(relative, base) : parseUrl(url.resolve(base, relative));\n}\n\nfunction validateUrl(input) {\n  if (/^\\[/.test(input.hostname) && !/^\\[[:0-9a-f]+\\]$/i.test(input.hostname)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  if (/^\\[/.test(input.host) && !/^\\[[:0-9a-f]+\\](:\\d+)?$/i.test(input.host)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  return input;\n}\n\nfunction spreadUrlObject(urlObject, target) {\n  var spread = target || {};\n  for (var key of preservedUrlFields) {\n    spread[key] = urlObject[key];\n  }\n\n  // Fix IPv6 hostname\n  if (spread.hostname.startsWith(\"[\")) {\n    spread.hostname = spread.hostname.slice(1, -1);\n  }\n  // Ensure port is a number\n  if (spread.port !== \"\") {\n    spread.port = Number(spread.port);\n  }\n  // Concatenate path\n  spread.path = spread.search ? spread.pathname + spread.search : spread.pathname;\n\n  return spread;\n}\n\nfunction removeMatchingHeaders(regex, headers) {\n  var lastValue;\n  for (var header in headers) {\n    if (regex.test(header)) {\n      lastValue = headers[header];\n      delete headers[header];\n    }\n  }\n  return (lastValue === null || typeof lastValue === \"undefined\") ?\n    undefined : String(lastValue).trim();\n}\n\nfunction createErrorType(code, message, baseClass) {\n  // Create constructor\n  function CustomError(properties) {\n    // istanbul ignore else\n    if (isFunction(Error.captureStackTrace)) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    Object.assign(this, properties || {});\n    this.code = code;\n    this.message = this.cause ? message + \": \" + this.cause.message : message;\n  }\n\n  // Attach constructor and set default properties\n  CustomError.prototype = new (baseClass || Error)();\n  Object.defineProperties(CustomError.prototype, {\n    constructor: {\n      value: CustomError,\n      enumerable: false,\n    },\n    name: {\n      value: \"Error [\" + code + \"]\",\n      enumerable: false,\n    },\n  });\n  return CustomError;\n}\n\nfunction destroyRequest(request, error) {\n  for (var event of events) {\n    request.removeListener(event, eventHandlers[event]);\n  }\n  request.on(\"error\", noop);\n  request.destroy(error);\n}\n\nfunction isSubdomain(subdomain, domain) {\n  assert(isString(subdomain) && isString(domain));\n  var dot = subdomain.length - domain.length - 1;\n  return dot > 0 && subdomain[dot] === \".\" && subdomain.endsWith(domain);\n}\n\nfunction isString(value) {\n  return typeof value === \"string\" || value instanceof String;\n}\n\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\nfunction isBuffer(value) {\n  return typeof value === \"object\" && (\"length\" in value);\n}\n\nfunction isURL(value) {\n  return URL && value instanceof URL;\n}\n\n// Exports\nmodule.exports = wrap({ http: http, https: https });\nmodule.exports.wrap = wrap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/follow-redirects/index.js\n");

/***/ })

};
;