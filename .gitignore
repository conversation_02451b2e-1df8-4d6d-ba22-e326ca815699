# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.local
.env.*.local

# Redis
dump.rdb

# Celery
celerybeat-schedule
celerybeat.pid

# Coverage
.coverage
htmlcov/
.pytest_cache/

# OS
.DS_Store
Thumbs.db

*.sqlite3
.qodo/

llm_api/ntlk_data/