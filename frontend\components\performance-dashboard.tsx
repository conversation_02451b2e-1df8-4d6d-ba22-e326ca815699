"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts"
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Award,
  Calendar,
  BookOpen
} from "lucide-react"
import { performanceApi } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import { formatTimeFromSeconds, generatePerformanceInsights } from "@/lib/quiz-utils"

interface Performance {
  id: number
  student: number
  document: number
  quiz_score: number
  time_taken: number
  remarks: string
  created_at: string
  document_title: string
  student_name: string
}

interface PerformanceDashboardProps {
  documentId?: number
  studentId?: number
}

export function PerformanceDashboard({ documentId, studentId }: PerformanceDashboardProps) {
  const [performances, setPerformances] = useState<Performance[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    averageScore: 0,
    averageTime: 0,
    totalAttempts: 0,
    bestScore: 0,
    improvement: 0
  })

  useEffect(() => {
    fetchPerformances()
  }, [documentId, studentId])

  const fetchPerformances = async () => {
    setLoading(true)
    try {
      let data: Performance[]
      if (studentId) {
        const response = await performanceApi.getStudentPerformances(studentId)
        data = response.results || response
      } else if (documentId) {
        const response = await performanceApi.getPerformanceStats(documentId)
        data = response.results || response
      } else {
        const response = await performanceApi.getPerformances()
        data = response.results || response
      }
      
      setPerformances(data)
      calculateStats(data)
    } catch (error) {
      console.error('Error fetching performances:', error)
      toast({
        title: "Error",
        description: "Failed to load performance data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (data: Performance[]) => {
    if (data.length === 0) {
      setStats({
        averageScore: 0,
        averageTime: 0,
        totalAttempts: 0,
        bestScore: 0,
        improvement: 0
      })
      return
    }

    const totalScore = data.reduce((sum, p) => sum + p.quiz_score, 0)
    const totalTime = data.reduce((sum, p) => sum + p.time_taken, 0)
    const averageScore = totalScore / data.length
    const averageTime = totalTime / data.length
    const bestScore = Math.max(...data.map(p => p.quiz_score))
    
    // Calculate improvement (compare first and last attempts)
    const sortedByDate = [...data].sort((a, b) => 
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    )
    const improvement = sortedByDate.length > 1 
      ? sortedByDate[sortedByDate.length - 1].quiz_score - sortedByDate[0].quiz_score
      : 0

    setStats({
      averageScore: Math.round(averageScore * 100) / 100,
      averageTime: Math.round(averageTime),
      totalAttempts: data.length,
      bestScore: Math.round(bestScore * 100) / 100,
      improvement: Math.round(improvement * 100) / 100
    })
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "destructive"
  }

  // Prepare chart data
  const chartData = performances
    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    .map((p, index) => ({
      attempt: index + 1,
      score: p.quiz_score,
      time: Math.round(p.time_taken / 60), // Convert to minutes
      date: formatDate(p.created_at)
    }))

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Performance Dashboard</h2>
        <Button onClick={fetchPerformances} variant="outline" size="sm">
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Average Score</p>
                <p className="text-2xl font-bold">{stats.averageScore}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Avg Time</p>
                <p className="text-2xl font-bold">{formatTimeFromSeconds(stats.averageTime)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-sm font-medium">Best Score</p>
                <p className="text-2xl font-bold">{stats.bestScore}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Improvement</p>
                <p className={`text-2xl font-bold ${stats.improvement >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.improvement > 0 ? '+' : ''}{stats.improvement}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {chartData.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Score Progress</CardTitle>
                <CardDescription>Your quiz scores over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="attempt" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'score' ? `${value}%` : `${value}m`,
                        name === 'score' ? 'Score' : 'Time'
                      ]}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="score" 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quiz History</CardTitle>
              <CardDescription>All your quiz attempts</CardDescription>
            </CardHeader>
            <CardContent>
              {performances.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No quiz attempts yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {performances.map((performance) => (
                    <div key={performance.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">{performance.document_title}</p>
                          <p className="text-sm text-gray-500">{formatDate(performance.created_at)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge variant={getScoreBadgeVariant(performance.quiz_score)}>
                          {Math.round(performance.quiz_score)}%
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {formatTimeFromSeconds(performance.time_taken)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {chartData.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Score Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="attempt" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => [`${value}%`, 'Score']} />
                      <Bar dataKey="score" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Time Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={250}>
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="attempt" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}m`, 'Time']} />
                      <Line type="monotone" dataKey="time" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
