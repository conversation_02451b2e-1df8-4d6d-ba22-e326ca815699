'use client'

import { useEffect, useRef, useCallback, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'

interface UseOptimizedTimerProps {
  fileName?: string
  enabled?: boolean
  isProcessingComplete?: boolean
  heartbeatInterval?: number // in milliseconds, default 60000 (1 minute)
  batchUpdateInterval?: number // in milliseconds, default 300000 (5 minutes)
}

interface TimerSession {
  fileName: string
  startTime: Date
  accumulatedTime: number
  isActive: boolean
}

export function useOptimizedTimer({
  fileName,
  enabled = true,
  isProcessingComplete = false,
  heartbeatInterval = 60000, // 1 minute
  batchUpdateInterval = 300000 // 5 minutes
}: UseOptimizedTimerProps = {}) {
  const [isTracking, setIsTracking] = useState(false)
  const [totalTime, setTotalTime] = useState(0)
  
  const sessionRef = useRef<TimerSession | null>(null)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const batchUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const pendingUpdatesRef = useRef<Array<{fileName: string, timeSeconds: number}>>([])
  
  const router = useRouter()
  const pathname = usePathname()

  // Get the correct API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`,
    }
  }

  // Send heartbeat to keep session alive
  const sendHeartbeat = useCallback(async () => {
    if (!sessionRef.current?.isActive || !sessionRef.current.fileName) return

    try {
      const response = await fetch(`${API_BASE_URL}/users/timer/heartbeat/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ file_name: sessionRef.current.fileName }),
      })

      if (!response.ok) {
        console.warn('Heartbeat failed:', response.status)
      }
    } catch (error) {
      console.warn('Heartbeat error:', error)
    }
  }, [API_BASE_URL])

  // Batch update multiple sessions for efficiency
  const batchUpdateSessions = useCallback(async () => {
    if (pendingUpdatesRef.current.length === 0) return

    try {
      const sessions = [...pendingUpdatesRef.current]
      pendingUpdatesRef.current = [] // Clear pending updates

      const response = await fetch(`${API_BASE_URL}/users/timer/batch-update/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ sessions }),
      })

      if (response.ok) {
        console.log(`Batch updated ${sessions.length} timer sessions`)
      } else {
        console.error('Batch update failed:', response.status)
        // Re-add failed updates to pending list
        pendingUpdatesRef.current.push(...sessions)
      }
    } catch (error) {
      console.error('Batch update error:', error)
    }
  }, [API_BASE_URL])

  // Add session time to pending updates
  const addToPendingUpdates = useCallback((fileName: string, timeSeconds: number) => {
    if (timeSeconds <= 0) return

    // Check if there's already a pending update for this file
    const existingIndex = pendingUpdatesRef.current.findIndex(update => update.fileName === fileName)
    
    if (existingIndex >= 0) {
      // Add to existing pending update
      pendingUpdatesRef.current[existingIndex].timeSeconds += timeSeconds
    } else {
      // Create new pending update
      pendingUpdatesRef.current.push({
        fileName,
        timeSeconds
      })
    }
  }, [])

  // Start timer session
  const startTimer = useCallback(async () => {
    if (!enabled || !fileName || !isProcessingComplete || isTracking) {
      return
    }

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        console.log('No token found, cannot start timer')
        return
      }

      console.log('Starting optimized timer session for file:', fileName)

      const response = await fetch(`${API_BASE_URL}/users/timer/start/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ file_name: fileName }),
      })

      if (response.ok) {
        const data = await response.json()
        
        sessionRef.current = {
          fileName,
          startTime: new Date(),
          accumulatedTime: 0,
          isActive: true
        }
        
        setIsTracking(true)
        console.log('Optimized timer started successfully:', data.message)

        // Start heartbeat interval
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current)
        }
        heartbeatIntervalRef.current = setInterval(sendHeartbeat, heartbeatInterval)

        // Start batch update interval
        if (batchUpdateIntervalRef.current) {
          clearInterval(batchUpdateIntervalRef.current)
        }
        batchUpdateIntervalRef.current = setInterval(batchUpdateSessions, batchUpdateInterval)

      } else {
        console.error('Failed to start timer:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error starting timer:', error)
    }
  }, [fileName, enabled, isProcessingComplete, isTracking, API_BASE_URL, sendHeartbeat, batchUpdateSessions, heartbeatInterval, batchUpdateInterval])

  // Stop timer session
  const stopTimer = useCallback(async (immediate = false) => {
    if (!sessionRef.current?.isActive) return

    try {
      const session = sessionRef.current
      const sessionEndTime = new Date()
      const sessionTimeSeconds = Math.floor((sessionEndTime.getTime() - session.startTime.getTime()) / 1000)

      console.log('Stopping optimized timer for file:', session.fileName, 'Session time:', sessionTimeSeconds, 'seconds')

      // Clear intervals
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }
      if (batchUpdateIntervalRef.current) {
        clearInterval(batchUpdateIntervalRef.current)
        batchUpdateIntervalRef.current = null
      }

      if (immediate) {
        // Send immediate update for critical stops (page unload, etc.)
        const response = await fetch(`${API_BASE_URL}/users/timer/stop/`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify({
            file_name: session.fileName,
            session_time_seconds: sessionTimeSeconds
          }),
        })

        if (response.ok) {
          const data = await response.json()
          setTotalTime(data.total_time_seconds)
          console.log('Timer stopped immediately:', data.message)
        }
      } else {
        // Add to pending updates for batch processing
        addToPendingUpdates(session.fileName, sessionTimeSeconds)
        
        // Process any remaining pending updates
        await batchUpdateSessions()
      }

      // Reset state
      sessionRef.current = null
      setIsTracking(false)

    } catch (error) {
      console.error('Error stopping timer:', error)
      // Reset state even on error
      sessionRef.current = null
      setIsTracking(false)
    }
  }, [API_BASE_URL, addToPendingUpdates, batchUpdateSessions])

  // Pause timer (for quizzes)
  const pauseTimer = useCallback(() => {
    if (!sessionRef.current?.isActive) return

    const now = new Date()
    const sessionTime = Math.floor((now.getTime() - sessionRef.current.startTime.getTime()) / 1000)
    
    // Accumulate time and mark as inactive
    sessionRef.current.accumulatedTime += sessionTime
    sessionRef.current.isActive = false
    
    console.log('Timer paused, accumulated time:', sessionRef.current.accumulatedTime)
  }, [])

  // Resume timer (after quizzes)
  const resumeTimer = useCallback(() => {
    if (!sessionRef.current || sessionRef.current.isActive) return

    // Reset start time and mark as active
    sessionRef.current.startTime = new Date()
    sessionRef.current.isActive = true
    
    console.log('Timer resumed')
  }, [])

  // Increment quiz count
  const incrementQuizCount = useCallback(async () => {
    if (!fileName) return

    try {
      const response = await fetch(`${API_BASE_URL}/users/timer/quiz/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ file_name: fileName }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Quiz count incremented:', data.number_of_quizzes)
      }
    } catch (error) {
      console.error('Error incrementing quiz count:', error)
    }
  }, [fileName, API_BASE_URL])

  // Auto-start timer when conditions are met
  useEffect(() => {
    if (!enabled || !fileName || !isProcessingComplete || !pathname.includes('/process')) {
      return
    }

    startTimer()

    // Handle page unload - stop timer immediately
    const handleBeforeUnload = () => {
      if (sessionRef.current?.isActive) {
        // Use navigator.sendBeacon for reliable data sending during page unload
        const sessionTimeSeconds = Math.floor((new Date().getTime() - sessionRef.current.startTime.getTime()) / 1000)
        const data = JSON.stringify({
          file_name: sessionRef.current.fileName,
          session_time_seconds: sessionTimeSeconds
        })
        
        const token = localStorage.getItem('token')
        if (token) {
          navigator.sendBeacon(
            `${API_BASE_URL}/users/timer/stop/`,
            new Blob([data], { type: 'application/json' })
          )
        }
      }
    }

    // Handle visibility change
    const handleVisibilityChange = () => {
      if (document.hidden) {
        pauseTimer()
      } else if (enabled && fileName && isProcessingComplete && pathname.includes('/process')) {
        resumeTimer()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [fileName, enabled, isProcessingComplete, pathname, startTimer, pauseTimer, resumeTimer, API_BASE_URL])

  // Stop timer when navigating away from process page
  useEffect(() => {
    if (!pathname.includes('/process') && isTracking) {
      console.log('Left process page, stopping timer:', pathname)
      stopTimer(true) // Immediate stop for navigation
    }
  }, [pathname, isTracking, stopTimer])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
      }
      if (batchUpdateIntervalRef.current) {
        clearInterval(batchUpdateIntervalRef.current)
      }
      if (sessionRef.current?.isActive) {
        stopTimer(true)
      }
    }
  }, [stopTimer])

  return {
    isTracking,
    totalTime,
    sessionStart: sessionRef.current?.startTime || null,
    stopTimer: () => stopTimer(false),
    pauseTimer,
    resumeTimer,
    incrementQuizCount,
  }
}
