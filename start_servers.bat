@echo off
echo Activating virtual environment and starting servers...

echo Starting Django server on port 8000...
start cmd /k "call venv\Scripts\activate && cd core && python manage.py runserver"

echo Starting FastAPI server on port 8001...
start cmd /k "call venv\Scripts\activate && cd llm_api && uvicorn main:app --reload --host 0.0.0.0 --port 8001"

echo Both servers are starting. You can now run the frontend with 'npm run dev' in the frontend directory.
