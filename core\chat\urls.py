from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import ChatSessionViewSet, ChatMessageViewSet, send_message_view, get_chat_history_view

router = DefaultRouter()
router.register(r'sessions', ChatSessionViewSet, basename='chat-session')
router.register(r'sessions/(?P<session_id>\d+)/messages', ChatMessageViewSet, basename='chat-message')

urlpatterns = [
    path('', include(router.urls)),
    # Direct endpoints for frontend compatibility
    path('message/', send_message_view, name='chat-message'),
    path('history/', get_chat_history_view, name='chat-history'),
]