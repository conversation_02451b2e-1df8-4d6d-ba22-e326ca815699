# Generated by Django 4.2.20 on 2025-04-17 09:12

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('chat', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='chatmessage',
            options={'ordering': ['created_at']},
        ),
        migrations.AlterModelOptions(
            name='chatsession',
            options={'ordering': ['-updated_at']},
        ),
        migrations.RenameField(
            model_name='chatmessage',
            old_name='message',
            new_name='content',
        ),
        migrations.RenameField(
            model_name='chatmessage',
            old_name='timestamp',
            new_name='created_at',
        ),
        migrations.RemoveField(
            model_name='chatmessage',
            name='is_user',
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='model',
            field=models.CharField(choices=[('openai', 'OpenAI'), ('gemini', 'Gemini')], default=django.utils.timezone.now, max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='role',
            field=models.CharField(choices=[('user', 'User'), ('assistant', 'Assistant'), ('system', 'System')], default=django.utils.timezone.now, max_length=10),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='tokens',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='title',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='chatsession',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddIndex(
            model_name='chatmessage',
            index=models.Index(fields=['session', 'created_at'], name='chat_chatme_session_70d41b_idx'),
        ),
        migrations.AddIndex(
            model_name='chatmessage',
            index=models.Index(fields=['role'], name='chat_chatme_role_e8d1af_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['user', 'created_at'], name='chat_chatse_user_id_1ec383_idx'),
        ),
        migrations.AddIndex(
            model_name='chatsession',
            index=models.Index(fields=['is_active'], name='chat_chatse_is_acti_e54bd5_idx'),
        ),
    ]
