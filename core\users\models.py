from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
import pytz

class Student(AbstractUser):
    is_paid = models.BooleanField(default=False)

    email = models.EmailField(unique=True)
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.CharField(max_length=100, null=True, blank=True)
    email_verification_sent_at = models.DateTimeField(null=True, blank=True)
    otp = models.CharField(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = 'email'  # Login using email instead of username
    REQUIRED_FIELDS = ['username']  # username is still required in form

    groups = models.ManyToManyField(
        Group,
        related_name='student_users',
        blank=True,
        help_text='The groups this user belongs to.',
        verbose_name='groups'
    )
    user_permissions = models.ManyToManyField(
        Permission,
        related_name='student_users',
        blank=True,
        help_text='Specific permissions for this user.',
        verbose_name='user permissions'
    )

    def __str__(self):
        return self.email

    def generate_verification_token(self):
        import secrets
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = timezone.now()
        self.save()
        return self.email_verification_token

    def generate_otp(self):
        import random
        import string
        self.otp = ''.join(random.choices(string.digits, k=6))
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        if not self.otp or not self.otp_created_at:
            return False

        # Check if OTP is expired (10 minutes validity)
        if timezone.now() > self.otp_created_at + timezone.timedelta(minutes=10):
            return False

        return self.otp == otp

@receiver(post_save, sender=Student)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=Student)
def save_user_profile(sender, instance, **kwargs):
    if not hasattr(instance, 'userprofile'):
        UserProfile.objects.create(user=instance)
    instance.userprofile.save()

class UserProfile(models.Model):
    user = models.OneToOneField('Student', on_delete=models.CASCADE)
    is_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {'Paid' if self.is_paid else 'Free'}"

class UserUsage(models.Model):
    user = models.ForeignKey('Student', on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    chat_count = models.IntegerField(default=0)
    file_upload_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'date')
        indexes = [
            models.Index(fields=['user', 'date']),
            models.Index(fields=['date']),  # For efficient cleanup
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date} - Chats: {self.chat_count}, Files: {self.file_upload_count}"

    @property
    def chat_limit(self):
        return 100 if self.user.userprofile.is_paid else 5000

    @property
    def file_upload_limit(self):
        return 5 if self.user.userprofile.is_paid else 100

    def can_make_chat(self):
        return self.chat_count < self.chat_limit

    def can_upload_file(self):
        return self.file_upload_count < self.file_upload_limit

    def increment_chat_count(self):
        self.chat_count = self.chat_count + 1
        UserUsage.objects.filter(id=self.id).update(chat_count=self.chat_count)
        self._update_cache()

    def increment_file_upload_count(self):
        self.file_upload_count = self.file_upload_count + 1
        UserUsage.objects.filter(id=self.id).update(file_upload_count=self.file_upload_count)
        self._update_cache()

    def _update_cache(self):
        """Update cache with current usage data"""
        cache_key = f"user_usage_{self.user.id}_{self.date}"
        cache.set(cache_key, {
            'chat_count': self.chat_count,
            'file_upload_count': self.file_upload_count,
            'chat_limit': self.chat_limit,
            'file_upload_limit': self.file_upload_limit
        }, timeout=86400)  # Cache for 24 hours

    @classmethod
    def get_or_create_usage(cls, user, date=None):
        """Get or create usage record with caching"""
        if date is None:
            date = timezone.now().date()

        cache_key = f"user_usage_{user.id}_{date}"
        cached_data = cache.get(cache_key)

        if cached_data:
            usage, _ = cls.objects.get_or_create(
                user=user,
                date=date,
                defaults={
                    'chat_count': cached_data['chat_count'],
                    'file_upload_count': cached_data['file_upload_count']
                }
            )
            return usage

        usage, created = cls.objects.get_or_create(
            user=user,
            date=date,
            defaults={'chat_count': 0, 'file_upload_count': 0}
        )

        if created:
            usage._update_cache()

        return usage

    @classmethod
    def cleanup_old_records(cls, days_to_keep=30):
        """Efficient cleanup of old records using bulk operations"""
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)

        # Get IDs of records to delete
        old_record_ids = list(cls.objects.filter(date__lt=cutoff_date).values_list('id', flat=True))

        if old_record_ids:
            # Delete in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, len(old_record_ids), chunk_size):
                chunk = old_record_ids[i:i + chunk_size]
                cls.objects.filter(id__in=chunk).delete()

        return len(old_record_ids)


class StudentPerformance(models.Model):
    """
    Model to track student performance on quizzes for specific documents.
    Each entry represents a single quiz attempt with score and time taken.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='performances')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='student_performances')
    quiz_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                    help_text="Score achieved on the quiz (percentage)")
    time_taken = models.PositiveIntegerField(help_text="Time taken to complete the quiz (in seconds)")
    remarks = models.TextField(blank=True, null=True, help_text="Feedback or comments on student performance")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['quiz_score']),  # For performance analytics
            models.Index(fields=['created_at']),  # For chronological ordering
        ]
        ordering = ['-created_at']
        verbose_name = "Student Performance"
        verbose_name_plural = "Student Performances"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - Score: {self.quiz_score}% - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class DocumentTimeTracking(models.Model):
    """
    Optimized model to track total time spent by users on specific documents.
    Tracks cumulative time spent on the 2nd webpage (chat, flashcards, flowcharts, etc.)
    """
    username = models.CharField(max_length=150, help_text="Username of the student")
    file_name = models.CharField(max_length=255, help_text="Name of the document file")
    total_time_seconds = models.PositiveIntegerField(default=0, help_text="Total time spent on 2nd webpage in seconds")
    number_of_sessions = models.PositiveIntegerField(default=0, help_text="Number of times the 2nd webpage was accessed")
    number_of_quizzes = models.PositiveIntegerField(default=0, help_text="Number of quizzes taken for this file")
    last_accessed = models.DateTimeField(auto_now=True, help_text="Last time the 2nd webpage was accessed (Indian timezone)")
    created_at = models.DateTimeField(auto_now_add=True, help_text="When this record was first created")

    class Meta:
        unique_together = ('username', 'file_name')
        indexes = [
            models.Index(fields=['username', 'file_name']),
            models.Index(fields=['last_accessed']),
            models.Index(fields=['total_time_seconds']),
        ]
        ordering = ['-last_accessed']
        verbose_name = "Document Time Tracking"
        verbose_name_plural = "Document Time Tracking"

    def __str__(self):
        return f"{self.username} - {self.file_name} - {self.total_time_seconds}s"

    @property
    def total_time_formatted(self):
        """Return total time in human readable format"""
        hours = self.total_time_seconds // 3600
        minutes = (self.total_time_seconds % 3600) // 60
        seconds = self.total_time_seconds % 60

        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"

    @property
    def average_session_time(self):
        """Calculate average session time in seconds"""
        if self.number_of_sessions > 0:
            return self.total_time_seconds / self.number_of_sessions
        return 0

    @classmethod
    def get_indian_timezone(cls):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def add_session_time(self, time_seconds):
        """Add time from a completed session with optimized database update"""
        # Convert to int if it's a string
        try:
            time_seconds = int(time_seconds)
        except (ValueError, TypeError):
            time_seconds = 0

        if time_seconds <= 0:
            return

        # Use F expressions for atomic updates to prevent race conditions
        from django.db.models import F
        from django.utils import timezone as django_timezone

        # Update fields atomically
        self.__class__.objects.filter(id=self.id).update(
            total_time_seconds=F('total_time_seconds') + time_seconds,
            number_of_sessions=F('number_of_sessions') + 1,
            last_accessed=django_timezone.now().astimezone(self.get_indian_timezone())
        )

        # Refresh from database to get updated values
        self.refresh_from_db()

    def increment_quiz_count(self):
        """Increment quiz count when a quiz is taken with optimized database update"""
        from django.db.models import F

        self.__class__.objects.filter(id=self.id).update(
            number_of_quizzes=F('number_of_quizzes') + 1
        )

        # Refresh from database to get updated values
        self.refresh_from_db()

    @classmethod
    def get_or_create_tracking(cls, username, file_name):
        """Get or create tracking record for user and file with caching"""
        cache_key = f"doc_tracking_{username}_{file_name}"
        cached_tracking = cache.get(cache_key)

        if cached_tracking:
            # Update last_accessed in cache and database
            indian_tz = cls.get_indian_timezone()
            now = timezone.now().astimezone(indian_tz)

            cls.objects.filter(id=cached_tracking['id']).update(last_accessed=now)
            cached_tracking['last_accessed'] = now.isoformat()
            cache.set(cache_key, cached_tracking, timeout=3600)  # Cache for 1 hour

            # Return the cached object
            try:
                return cls.objects.get(id=cached_tracking['id'])
            except cls.DoesNotExist:
                # Cache is stale, remove it and continue with normal flow
                cache.delete(cache_key)

        indian_tz = cls.get_indian_timezone()
        now = timezone.now().astimezone(indian_tz)

        tracking, created = cls.objects.get_or_create(
            username=username,
            file_name=file_name,
            defaults={
                'last_accessed': now,
            }
        )

        if not created:
            tracking.last_accessed = now
            tracking.save(update_fields=['last_accessed'])

        # Cache the tracking record
        cache.set(cache_key, {
            'id': tracking.id,
            'username': tracking.username,
            'file_name': tracking.file_name,
            'total_time_seconds': tracking.total_time_seconds,
            'number_of_sessions': tracking.number_of_sessions,
            'number_of_quizzes': tracking.number_of_quizzes,
            'last_accessed': tracking.last_accessed.isoformat(),
        }, timeout=3600)  # Cache for 1 hour

        return tracking

    @classmethod
    def batch_update_sessions(cls, session_data_list):
        """
        Efficiently batch update multiple session records
        session_data_list: List of dicts with keys: username, file_name, time_seconds
        """
        if not session_data_list:
            return

        from django.db.models import F
        from django.utils import timezone as django_timezone

        # Group by tracking record to avoid multiple updates to same record
        tracking_updates = {}
        for session_data in session_data_list:
            key = (session_data['username'], session_data['file_name'])
            if key not in tracking_updates:
                tracking_updates[key] = 0

            # Convert time_seconds to int if it's a string
            try:
                time_seconds = int(session_data['time_seconds'])
            except (ValueError, TypeError):
                time_seconds = 0

            tracking_updates[key] += time_seconds

        # Perform batch updates
        for (username, file_name), total_time in tracking_updates.items():
            if total_time > 0:
                cls.objects.filter(
                    username=username,
                    file_name=file_name
                ).update(
                    total_time_seconds=F('total_time_seconds') + total_time,
                    number_of_sessions=F('number_of_sessions') + 1,
                    last_accessed=django_timezone.now().astimezone(cls.get_indian_timezone())
                )

                # Clear cache for updated records
                from django.core.cache import cache
                cache_key = f"doc_tracking_{username}_{file_name}"
                cache.delete(cache_key)


class CMSScore(models.Model):
    """
    Model to store CMS (Concept Mastery System) scores for adaptive learning.
    Calculates scores based on quiz performance and time efficiency.
    """

    TIME_CATEGORY_CHOICES = [
        ('high', 'High - More time than average'),
        ('average', 'Average - Around average time'),
        ('low', 'Low - Less time than average'),
    ]

    QUIZ_PERFORMANCE_CHOICES = [
        ('excellent', 'Quiz ≥ 70%'),
        ('moderate', 'Quiz 40-69%'),
        ('poor', 'Quiz < 40%'),
    ]

    USER_ZONE_CHOICES = [
        ('mastered', 'Mastered (90-100)'),
        ('stable', 'Stable (75-89)'),
        ('moderate_gap', 'Moderate Gap (60-74)'),
        ('weak', 'Weak (40-59)'),
        ('unclear', 'Unclear (< 40)'),
    ]

    # Foreign key relationships
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='cms_scores')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='cms_scores')
    quiz_attempt = models.OneToOneField('documents.QuizAttempt', on_delete=models.CASCADE, related_name='cms_score')

    # Quiz performance component (70% of total score)
    quiz_score_percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        help_text="Original quiz score as percentage (0-100)"
    )
    quiz_score_component = models.DecimalField(
        max_digits=5, decimal_places=2,
        help_text="Quiz score scaled to 70 points (0-70)"
    )
    quiz_performance_category = models.CharField(
        max_length=20, choices=QUIZ_PERFORMANCE_CHOICES,
        help_text="Quiz performance category based on score"
    )

    # Time efficiency component (30% of total score)
    time_spent_seconds = models.PositiveIntegerField(
        help_text="Total time spent learning this document"
    )
    average_time_seconds = models.PositiveIntegerField(
        help_text="Predicted average learning time for this document"
    )
    time_ratio = models.DecimalField(
        max_digits=5, decimal_places=2,
        help_text="Ratio of time spent to average time (time_spent/average_time)"
    )
    time_category = models.CharField(
        max_length=20, choices=TIME_CATEGORY_CHOICES,
        help_text="Time category based on ratio to average"
    )
    time_component_points = models.IntegerField(
        help_text="Points awarded for time efficiency (-10 to +30)"
    )

    # Final CMS score
    cms_score = models.DecimalField(
        max_digits=5, decimal_places=2,
        help_text="Final CMS score out of 100"
    )
    user_zone = models.CharField(
        max_length=20, choices=USER_ZONE_CHOICES,
        help_text="User zone classification based on CMS score"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['cms_score']),
            models.Index(fields=['user_zone']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']
        verbose_name = "CMS Score"
        verbose_name_plural = "CMS Scores"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - CMS: {self.cms_score} ({self.user_zone})"

    @classmethod
    def calculate_time_category(cls, time_ratio):
        """
        Determine time category based on ratio of time spent to average time.

        Args:
            time_ratio (float): Ratio of time_spent / average_time

        Returns:
            str: Time category ('high', 'average', 'low')
        """
        if time_ratio >= 1.5:  # 150% or more of average time
            return 'high'
        elif time_ratio <= 0.7:  # 70% or less of average time
            return 'low'
        else:
            return 'average'

    @classmethod
    def calculate_quiz_performance_category(cls, quiz_score):
        """
        Determine quiz performance category based on score.

        Args:
            quiz_score (float): Quiz score as percentage (0-100)

        Returns:
            str: Performance category ('excellent', 'moderate', 'poor')
        """
        if quiz_score >= 70:
            return 'excellent'
        elif quiz_score >= 40:
            return 'moderate'
        else:
            return 'poor'

    @classmethod
    def calculate_time_component_points(cls, time_category, quiz_performance_category):
        """
        Calculate time component points based on the scoring matrix.

        Args:
            time_category (str): 'high', 'average', or 'low'
            quiz_performance_category (str): 'excellent', 'moderate', or 'poor'

        Returns:
            int: Points to add/subtract for time component (-10 to +30)
        """
        # Scoring matrix from the provided image
        scoring_matrix = {
            ('high', 'excellent'): 30,    # High time, Quiz ≥ 70%
            ('high', 'moderate'): 10,     # High time, Quiz 40-69%
            ('high', 'poor'): -10,        # High time, Quiz < 40%
            ('average', 'excellent'): 20, # Average time, Quiz ≥ 70%
            ('average', 'moderate'): 15,  # Average time, Quiz 40-69%
            ('average', 'poor'): 0,       # Average time, Quiz < 40%
            ('low', 'excellent'): 30,     # Low time, Quiz ≥ 70%
            ('low', 'moderate'): 0,       # Low time, Quiz 40-69%
            ('low', 'poor'): -10,         # Low time, Quiz < 40%
        }

        return scoring_matrix.get((time_category, quiz_performance_category), 0)

    @classmethod
    def calculate_user_zone(cls, cms_score):
        """
        Determine user zone based on CMS score.

        Args:
            cms_score (float): CMS score out of 100

        Returns:
            str: User zone classification
        """
        if cms_score >= 90:
            return 'mastered'
        elif cms_score >= 75:
            return 'stable'
        elif cms_score >= 60:
            return 'moderate_gap'
        elif cms_score >= 40:
            return 'weak'
        else:
            return 'unclear'

    @classmethod
    def create_cms_score(cls, quiz_attempt, time_spent_seconds, average_time_seconds):
        """
        Create a CMS score record for a quiz attempt.

        Args:
            quiz_attempt: QuizAttempt instance
            time_spent_seconds (int): Total time spent learning the document
            average_time_seconds (int): Predicted average learning time

        Returns:
            CMSScore: Created CMS score instance
        """
        # Calculate quiz score component (70% of total)
        quiz_score_percentage = float(quiz_attempt.score)
        quiz_score_component = (quiz_score_percentage / 100) * 70
        quiz_performance_category = cls.calculate_quiz_performance_category(quiz_score_percentage)

        # Calculate time efficiency component
        time_ratio = time_spent_seconds / average_time_seconds if average_time_seconds > 0 else 1.0
        time_category = cls.calculate_time_category(time_ratio)
        time_component_points = cls.calculate_time_component_points(time_category, quiz_performance_category)

        # Calculate final CMS score
        cms_score = quiz_score_component + time_component_points
        # Ensure CMS score is within 0-100 range
        cms_score = max(0, min(100, cms_score))

        # Determine user zone
        user_zone = cls.calculate_user_zone(cms_score)

        # Create and save the CMS score record
        cms_score_record = cls.objects.create(
            student=quiz_attempt.user,
            document=quiz_attempt.document,
            quiz_attempt=quiz_attempt,
            quiz_score_percentage=quiz_score_percentage,
            quiz_score_component=quiz_score_component,
            quiz_performance_category=quiz_performance_category,
            time_spent_seconds=time_spent_seconds,
            average_time_seconds=average_time_seconds,
            time_ratio=time_ratio,
            time_category=time_category,
            time_component_points=time_component_points,
            cms_score=cms_score,
            user_zone=user_zone
        )

        return cms_score_record









