"""
Test script for CMS (Concept Mastery System) functionality.
This script demonstrates how the CMS scoring system works.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from documents.models import Document, Quiz, QuizAttempt, DocumentLearningTime
from users.models import CMSScore, DocumentTimeTracking
from users.cms_utils import CMSCalculator, RecommendationEngine
from decimal import Decimal

User = get_user_model()


class CMSTestCase(TestCase):
    """Test cases for CMS scoring system."""
    
    def setUp(self):
        """Set up test data."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test document
        self.document = Document.objects.create(
            title='Test Document',
            content='Test content for learning',
            user=self.user
        )
        
        # Create document learning time prediction
        self.learning_time = DocumentLearningTime.objects.create(
            document=self.document,
            predicted_time_seconds=1800,  # 30 minutes
            topic_difficulty=3,
            content_length_words=500,
            concept_density=3
        )
        
        # Create time tracking record
        self.time_tracking = DocumentTimeTracking.objects.create(
            username=self.user.username,
            file_name=self.document.title,
            total_time_seconds=1200,  # 20 minutes (less than average)
            number_of_sessions=1,
            number_of_quizzes=0
        )
        
        # Create quiz questions
        self.quiz1 = Quiz.objects.create(
            document=self.document,
            question='What is the main topic?',
            answer='Learning'
        )
        
        self.quiz2 = Quiz.objects.create(
            document=self.document,
            question='What is the purpose?',
            answer='Testing'
        )
    
    def test_cms_calculation_excellent_performance_low_time(self):
        """Test CMS calculation for excellent quiz performance with low time."""
        # Create quiz attempt with high score
        quiz_attempt = QuizAttempt.objects.create(
            document=self.document,
            user=self.user,
            score=Decimal('85.0')  # 85% - excellent performance
        )
        
        # Calculate CMS score
        cms_data = CMSCalculator.calculate_cms_score(quiz_attempt)
        
        # Verify calculations
        self.assertEqual(cms_data['quiz_performance_category'], 'excellent')
        self.assertEqual(cms_data['time_category'], 'low')  # 20 min vs 30 min average
        self.assertEqual(cms_data['quiz_score_component'], 59.5)  # 85% * 0.7 = 59.5
        self.assertEqual(cms_data['time_component_points'], 30)  # Low time + excellent = +30
        self.assertEqual(cms_data['cms_score'], 89.5)  # 59.5 + 30 = 89.5
        self.assertEqual(cms_data['user_zone'], 'stable')  # 75-89 range
    
    def test_cms_calculation_moderate_performance_high_time(self):
        """Test CMS calculation for moderate quiz performance with high time."""
        # Update time tracking to high time
        self.time_tracking.total_time_seconds = 3000  # 50 minutes (high time)
        self.time_tracking.save()
        
        # Create quiz attempt with moderate score
        quiz_attempt = QuizAttempt.objects.create(
            document=self.document,
            user=self.user,
            score=Decimal('55.0')  # 55% - moderate performance
        )
        
        # Calculate CMS score
        cms_data = CMSCalculator.calculate_cms_score(quiz_attempt)
        
        # Verify calculations
        self.assertEqual(cms_data['quiz_performance_category'], 'moderate')
        self.assertEqual(cms_data['time_category'], 'high')  # 50 min vs 30 min average
        self.assertEqual(cms_data['quiz_score_component'], 38.5)  # 55% * 0.7 = 38.5
        self.assertEqual(cms_data['time_component_points'], 10)  # High time + moderate = +10
        self.assertEqual(cms_data['cms_score'], 48.5)  # 38.5 + 10 = 48.5
        self.assertEqual(cms_data['user_zone'], 'weak')  # 40-59 range
    
    def test_cms_calculation_poor_performance_low_time(self):
        """Test CMS calculation for poor quiz performance with low time."""
        # Create quiz attempt with low score
        quiz_attempt = QuizAttempt.objects.create(
            document=self.document,
            user=self.user,
            score=Decimal('25.0')  # 25% - poor performance
        )
        
        # Calculate CMS score
        cms_data = CMSCalculator.calculate_cms_score(quiz_attempt)
        
        # Verify calculations
        self.assertEqual(cms_data['quiz_performance_category'], 'poor')
        self.assertEqual(cms_data['time_category'], 'low')  # 20 min vs 30 min average
        self.assertEqual(cms_data['quiz_score_component'], 17.5)  # 25% * 0.7 = 17.5
        self.assertEqual(cms_data['time_component_points'], -10)  # Low time + poor = -10
        self.assertEqual(cms_data['cms_score'], 7.5)  # 17.5 - 10 = 7.5
        self.assertEqual(cms_data['user_zone'], 'unclear')  # < 40 range
    
    def test_recommendation_generation(self):
        """Test recommendation generation for different user zones."""
        # Test mastered zone
        cms_data_mastered = {'cms_score': 95, 'user_zone': 'mastered', 'quiz_performance_category': 'excellent', 'time_category': 'low'}
        recommendation = RecommendationEngine.generate_detailed_recommendation(cms_data_mastered)
        
        self.assertEqual(recommendation['user_zone'], 'mastered')
        self.assertIn("You've nailed it!", recommendation['recommendation']['message'])
        self.assertEqual(recommendation['recommendation']['icon'], '✅')
        
        # Test weak zone
        cms_data_weak = {'cms_score': 45, 'user_zone': 'weak', 'quiz_performance_category': 'moderate', 'time_category': 'high'}
        recommendation = RecommendationEngine.generate_detailed_recommendation(cms_data_weak)
        
        self.assertEqual(recommendation['user_zone'], 'weak')
        self.assertIn("strengthen your basics", recommendation['recommendation']['message'])
        self.assertEqual(recommendation['recommendation']['icon'], '🔴')
    
    def test_cms_score_model_creation(self):
        """Test CMS score model creation and relationships."""
        # Create quiz attempt
        quiz_attempt = QuizAttempt.objects.create(
            document=self.document,
            user=self.user,
            score=Decimal('75.0')
        )
        
        # Create CMS score using the model's create method
        cms_score = CMSScore.create_cms_score(
            quiz_attempt=quiz_attempt,
            time_spent_seconds=1200,
            average_time_seconds=1800
        )
        
        # Verify the CMS score was created correctly
        self.assertEqual(cms_score.student, self.user)
        self.assertEqual(cms_score.document, self.document)
        self.assertEqual(cms_score.quiz_attempt, quiz_attempt)
        self.assertEqual(cms_score.quiz_score_percentage, 75.0)
        self.assertEqual(cms_score.user_zone, 'stable')
        
        # Verify relationships
        self.assertEqual(quiz_attempt.cms_score, cms_score)
        self.assertIn(cms_score, self.user.cms_scores.all())
        self.assertIn(cms_score, self.document.cms_scores.all())


def run_cms_demo():
    """
    Demonstration function showing how CMS scoring works.
    This can be run manually to see the system in action.
    """
    print("=== CMS (Concept Mastery System) Demo ===\n")
    
    # Example scenarios
    scenarios = [
        {
            'name': 'Fast Learner - High Score',
            'quiz_score': 90,
            'time_spent': 1200,  # 20 minutes
            'average_time': 1800,  # 30 minutes
        },
        {
            'name': 'Thorough Learner - High Score',
            'quiz_score': 85,
            'time_spent': 3000,  # 50 minutes
            'average_time': 1800,  # 30 minutes
        },
        {
            'name': 'Struggling Student',
            'quiz_score': 35,
            'time_spent': 2700,  # 45 minutes
            'average_time': 1800,  # 30 minutes
        },
        {
            'name': 'Rushed Student',
            'quiz_score': 45,
            'time_spent': 900,   # 15 minutes
            'average_time': 1800,  # 30 minutes
        }
    ]
    
    for scenario in scenarios:
        print(f"--- {scenario['name']} ---")
        print(f"Quiz Score: {scenario['quiz_score']}%")
        print(f"Time Spent: {scenario['time_spent']/60:.1f} minutes")
        print(f"Average Time: {scenario['average_time']/60:.1f} minutes")
        
        # Calculate time ratio and categories
        time_ratio = scenario['time_spent'] / scenario['average_time']
        time_category = CMSCalculator.categorize_time_efficiency(time_ratio)
        quiz_category = CMSCalculator.categorize_quiz_performance(scenario['quiz_score'])
        
        # Calculate components
        quiz_component = (scenario['quiz_score'] / 100) * 70
        time_points = CMSCalculator.calculate_time_component_points(time_category, quiz_category)
        cms_score = quiz_component + time_points
        cms_score = max(0, min(100, cms_score))
        user_zone = CMSCalculator.determine_user_zone(cms_score)
        
        print(f"Time Category: {time_category} (ratio: {time_ratio:.2f})")
        print(f"Quiz Category: {quiz_category}")
        print(f"Quiz Component: {quiz_component:.1f}/70")
        print(f"Time Points: {time_points:+d}")
        print(f"CMS Score: {cms_score:.1f}/100")
        print(f"User Zone: {user_zone}")
        
        # Get recommendation
        recommendation = RecommendationEngine.get_recommendation(user_zone)
        print(f"Recommendation: {recommendation['icon']} {recommendation['message']}")
        print()


if __name__ == '__main__':
    # Run the demo
    run_cms_demo()
