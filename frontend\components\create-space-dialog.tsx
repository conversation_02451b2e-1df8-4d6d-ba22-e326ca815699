"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface CreateSpaceDialogProps {
  open: boolean
  setOpen: (open: boolean) => void
  onCreated?: () => void
}

export function CreateSpaceDialog({ open, setOpen, onCreated }: CreateSpaceDialogProps) {
  const [spaceName, setSpaceName] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleCreateSpace = async () => {
    if (!spaceName.trim()) return
    setLoading(true)
    setError(null)
    try {
      // Call backend to create group
      await import("@/lib/api").then(({ documentApi }) =>
        documentApi.createGroup({ name: spaceName })
      )
      setOpen(false)
      setSpaceName("")
      onCreated?.()
    } catch (err: any) {
      setError(err?.response?.data?.error || "Failed to create space")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md bg-background border-border">
        <DialogHeader>
          <DialogTitle>Create New Space</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Space Name</Label>
              <Input
                id="name"
                placeholder="Enter space name"
                value={spaceName}
                onChange={(e) => setSpaceName(e.target.value)}
                className="bg-background border-border"
                disabled={loading}
              />
            </div>
            {error && <div className="text-red-500 text-sm">{error}</div>}
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setOpen(false)} disabled={loading}>
                Cancel
              </Button>
              <Button
                className="bg-purple-600 hover:bg-purple-700"
                onClick={handleCreateSpace}
                disabled={!spaceName.trim() || loading}
              >
                {loading ? "Creating..." : "Create Space"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
